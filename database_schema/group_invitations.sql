-- Group Invitations Table Schema
-- This file contains the SQL commands to create the group_invitations table in Supabase

-- Create the group_invitations table
CREATE TABLE IF NOT EXISTS group_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    invited_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    invited_by_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    message TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT unique_group_user_invitation UNIQUE (group_id, invited_user_id, status),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    CONSTRAINT responded_at_check CHECK (
        (status = 'pending' AND responded_at IS NULL) OR 
        (status != 'pending' AND responded_at IS NOT NULL)
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_group_invitations_group_id ON group_invitations(group_id);
CREATE INDEX IF NOT EXISTS idx_group_invitations_invited_user_id ON group_invitations(invited_user_id);
CREATE INDEX IF NOT EXISTS idx_group_invitations_invited_by_user_id ON group_invitations(invited_by_user_id);
CREATE INDEX IF NOT EXISTS idx_group_invitations_status ON group_invitations(status);
CREATE INDEX IF NOT EXISTS idx_group_invitations_created_at ON group_invitations(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE group_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Policy: Users can view invitations they sent or received
CREATE POLICY "Users can view their invitations" ON group_invitations
    FOR SELECT USING (
        auth.uid() = invited_user_id OR 
        auth.uid() = invited_by_user_id OR
        auth.uid() IN (
            SELECT owner_id FROM groups WHERE id = group_id
        )
    );

-- Policy: Users can create invitations for groups they own
CREATE POLICY "Group owners can create invitations" ON group_invitations
    FOR INSERT WITH CHECK (
        auth.uid() = invited_by_user_id AND
        auth.uid() IN (
            SELECT owner_id FROM groups WHERE id = group_id
        )
    );

-- Policy: Group owners can update invitation status
CREATE POLICY "Group owners can update invitations" ON group_invitations
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT owner_id FROM groups WHERE id = group_id
        )
    );

-- Policy: Users can delete invitations they sent (before acceptance)
CREATE POLICY "Users can delete their pending invitations" ON group_invitations
    FOR DELETE USING (
        auth.uid() = invited_by_user_id AND 
        status = 'pending'
    );

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
    UPDATE group_invitations 
    SET status = 'expired', responded_at = NOW()
    WHERE status = 'pending' 
    AND created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run the expiration function daily
-- Note: This requires the pg_cron extension to be enabled
-- SELECT cron.schedule('expire-invitations', '0 0 * * *', 'SELECT expire_old_invitations();');

-- Function to prevent duplicate pending invitations
CREATE OR REPLACE FUNCTION check_duplicate_invitation()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if there's already a pending invitation for this user to this group
    IF EXISTS (
        SELECT 1 FROM group_invitations 
        WHERE group_id = NEW.group_id 
        AND invited_user_id = NEW.invited_user_id 
        AND status = 'pending'
        AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
    ) THEN
        RAISE EXCEPTION 'User already has a pending invitation to this group';
    END IF;
    
    -- Check if user is already a member of the group
    IF EXISTS (
        SELECT 1 FROM groups 
        WHERE id = NEW.group_id 
        AND NEW.invited_user_id = ANY(member_ids)
    ) THEN
        RAISE EXCEPTION 'User is already a member of this group';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for duplicate invitation check
CREATE TRIGGER check_duplicate_invitation_trigger
    BEFORE INSERT OR UPDATE ON group_invitations
    FOR EACH ROW
    EXECUTE FUNCTION check_duplicate_invitation();

-- Function to automatically add user to group when invitation is accepted
CREATE OR REPLACE FUNCTION handle_invitation_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- If invitation status changed to 'accepted'
    IF OLD.status = 'pending' AND NEW.status = 'accepted' THEN
        -- Add user to group members
        UPDATE groups 
        SET member_ids = array_append(member_ids, NEW.invited_user_id),
            updated_at = NOW()
        WHERE id = NEW.group_id 
        AND NOT (NEW.invited_user_id = ANY(member_ids));
        
        -- Set responded_at if not already set
        IF NEW.responded_at IS NULL THEN
            NEW.responded_at = NOW();
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for invitation acceptance
CREATE TRIGGER handle_invitation_acceptance_trigger
    BEFORE UPDATE ON group_invitations
    FOR EACH ROW
    EXECUTE FUNCTION handle_invitation_acceptance();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON group_invitations TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Comments for documentation
COMMENT ON TABLE group_invitations IS 'Stores group invitation data with full audit trail';
COMMENT ON COLUMN group_invitations.id IS 'Unique identifier for the invitation';
COMMENT ON COLUMN group_invitations.group_id IS 'Reference to the group being invited to';
COMMENT ON COLUMN group_invitations.invited_user_id IS 'User being invited';
COMMENT ON COLUMN group_invitations.invited_by_user_id IS 'User who sent the invitation';
COMMENT ON COLUMN group_invitations.message IS 'Optional invitation message';
COMMENT ON COLUMN group_invitations.status IS 'Current status: pending, accepted, declined, expired';
COMMENT ON COLUMN group_invitations.created_at IS 'When the invitation was created';
COMMENT ON COLUMN group_invitations.responded_at IS 'When the invitation was responded to';
