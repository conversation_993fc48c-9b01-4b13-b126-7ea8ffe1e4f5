//
//  TaskEntity.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Core Data Entity for Tasks
//

import Foundation
import CoreData

@objc(TaskEntity)
public class TaskEntity: NSManagedObject {
    
}

// MARK: - Core Data Properties
extension TaskEntity {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<TaskEntity> {
        return NSFetchRequest<TaskEntity>(entityName: "TaskEntity")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var title: String?
    @NSManaged public var taskDescription: String?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var priority: String?
    @NSManaged public var status: String?
    @NSManaged public var taskType: String?
    @NSManaged public var createdByUserId: UUID?
    @NSManaged public var groupId: UUID?
    @NSManaged public var dueDate: Date?
    @NSManaged public var completedAt: Date?
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var reminderTime: Date?
    @NSManaged public var isImportant: Bool
    @NSManaged public var difficulty: String?
    @NSManaged public var estimatedDuration: Double
    @NSManaged public var tags: [String]?
    @NSManaged public var subtasks: Data?
    @NSManaged public var attachments: Data?
    @NSManaged public var location: Data?
    @NSManaged public var category: Data?
    @NSManaged public var recurrence: Data?
    @NSManaged public var needsSync: Bool
    @NSManaged public var isTaskDeleted: Bool
    @NSManaged public var lastSyncedAt: Date?
}
