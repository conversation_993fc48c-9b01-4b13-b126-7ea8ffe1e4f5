//
//  CoreDataManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Core Data Stack Management
//

import Foundation
import CoreData
import SwiftUI

// MARK: - Core Data Manager
@MainActor
class CoreDataManager: ObservableObject {
    static let shared = CoreDataManager()
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Core Data Stack
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "TaskPlus")
        
        // Configure for better performance
        let description = container.persistentStoreDescriptions.first
        description?.shouldInferMappingModelAutomatically = true
        description?.shouldMigrateStoreAutomatically = true
        description?.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        description?.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        container.loadPersistentStores { _, error in
            if let error = error {
                print("❌ Core Data failed to load: \(error.localizedDescription)")
                fatalError("Core Data error: \(error)")
            } else {
                print("✅ Core Data loaded successfully")
            }
        }
        
        // Configure view context
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        return container
    }()
    
    // MARK: - Contexts
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    var backgroundContext: NSManagedObjectContext {
        return persistentContainer.newBackgroundContext()
    }
    
    // MARK: - Initialization
    private init() {
        print("🚀 CoreDataManager initialized")
        setupNotifications()
    }
    
    // MARK: - Save Operations
    func save() async throws {
        let context = viewContext
        
        guard context.hasChanges else {
            print("📝 No changes to save")
            return
        }
        
        do {
            try context.save()
            print("✅ Core Data saved successfully")
        } catch {
            print("❌ Core Data save failed: \(error.localizedDescription)")
            await setError("Failed to save data: \(error.localizedDescription)")
            throw error
        }
    }
    
    func saveBackground(_ context: NSManagedObjectContext) async throws {
        guard context.hasChanges else { return }
        
        try await context.perform {
            do {
                try context.save()
                print("✅ Background context saved successfully")
            } catch {
                print("❌ Background save failed: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    // MARK: - Fetch Operations
    func fetch<T: NSManagedObject>(_ request: NSFetchRequest<T>) async throws -> [T] {
        let context = viewContext
        
        do {
            let results = try context.fetch(request)
            print("📱 Fetched \(results.count) \(T.self) objects")
            return results
        } catch {
            print("❌ Fetch failed: \(error.localizedDescription)")
            await setError("Failed to fetch data: \(error.localizedDescription)")
            throw error
        }
    }
    
    func fetchBackground<T: NSManagedObject>(_ request: NSFetchRequest<T>) async throws -> [T] {
        let context = backgroundContext
        
        return try await context.perform {
            do {
                let results = try context.fetch(request)
                print("📱 Background fetched \(results.count) \(T.self) objects")
                return results
            } catch {
                print("❌ Background fetch failed: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    // MARK: - Delete Operations
    func delete(_ object: NSManagedObject) async throws {
        let context = viewContext
        context.delete(object)
        try await save()
        print("🗑️ Object deleted successfully")
    }
    
    func batchDelete<T: NSManagedObject>(_ entityType: T.Type, predicate: NSPredicate? = nil) async throws {
        let context = backgroundContext
        
        try await context.perform {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: String(describing: entityType))
            if let predicate = predicate {
                fetchRequest.predicate = predicate
            }
            
            let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
            batchDeleteRequest.resultType = .resultTypeCount
            
            do {
                let result = try context.execute(batchDeleteRequest) as? NSBatchDeleteResult
                let deletedCount = result?.result as? Int ?? 0
                print("🗑️ Batch deleted \(deletedCount) \(entityType) objects")
                
                try context.save()
            } catch {
                print("❌ Batch delete failed: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    // MARK: - Utility Methods
    func count<T: NSManagedObject>(_ entityType: T.Type, predicate: NSPredicate? = nil) async throws -> Int {
        let context = viewContext
        let request = NSFetchRequest<T>(entityName: String(describing: entityType))
        request.predicate = predicate
        
        do {
            let count = try context.count(for: request)
            print("📊 Count for \(entityType): \(count)")
            return count
        } catch {
            print("❌ Count failed: \(error.localizedDescription)")
            throw error
        }
    }
    
    func exists<T: NSManagedObject>(_ entityType: T.Type, predicate: NSPredicate) async throws -> Bool {
        let count = try await self.count(entityType, predicate: predicate)
        return count > 0
    }
    
    // MARK: - Reset Operations
    func resetAllData() async throws {
        print("🧹 Resetting all Core Data...")
        
        let context = backgroundContext
        try await context.perform {
            // Delete all TaskEntity objects
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "TaskEntity")
            let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
            
            do {
                try context.execute(batchDeleteRequest)
                try context.save()
                print("✅ All data reset successfully")
            } catch {
                print("❌ Reset failed: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    // MARK: - Error Handling
    private func setError(_ message: String) {
        errorMessage = message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.errorMessage = nil
        }
    }
    
    // MARK: - Notifications
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(contextDidSave),
            name: .NSManagedObjectContextDidSave,
            object: nil
        )
    }
    
    @objc private func contextDidSave(_ notification: Notification) {
        guard let context = notification.object as? NSManagedObjectContext else { return }
        
        if context != viewContext {
            viewContext.perform {
                self.viewContext.mergeChanges(fromContextDidSave: notification)
            }
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Core Data Extensions
extension NSManagedObjectContext {
    func saveIfNeeded() throws {
        guard hasChanges else { return }
        try save()
    }
}

// MARK: - Preview Helper
extension CoreDataManager {
    static var preview: CoreDataManager {
        let manager = CoreDataManager()
        // Add preview data if needed
        return manager
    }
}
