//
//  TaskRowView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Enhanced Task Row View
struct TaskRowView: View {
    let task: Task
    @EnvironmentObject private var dataManager: DataManager
    @State private var showingTaskDetail = false
    @State private var isPressed = false
    @State private var isCompleting = false

    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                showingTaskDetail = true
            }
        }) {
            HStack(spacing: 16) {
                // Completion Button with Animation
                Button(action: {
                    toggleCompletion()
                }) {
                    ZStack {
                        Circle()
                            .fill(task.status == .completed ? DesignSystem.Colors.success : DesignSystem.Colors.surface)
                            .frame(width: 28, height: 28)
                            .overlay(
                                Circle()
                                    .stroke(task.status == .completed ? DesignSystem.Colors.success : DesignSystem.Colors.border, lineWidth: 2)
                            )

                        if task.status == .completed {
                            Image(systemName: "checkmark")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(.white)
                                .scaleEffect(isCompleting ? 1.2 : 1.0)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isCompleting)
                        }

                        if isCompleting {
                            Circle()
                                .stroke(DesignSystem.Colors.success, lineWidth: 2)
                                .frame(width: 32, height: 32)
                                .rotationEffect(.degrees(isCompleting ? 360 : 0))
                                .animation(.linear(duration: 0.5), value: isCompleting)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
                
                // Task Content
                VStack(alignment: .leading, spacing: 5) {
                    // Title and Important Star
                    HStack {
                        Text(task.title)
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(task.status == .completed ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.text)
                            .strikethrough(task.status == .completed)
                            .lineLimit(2)
                        
                        if task.isImportant {
                            Image(systemName: "star.fill")
                                .font(.system(size: 11))
                                .foregroundColor(DesignSystem.Colors.goldenHour)
                        }

                        Spacer()
                    }

                    // Description
                    if let description = task.description, !description.isEmpty {
                        Text(description)
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(1)
                            .opacity(task.status == .completed ? 0.6 : 1.0)
                    }
                    
                    // Tags and Metadata
                    HStack(spacing: 8) {
                        // Priority Badge
                        priorityBadge
                        
                        // Category
                        if let category = task.category {
                            HStack(spacing: 2) {
                                Image(systemName: category.icon)
                                    .font(.system(size: 9))
                                Text(category.name)
                                    .font(.system(size: 11, weight: .medium))
                            }
                            .foregroundColor(Color(category.color))
                            .padding(.horizontal, 5)
                            .padding(.vertical, 2)
                            .background(Color(category.color).opacity(0.08))
                            .cornerRadius(6)
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(Color(category.color).opacity(0.2), lineWidth: 0.5)
                            )
                        }
                        
                        // Due Date
                        if let dueDate = task.dueDate {
                            dueDateBadge(dueDate)
                        }
                        
                        Spacer()
                        
                        // Difficulty
                        difficultyBadge
                    }
                    
                    // Subtasks Progress
                    if !task.subtasks.isEmpty {
                        subtasksProgress
                    }
                }
                
                // Chevron with animation
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .rotationEffect(.degrees(isPressed ? 90 : 0))
                    .animation(.easeInOut(duration: 0.2), value: isPressed)
            }
            .padding(.horizontal, 14)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemBackground))
                    .shadow(
                        color: isPressed ? DesignSystem.Colors.primary.opacity(0.1) : Color.black.opacity(0.03),
                        radius: isPressed ? 6 : 2,
                        x: 0,
                        y: isPressed ? 3 : 1
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(.systemGray6), lineWidth: 0.5)
                    )
            )
            .scaleEffect(isPressed ? 0.99 : 1.0)
            .opacity(task.status == .completed ? 0.75 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
            .animation(.easeInOut(duration: 0.3), value: task.status)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .sheet(isPresented: $showingTaskDetail) {
            TaskDetailView(task: task)
        }
    }
    
    // MARK: - Priority Badge
    private var priorityBadge: some View {
        HStack(spacing: 2) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 9))
            Text(task.priority.displayName)
                .font(.system(size: 11, weight: .medium))
        }
        .foregroundColor(priorityColor)
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(priorityColor.opacity(0.08))
        .cornerRadius(6)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(priorityColor.opacity(0.2), lineWidth: 0.5)
        )
    }
    
    private var priorityColor: Color {
        switch task.priority {
        case .high:
            return Color(red: 0.95, green: 0.26, blue: 0.21) // أحمر حيوي
        case .medium:
            return Color(red: 1.0, green: 0.58, blue: 0.0)   // برتقالي حيوي
        case .low:
            return Color(red: 0.20, green: 0.78, blue: 0.35) // أخضر حيوي
        }
    }
    
    // MARK: - Due Date Badge
    private func dueDateBadge(_ dueDate: Date) -> some View {
        HStack(spacing: 2) {
            Image(systemName: task.isOverdue ? "exclamationmark.triangle.fill" : "calendar")
                .font(.system(size: 9))
            Text(formatDueDate(dueDate))
                .font(.system(size: 11, weight: .medium))
        }
        .foregroundColor(dueDateColor(dueDate))
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(dueDateColor(dueDate).opacity(0.08))
        .cornerRadius(6)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(dueDateColor(dueDate).opacity(0.2), lineWidth: 0.5)
        )
    }
    
    private func formatDueDate(_ date: Date) -> String {
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInTomorrow(date) {
            return "Tomorrow"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            let formatter = DateFormatter()
            formatter.dateFormat = "EEEE"
            return formatter.string(from: date)
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM d"
            return formatter.string(from: date)
        }
    }
    
    private func dueDateColor(_ date: Date) -> Color {
        if task.status == .completed {
            return Color(.systemGray)
        } else if date < Date() {
            return Color(.systemRed)
        } else if Calendar.current.isDateInToday(date) {
            return Color(.systemOrange)
        } else if Calendar.current.isDateInTomorrow(date) {
            return Color(.systemBlue)
        } else {
            return Color(.systemTeal)
        }
    }
    
    // MARK: - Difficulty Badge
    private var difficultyBadge: some View {
        HStack(spacing: 2) {
            Image(systemName: "gauge")
                .font(.system(size: 9))
            Text(task.difficulty.displayName)
                .font(.system(size: 11, weight: .medium))
        }
        .foregroundColor(Color(task.difficulty.color))
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(Color(task.difficulty.color).opacity(0.08))
        .cornerRadius(6)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(Color(task.difficulty.color).opacity(0.2), lineWidth: 0.5)
        )
    }
    
    // MARK: - Subtasks Progress
    private var subtasksProgress: some View {
        let completedCount = task.subtasks.filter { $0.isCompleted }.count
        let totalCount = task.subtasks.count

        return HStack(spacing: 3) {
            Image(systemName: "list.bullet")
                .font(.system(size: 9))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Text("\(completedCount)/\(totalCount)")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(completedCount == totalCount ? DesignSystem.Colors.success : DesignSystem.Colors.textSecondary)

            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 2)
                        .cornerRadius(1)

                    Rectangle()
                        .fill(completedCount == totalCount ? DesignSystem.Colors.success : DesignSystem.Colors.primary)
                        .frame(width: geometry.size.width * (totalCount > 0 ? CGFloat(completedCount) / CGFloat(totalCount) : 0), height: 2)
                        .cornerRadius(1)
                        .animation(.easeInOut(duration: 0.3), value: completedCount)
                }
            }
            .frame(height: 2)
        }
    }
    
    // MARK: - Actions
    private func toggleCompletion() {
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Animate completion state
        withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
            isCompleting = true
        }

        _Concurrency.Task {
            await dataManager.toggleTaskCompletion(task)

            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isCompleting = false
                }
            }
        }
    }
}

// MARK: - Enhanced Task Detail View
struct TaskDetailView: View {
    let task: Task
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager

    @State private var showingEditView = false
    @State private var showingDeleteAlert = false
    @State private var isDeleting = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Title
                    Text(task.title)
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)

                    // Status Badge
                    HStack {
                        StatusBadge(status: task.status)
                        Spacer()
                    }

                    // Description
                    if let description = task.description, !description.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description")
                                .font(DesignSystem.Typography.headline)
                                .foregroundColor(DesignSystem.Colors.text)

                            Text(description)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }

                    // Metadata
                    VStack(alignment: .leading, spacing: 12) {
                        if let dueDate = task.dueDate {
                            MetadataRow(icon: "calendar", label: "Due Date", value: dueDate.formatted(date: .abbreviated, time: .omitted))
                        }

                        MetadataRow(icon: "exclamationmark.triangle", label: "Priority", value: task.priority.displayName)
                        MetadataRow(icon: "gauge", label: "Difficulty", value: task.difficulty.displayName)

                        if let estimatedDuration = task.estimatedDuration {
                            MetadataRow(icon: "clock", label: "Estimated Duration", value: formatDuration(estimatedDuration))
                        }

                        if !task.tags.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Tags")
                                    .font(DesignSystem.Typography.bodyBold)
                                    .foregroundColor(DesignSystem.Colors.text)

                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                                    ForEach(task.tags, id: \.self) { tag in
                                        Text(tag)
                                            .font(DesignSystem.Typography.caption)
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 4)
                                            .background(DesignSystem.Colors.primary.opacity(0.1))
                                            .foregroundColor(DesignSystem.Colors.primary)
                                            .cornerRadius(8)
                                    }
                                }
                            }
                        }
                    }

                    // Subtasks
                    if !task.subtasks.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Subtasks (\(task.subtasks.filter { $0.isCompleted }.count)/\(task.subtasks.count))")
                                .font(DesignSystem.Typography.headline)
                                .foregroundColor(DesignSystem.Colors.text)

                            ForEach(task.subtasks) { subtask in
                                HStack {
                                    Image(systemName: subtask.isCompleted ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(subtask.isCompleted ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)

                                    Text(subtask.title)
                                        .font(DesignSystem.Typography.body)
                                        .strikethrough(subtask.isCompleted)
                                        .foregroundColor(subtask.isCompleted ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.text)
                                }
                            }
                        }
                    }

                    // Action Buttons
                    VStack(spacing: 12) {
                        // Edit Button
                        Button(action: {
                            showingEditView = true
                        }) {
                            HStack {
                                Image(systemName: "pencil")
                                Text("Edit Task")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(DesignSystem.Colors.primary)
                            .foregroundColor(.white)
                            .cornerRadius(DesignSystem.CornerRadius.medium)
                        }

                        // Delete Button
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("Delete Task")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(DesignSystem.CornerRadius.medium)
                        }
                        .disabled(isDeleting)
                    }
                    .padding(.top, 20)

                    Spacer()
                }
                .padding(20)
            }
            .navigationTitle("Task Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            TaskEditView(task: task)
        }
        .alert("Delete Task", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteTask()
            }
        } message: {
            Text("Are you sure you want to delete '\(task.title)'? This action cannot be undone.")
        }
    }

    private func deleteTask() {
        isDeleting = true
        _Concurrency.Task {
            await dataManager.deleteTask(task)
            await MainActor.run {
                isDeleting = false
                dismiss()
            }
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - Helper Views
struct StatusBadge: View {
    let status: Task.TaskStatus

    var body: some View {
        Text(status.displayName)
            .font(DesignSystem.Typography.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(statusColor.opacity(0.2))
            .foregroundColor(statusColor)
            .cornerRadius(8)
    }

    private var statusColor: Color {
        switch status {
        case .completed:
            return .green
        case .inProgress:
            return DesignSystem.Colors.primary
        case .active:
            return .blue
        case .draft:
            return .gray
        case .cancelled:
            return .red
        case .archived:
            return .secondary
        }
    }
}

struct MetadataRow: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(DesignSystem.Colors.primary)
                .frame(width: 20)

            Text(label)
                .font(DesignSystem.Typography.bodyBold)
                .foregroundColor(DesignSystem.Colors.text)

            Spacer()

            Text(value)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
}

#Preview {
    let sampleTask = Task(
        title: "Sample Task",
        description: "This is a sample task description",
        dueDate: Date(),
        priority: .high,
        createdByUserId: UUID()
    )

    return TaskRowView(task: sampleTask)
        .environmentObject(DataManager.shared)
}
