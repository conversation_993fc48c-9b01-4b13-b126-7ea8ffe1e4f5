//
//  TaskEditView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Task Edit View
struct TaskEditView: View {
    let task: Task
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    
    // Form fields
    @State private var title: String
    @State private var description: String
    @State private var dueDate: Date?
    @State private var priority: Task.Priority
    @State private var difficulty: Task.Difficulty
    @State private var tags: [String]
    @State private var estimatedDuration: TimeInterval
    @State private var isImportant: Bool
    
    // UI state
    @State private var isUpdating = false
    @State private var showingDatePicker = false
    @State private var showingDurationPicker = false
    @State private var newTag = ""
    
    @FocusState private var focusedField: Field?
    
    enum Field {
        case title, description, newTag
    }
    
    init(task: Task) {
        self.task = task
        self._title = State(initialValue: task.title)
        self._description = State(initialValue: task.description ?? "")
        self._dueDate = State(initialValue: task.dueDate)
        self._priority = State(initialValue: task.priority)
        self._difficulty = State(initialValue: task.difficulty)
        self._tags = State(initialValue: task.tags)
        self._estimatedDuration = State(initialValue: task.estimatedDuration ?? 0)
        self._isImportant = State(initialValue: task.isImportant)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Basic Information
                    basicInfoSection
                    
                    // Priority and Difficulty
                    priorityDifficultySection
                    
                    // Date and Duration
                    dateTimeSection
                    
                    // Tags
                    tagsSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Edit Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        updateTask()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .fontWeight(.semibold)
                    .disabled(!isFormValid || isUpdating)
                }
            }
        }
        .sheet(isPresented: $showingDatePicker) {
            DatePickerView(date: $dueDate, title: "Due Date")
        }
        .sheet(isPresented: $showingDurationPicker) {
            DurationPickerView(duration: $estimatedDuration)
        }
    }
    
    // MARK: - Form Sections
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Basic Information")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                TaskMateTextField(
                    "Task Title",
                    placeholder: "What needs to be done?",
                    text: $title
                )
                .focused($focusedField, equals: .title)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Description")
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    TextEditor(text: $description)
                        .focused($focusedField, equals: .description)
                        .frame(minHeight: 80)
                        .padding(12)
                        .background(DesignSystem.Colors.surface)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                .stroke(DesignSystem.Colors.border, lineWidth: 1)
                        )
                }
            }
        }
    }
    
    private var priorityDifficultySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Priority & Difficulty")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 16) {
                // Priority Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Priority")
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    HStack(spacing: 12) {
                        ForEach(Task.Priority.allCases, id: \.self) { priorityOption in
                            PriorityButton(
                                priority: priorityOption,
                                isSelected: priority == priorityOption
                            ) {
                                priority = priorityOption
                            }
                        }
                    }
                }
                
                // Difficulty Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Difficulty")
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    HStack(spacing: 12) {
                        ForEach(Task.Difficulty.allCases, id: \.self) { difficultyOption in
                            DifficultyButton(
                                difficulty: difficultyOption,
                                isSelected: difficulty == difficultyOption
                            ) {
                                difficulty = difficultyOption
                            }
                        }
                    }
                }
                
                // Important Toggle
                Toggle("Mark as Important", isOn: $isImportant)
                    .font(DesignSystem.Typography.bodyBold)
                    .foregroundColor(DesignSystem.Colors.text)
            }
        }
    }
    
    private var dateTimeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Date & Time")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                // Due Date
                Button(action: {
                    showingDatePicker = true
                }) {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(DesignSystem.Colors.primary)
                        
                        Text("Due Date")
                            .font(DesignSystem.Typography.bodyBold)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                        
                        if let dueDate = dueDate {
                            Text(dueDate.formatted(date: .abbreviated, time: .omitted))
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        } else {
                            Text("Not set")
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    .padding()
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
                
                // Estimated Duration
                Button(action: {
                    showingDurationPicker = true
                }) {
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(DesignSystem.Colors.primary)
                        
                        Text("Estimated Duration")
                            .font(DesignSystem.Typography.bodyBold)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                        
                        Text(formatDuration(estimatedDuration))
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    .padding()
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
            }
        }
    }
    
    private var tagsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tags")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                // Add new tag
                HStack {
                    TaskMateTextField(
                        "Add Tag",
                        placeholder: "Enter tag name",
                        text: $newTag
                    )
                    .focused($focusedField, equals: .newTag)
                    .onSubmit {
                        addTag()
                    }
                    
                    Button("Add") {
                        addTag()
                    }
                    .disabled(newTag.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                
                // Existing tags
                if !tags.isEmpty {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(tags, id: \.self) { tag in
                            HStack {
                                Text(tag)
                                    .font(DesignSystem.Typography.caption)
                                
                                Button(action: {
                                    removeTag(tag)
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.caption)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(DesignSystem.Colors.primary.opacity(0.1))
                            .foregroundColor(DesignSystem.Colors.primary)
                            .cornerRadius(8)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // MARK: - Actions
    private func updateTask() {
        isUpdating = true
        
        var updatedTask = task
        updatedTask.title = title.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedTask.description = description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedTask.dueDate = dueDate
        updatedTask.priority = priority
        updatedTask.difficulty = difficulty
        updatedTask.tags = tags
        updatedTask.estimatedDuration = estimatedDuration > 0 ? estimatedDuration : nil
        updatedTask.isImportant = isImportant
        updatedTask.updatedAt = Date()
        
        _Concurrency.Task {
            await dataManager.updateTask(updatedTask)
            
            await MainActor.run {
                isUpdating = false
                dismiss()
            }
        }
    }
    
    private func addTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !tags.contains(trimmedTag) {
            tags.append(trimmedTag)
            newTag = ""
        }
    }
    
    private func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        if duration == 0 {
            return "Not set"
        }

        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - Helper Views
struct PriorityButton: View {
    let priority: Task.Priority
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(priority.displayName)
                .font(DesignSystem.Typography.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(isSelected ? priorityColor : DesignSystem.Colors.surface)
                .foregroundColor(isSelected ? .white : priorityColor)
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(priorityColor, lineWidth: 1)
                )
        }
    }

    private var priorityColor: Color {
        switch priority {
        case .low:
            return .green
        case .medium:
            return .orange
        case .high:
            return .red
        }
    }
}

struct DifficultyButton: View {
    let difficulty: Task.Difficulty
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(difficulty.displayName)
                .font(DesignSystem.Typography.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(isSelected ? difficultyColor : DesignSystem.Colors.surface)
                .foregroundColor(isSelected ? .white : difficultyColor)
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(difficultyColor, lineWidth: 1)
                )
        }
    }

    private var difficultyColor: Color {
        switch difficulty {
        case .easy:
            return .green
        case .medium:
            return .yellow
        case .hard:
            return .orange
        case .expert:
            return .red
        }
    }
}
