//
//  MainTabView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Home Dashboard
            HomeView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Home")
                }
                .tag(0)
            
            // Personal Tasks
            PersonalTasksView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "checkmark.square.fill" : "checkmark.square")
                    Text("Tasks")
                }
                .tag(1)
            
            // Groups
            NavigationStack {
                GroupsView()
                    .navigationTitle("Groups")
                    .navigationBarTitleDisplayMode(.large)
                    .navigationDestination(for: Group.self) { group in
                        GroupDetailView(group: group)
                    }
            }
            .tabItem {
                Image(systemName: selectedTab == 2 ? "person.3.fill" : "person.3")
                Text("Groups")
            }
            .tag(2)
            
            // Friends
            FriendsView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "heart.fill" : "heart")
                    Text("Friends")
                }
                .tag(3)
            
            // Settings
            SettingsView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "gearshape.fill" : "gearshape")
                    Text("Settings")
                }
                .tag(4)
        }
        .accentColor(DesignSystem.Colors.sunsetCoral)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.white
        
        // Selected item color
        appearance.selectionIndicatorTintColor = UIColor(DesignSystem.Colors.sunsetCoral)
        
        // Normal item color
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor(DesignSystem.Colors.textSecondary)
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor(DesignSystem.Colors.textSecondary)
        ]
        
        // Selected item color
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor(DesignSystem.Colors.sunsetCoral)
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor(DesignSystem.Colors.sunsetCoral)
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Placeholder Views
struct HomeView: View {
    var body: some View {
        NavigationView {
            ZStack {
                // Gradient background
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.sunriseOrange.opacity(0.1),
                        DesignSystem.Colors.dawnBlue.opacity(0.3)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Welcome message
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text("Good Morning! 🌅")
                            .font(DesignSystem.Typography.displayMediumStyle)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("Ready to make today productive?")
                            .font(DesignSystem.Typography.bodyLargeStyle)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    // Quick stats cards
                    HStack(spacing: DesignSystem.Spacing.md) {
                        QuickStatCard(title: "Today's Tasks", value: "5", color: DesignSystem.Colors.sunriseOrange)
                        QuickStatCard(title: "Completed", value: "2", color: DesignSystem.Colors.success)
                        QuickStatCard(title: "Streak", value: "7", color: DesignSystem.Colors.goldenHour)
                    }
                    
                    Spacer()
                    
                    // Coming soon message
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 48))
                            .foregroundColor(DesignSystem.Colors.sunsetCoral)
                        
                        Text("Dashboard Coming Soon")
                            .font(DesignSystem.Typography.headlineLargeStyle)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("We're building an amazing dashboard to help you track your progress and stay motivated!")
                            .font(DesignSystem.Typography.bodyMediumStyle)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, DesignSystem.Spacing.lg)
                    }
                    
                    Spacer()
                }
                .padding(DesignSystem.Spacing.lg)
            }
            .navigationTitle("TaskMate")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

struct QuickStatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xs) {
            Text(value)
                .font(DesignSystem.Typography.displaySmallStyle)
                .foregroundColor(color)
            
            Text(title)
                .font(DesignSystem.Typography.captionMediumStyle)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(DesignSystem.Spacing.md)
        .taskMateCard()
    }
}

struct PersonalTasksView: View {
    @EnvironmentObject private var dataManager: DataManager
    @State private var showingTaskCreation = false
    @State private var isRefreshing = false
    @State private var searchText = ""
    @State private var selectedFilter: TaskFilter = .all

    enum TaskFilter: String, CaseIterable {
        case all = "All"
        case pending = "Pending"
        case inProgress = "In Progress"
        case completed = "Completed"
        case important = "Important"
        case overdue = "Overdue"
    }

    var filteredTasks: [Task] {
        let filtered = dataManager.tasks.filter { task in
            // Search filter
            if !searchText.isEmpty {
                let searchLower = searchText.lowercased()
                let matchesTitle = task.title.lowercased().contains(searchLower)
                let matchesDescription = task.description?.lowercased().contains(searchLower) ?? false
                let matchesTags = task.tags.contains { $0.lowercased().contains(searchLower) }

                if !(matchesTitle || matchesDescription || matchesTags) {
                    return false
                }
            }

            // Status filter
            switch selectedFilter {
            case .all:
                return true
            case .pending:
                return task.status != .completed && task.status != .inProgress
            case .inProgress:
                return task.status == .inProgress
            case .completed:
                return task.status == .completed
            case .important:
                return task.isImportant
            case .overdue:
                return task.isOverdue && task.status != .completed
            }
        }

        // Sort by priority and due date
        return filtered.sorted { task1, task2 in
            // Completed tasks go to bottom
            if task1.status == .completed && task2.status != .completed {
                return false
            } else if task1.status != .completed && task2.status == .completed {
                return true
            }

            // Important tasks go to top
            if task1.isImportant && !task2.isImportant {
                return true
            } else if !task1.isImportant && task2.isImportant {
                return false
            }

            // Overdue tasks go to top
            if task1.isOverdue && !task2.isOverdue {
                return true
            } else if !task1.isOverdue && task2.isOverdue {
                return false
            }

            // Sort by due date
            if let date1 = task1.dueDate, let date2 = task2.dueDate {
                return date1 < date2
            } else if task1.dueDate != nil {
                return true
            } else if task2.dueDate != nil {
                return false
            }

            // Finally sort by creation date
            return task1.createdAt > task2.createdAt
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with Add Button (replacing navigation title)
                headerSection

                // Search Bar
                searchBar

                // Filter Tabs
                filterTabs

                // Content
                if dataManager.tasks.isEmpty && !dataManager.isSyncing {
                    // Empty State
                    emptyState
                } else if filteredTasks.isEmpty && !searchText.isEmpty {
                    // No Search Results
                    noSearchResults
                } else {
                    // Task List with Pull-to-Refresh
                    taskList
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingTaskCreation) {
            EnhancedTaskCreationView()
        }
    }

    // MARK: - UI Components
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("My Tasks")
                    .font(.system(size: 28, weight: .bold, design: .default))
                    .foregroundColor(DesignSystem.Colors.text)

                if !dataManager.tasks.isEmpty {
                    Text("\(filteredTasks.count) of \(dataManager.tasks.count) tasks")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            Spacer()

            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    showingTaskCreation = true
                }
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color(.systemBlue),
                                        Color(.systemPurple)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                    .shadow(color: Color(.systemBlue).opacity(0.3), radius: 4, x: 0, y: 2)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 12)
    }

    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            TextField("Search tasks...", text: $searchText)
                .font(.system(size: 15, weight: .regular))
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        searchText = ""
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
    }

    private var filterTabs: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(TaskFilter.allCases, id: \.self) { filter in
                    FilterTab(
                        title: filter.rawValue,
                        count: getFilterCount(filter),
                        isSelected: selectedFilter == filter
                    ) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedFilter = filter
                        }
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 8)
        .padding(.bottom, 4)
    }

    private var emptyState: some View {
        VStack(spacing: 20) {
            Spacer()

            // Animated Icon
            Image(systemName: "checkmark.square")
                .font(.system(size: 56))
                .foregroundColor(DesignSystem.Colors.primary.opacity(0.7))

            Text("No Tasks Yet")
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)

            Text("Create your first task to get started!")
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            TaskMateButton("Create Your First Task") {
                showingTaskCreation = true
            }
            .padding(.horizontal, 40)

            Spacer()
        }
        .transition(.opacity.combined(with: .scale))
    }

    private var noSearchResults: some View {
        VStack(spacing: 14) {
            Spacer()

            Image(systemName: "magnifyingglass")
                .font(.system(size: 42))
                .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.7))

            Text("No Results Found")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            Text("Try adjusting your search or filter")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Spacer()
        }
        .transition(.opacity)
    }

    private var taskList: some View {
        List {
            if dataManager.isSyncing && !isRefreshing {
                // Loading indicator
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(0.9)
                    Text("Syncing tasks...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.leading, 8)
                    Spacer()
                }
                .padding(.vertical, 16)
                .listRowBackground(Color.clear)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets())
            }

            ForEach(filteredTasks) { task in
                TaskRowView(task: task)
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets(top: 3, leading: 20, bottom: 3, trailing: 20))
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
            }
        }
        .listStyle(PlainListStyle())
        .refreshable {
            await refreshTasks()
        }
        .animation(.easeInOut(duration: 0.3), value: filteredTasks.count)
    }

    // MARK: - Helper Methods
    private func getFilterCount(_ filter: TaskFilter) -> Int {
        switch filter {
        case .all:
            return dataManager.tasks.count
        case .pending:
            return dataManager.tasks.filter { $0.status != .completed && $0.status != .inProgress }.count
        case .inProgress:
            return dataManager.tasks.filter { $0.status == .inProgress }.count
        case .completed:
            return dataManager.tasks.filter { $0.status == .completed }.count
        case .important:
            return dataManager.tasks.filter { $0.isImportant }.count
        case .overdue:
            return dataManager.tasks.filter { $0.isOverdue && $0.status != .completed }.count
        }
    }

    private func refreshTasks() async {
        isRefreshing = true

        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // Sync with a minimum delay for better UX
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await dataManager.syncDataIfNeeded()
            }
            group.addTask {
                try? await _Concurrency.Task.sleep(nanoseconds: 1_000_000_000) // 1 second minimum
            }
        }

        isRefreshing = false
    }
}

// MARK: - Filter Tab Component
struct FilterTab: View {
    let title: String
    let count: Int
    let isSelected: Bool
    let action: () -> Void

    private var filterColor: Color {
        switch title {
        case "All":
            return Color(.systemBlue)
        case "Pending":
            return Color(.systemOrange)
        case "In Progress":
            return Color(.systemPurple)
        case "Completed":
            return Color(.systemGreen)
        case "Important":
            return Color(.systemYellow)
        case "Overdue":
            return Color(.systemRed)
        default:
            return DesignSystem.Colors.primary
        }
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 13, weight: isSelected ? .semibold : .medium))

                if count > 0 {
                    Text("\(count)")
                        .font(.system(size: 11, weight: .bold))
                        .foregroundColor(isSelected ? .white : DesignSystem.Colors.textSecondary)
                        .padding(.horizontal, 5)
                        .padding(.vertical, 1)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(isSelected ? filterColor : Color(.systemGray5))
                        )
                }
            }
            .foregroundColor(isSelected ? filterColor : DesignSystem.Colors.textSecondary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? filterColor.opacity(0.08) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? filterColor.opacity(0.3) : Color(.systemGray5), lineWidth: 0.5)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
    }
}





struct SettingsView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @State private var showingEditProfile = false
    @State private var showingLogoutAlert = false

    var body: some View {
        NavigationView {
            List {
                // Profile Section
                profileSection

                // Account Actions
                accountSection
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .alert("Sign Out", isPresented: $showingLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                _Concurrency.Task {
                    await authManager.logout()
                }
            }
        } message: {
            Text("Are you sure you want to sign out?")
        }
    }

    // MARK: - Profile Section
    private var profileSection: some View {
        Section {
            HStack(spacing: 12) {
                // Avatar
                if let avatarURL = authManager.currentUser?.avatarURL, !avatarURL.isEmpty {
                    AsyncImage(url: URL(string: avatarURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Circle()
                            .fill(Color(.systemGray5))
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.6)
                            )
                    }
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
                } else {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                        .overlay(
                            Text(authManager.currentUser?.displayName.prefix(2).uppercased() ?? "U")
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(.white)
                        )
                }

                VStack(alignment: .leading, spacing: 3) {
                    Text(authManager.currentUser?.displayName ?? "User")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Text(authManager.currentUser?.email ?? "<EMAIL>")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    if let bio = authManager.currentUser?.bio {
                        Text(bio)
                            .font(.system(size: 11, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                Button(action: {
                    showingEditProfile = true
                }) {
                    Image(systemName: "pencil")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.sunriseOrange)
                }
            }
            .padding(.vertical, 6)
        }
    }







    // MARK: - Account Actions Section
    private var accountSection: some View {
        Section {
            Button(action: {
                showingLogoutAlert = true
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.red)
                        .frame(width: 20)

                    Text("Sign Out")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.red)

                    Spacer()
                }
                .padding(.vertical, 2)
            }
        }
    }
}

struct PlaceholderView: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    DesignSystem.Colors.twilightPurple.opacity(0.3),
                    DesignSystem.Colors.dawnBlue.opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                Spacer()
                
                Image(systemName: icon)
                    .font(.system(size: 64))
                    .foregroundColor(DesignSystem.Colors.sunsetCoral)
                
                Text(title)
                    .font(DesignSystem.Typography.displaySmallStyle)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(description)
                    .font(DesignSystem.Typography.bodyMediumStyle)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                
                Text("Coming Soon! 🚀")
                    .font(DesignSystem.Typography.headlineSmallStyle)
                    .foregroundColor(DesignSystem.Colors.goldenHour)
                    .padding(.top, DesignSystem.Spacing.md)
                
                Spacer()
            }
            .padding(DesignSystem.Spacing.lg)
        }
    }
}

// MARK: - Settings Row Component
struct SettingsRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 20, height: 20)

                // Content
                VStack(alignment: .leading, spacing: 1) {
                    Text(title)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.text)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subtitle)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // Arrow
                Image(systemName: "chevron.right")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.vertical, 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    MainTabView()
}
