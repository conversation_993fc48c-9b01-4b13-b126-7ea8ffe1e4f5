//
//  NotificationSettingsView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Notification Settings View
struct NotificationSettingsView: View {
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var settings: NotificationSettings
    @State private var showingTimePickerFor: TimePicker?
    @State private var showingPermissionAlert = false
    
    enum TimePicker: String, CaseIterable, Identifiable {
        case dailySummary = "Daily Summary"
        case quietStart = "Quiet Hours Start"
        case quietEnd = "Quiet Hours End"

        var id: String { rawValue }
    }
    
    init() {
        _settings = State(initialValue: NotificationManager.shared.notificationSettings)
    }
    
    var body: some View {
        NavigationView {
            List {
                // Permission Status
                permissionSection
                
                // Basic Settings
                basicSettingsSection
                
                // Timing Settings
                timingSettingsSection
                
                // Advanced Settings
                advancedSettingsSection
                
                // Quiet Hours
                quietHoursSection
                
                // Statistics
                statisticsSection
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveSettings()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(item: $showingTimePickerFor) { timePicker in
            TimePickerSheet(
                title: timePicker.rawValue,
                time: getTimeBinding(for: timePicker)
            )
        }
        .alert("Notifications Disabled", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                openAppSettings()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable notifications in Settings to receive task reminders.")
        }
    }
    
    // MARK: - Sections
    private var permissionSection: some View {
        Section {
            HStack {
                Image(systemName: notificationManager.isAuthorized ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(notificationManager.isAuthorized ? .green : .red)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Notification Permission")
                        .font(.headline)
                    
                    Text(notificationManager.isAuthorized ? "Enabled" : "Disabled")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if !notificationManager.isAuthorized {
                    Button("Enable") {
                        requestPermission()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
            .padding(.vertical, 4)
        } header: {
            Text("Permission")
        }
    }
    
    private var basicSettingsSection: some View {
        Section {
            SettingsToggle(
                title: "Task Reminders",
                subtitle: "Get notified before tasks are due",
                icon: "bell.fill",
                iconColor: .blue,
                isOn: $settings.taskRemindersEnabled
            )
            
            SettingsToggle(
                title: "Smart Reminders",
                subtitle: "Intelligent reminder scheduling",
                icon: "brain.head.profile",
                iconColor: .purple,
                isOn: $settings.smartRemindersEnabled
            )
            
            SettingsToggle(
                title: "Overdue Alerts",
                subtitle: "Notifications for overdue tasks",
                icon: "exclamationmark.triangle.fill",
                iconColor: .red,
                isOn: $settings.overdueNotificationsEnabled
            )
            
            SettingsToggle(
                title: "Daily Summary",
                subtitle: "Daily progress overview",
                icon: "chart.bar.fill",
                iconColor: .green,
                isOn: $settings.dailySummaryEnabled
            )
            
            SettingsToggle(
                title: "Completion Celebration",
                subtitle: "Celebrate when you complete tasks",
                icon: "party.popper.fill",
                iconColor: .yellow,
                isOn: $settings.completionCelebrationEnabled
            )
        } header: {
            Text("Notification Types")
        }
    }
    
    private var timingSettingsSection: some View {
        Section {
            // Reminder timing
            HStack {
                Label("Default Reminder", systemImage: "clock.fill")
                    .foregroundColor(.orange)
                
                Spacer()
                
                Picker("Minutes Before", selection: $settings.reminderMinutesBefore) {
                    Text("5 minutes").tag(5)
                    Text("15 minutes").tag(15)
                    Text("30 minutes").tag(30)
                    Text("1 hour").tag(60)
                    Text("2 hours").tag(120)
                    Text("1 day").tag(1440)
                }
                .pickerStyle(.menu)
            }
            
            // Daily summary time
            Button(action: {
                showingTimePickerFor = .dailySummary
            }) {
                HStack {
                    Label("Daily Summary Time", systemImage: "calendar.circle.fill")
                        .foregroundColor(.green)
                    
                    Spacer()
                    
                    Text(settings.dailySummaryTime.displayString)
                        .foregroundColor(.secondary)
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(.plain)
            
            // Snooze duration
            HStack {
                Label("Snooze Duration", systemImage: "zzz")
                    .foregroundColor(.indigo)
                
                Spacer()
                
                Picker("Snooze Minutes", selection: $settings.snoozeMinutes) {
                    Text("5 minutes").tag(5)
                    Text("10 minutes").tag(10)
                    Text("15 minutes").tag(15)
                    Text("30 minutes").tag(30)
                    Text("1 hour").tag(60)
                }
                .pickerStyle(.menu)
            }
        } header: {
            Text("Timing")
        }
    }
    
    private var advancedSettingsSection: some View {
        Section {
            SettingsToggle(
                title: "Custom Sounds",
                subtitle: "Use priority-based notification sounds",
                icon: "speaker.wave.3.fill",
                iconColor: .pink,
                isOn: $settings.useCustomSounds
            )
            
            SettingsToggle(
                title: "Motivational Messages",
                subtitle: "Encouraging notifications throughout the day",
                icon: "heart.fill",
                iconColor: .red,
                isOn: $settings.motivationalMessagesEnabled
            )
            
            HStack {
                Label("Max Reminders per Task", systemImage: "repeat.circle.fill")
                    .foregroundColor(.teal)
                
                Spacer()
                
                Picker("Max Reminders", selection: $settings.maxRemindersPerTask) {
                    Text("1").tag(1)
                    Text("2").tag(2)
                    Text("3").tag(3)
                    Text("5").tag(5)
                    Text("Unlimited").tag(10)
                }
                .pickerStyle(.menu)
            }
        } header: {
            Text("Advanced")
        }
    }
    
    private var quietHoursSection: some View {
        Section {
            SettingsToggle(
                title: "Quiet Hours",
                subtitle: "Disable notifications during specific hours",
                icon: "moon.fill",
                iconColor: .indigo,
                isOn: $settings.quietHoursEnabled
            )
            
            if settings.quietHoursEnabled {
                Button(action: {
                    showingTimePickerFor = .quietStart
                }) {
                    HStack {
                        Label("Start Time", systemImage: "moon.circle.fill")
                            .foregroundColor(.indigo)
                        
                        Spacer()
                        
                        Text(settings.quietHoursStart.displayString)
                            .foregroundColor(.secondary)
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(.plain)
                
                Button(action: {
                    showingTimePickerFor = .quietEnd
                }) {
                    HStack {
                        Label("End Time", systemImage: "sun.max.circle.fill")
                            .foregroundColor(.orange)
                        
                        Spacer()
                        
                        Text(settings.quietHoursEnd.displayString)
                            .foregroundColor(.secondary)
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(.plain)
            }
        } header: {
            Text("Quiet Hours")
        } footer: {
            if settings.quietHoursEnabled {
                Text("Notifications will be silenced during quiet hours, except for high-priority overdue tasks.")
            }
        }
    }
    
    private var statisticsSection: some View {
        Section {
            let stats = notificationManager.getNotificationStats()
            
            StatRow(title: "Total Tasks", value: "\(stats.totalTasks)", icon: "list.bullet", color: .blue)
            StatRow(title: "With Reminders", value: "\(stats.tasksWithReminders)", icon: "bell", color: .green)
            StatRow(title: "Overdue", value: "\(stats.overdueTasks)", icon: "exclamationmark.triangle", color: .red)
            StatRow(title: "Completed Today", value: "\(stats.completedToday)", icon: "checkmark.circle", color: .green)
            
            HStack {
                Label("Reminder Coverage", systemImage: "chart.pie.fill")
                    .foregroundColor(.purple)
                
                Spacer()
                
                Text("\(Int(stats.reminderCoverage * 100))%")
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
            }
        } header: {
            Text("Statistics")
        }
    }
    
    // MARK: - Actions
    private func requestPermission() {
        _Concurrency.Task {
            await notificationManager.requestAuthorization()
            
            if !notificationManager.isAuthorized {
                await MainActor.run {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func saveSettings() {
        notificationManager.updateNotificationSettings(settings)
        
        // Reschedule all notifications with new settings
        notificationManager.cancelAllTaskReminders()
        notificationManager.scheduleAllTaskReminders()
        
        if settings.dailySummaryEnabled {
            notificationManager.scheduleDailySummary()
        }
        
        print("✅ Notification settings saved and applied")
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    private func getTimeBinding(for timePicker: TimePicker) -> Binding<NotificationSettings.NotificationTime> {
        switch timePicker {
        case .dailySummary:
            return $settings.dailySummaryTime
        case .quietStart:
            return $settings.quietHoursStart
        case .quietEnd:
            return $settings.quietHoursEnd
        }
    }
}

// MARK: - Helper Views
struct SettingsToggle: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Label {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } icon: {
                Image(systemName: icon)
                    .foregroundColor(iconColor)
            }

            Spacer()

            Toggle("", isOn: $isOn)
        }
        .padding(.vertical, 2)
    }
}

struct StatRow: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        HStack {
            Label(title, systemImage: icon)
                .foregroundColor(color)

            Spacer()

            Text(value)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
        }
    }
}

struct TimePickerSheet: View {
    let title: String
    @Binding var time: NotificationSettings.NotificationTime
    @Environment(\.dismiss) private var dismiss

    @State private var selectedDate: Date

    init(title: String, time: Binding<NotificationSettings.NotificationTime>) {
        self.title = title
        self._time = time

        var components = DateComponents()
        components.hour = time.wrappedValue.hour
        components.minute = time.wrappedValue.minute

        self._selectedDate = State(initialValue: Calendar.current.date(from: components) ?? Date())
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                DatePicker(
                    "Select Time",
                    selection: $selectedDate,
                    displayedComponents: .hourAndMinute
                )
                .datePickerStyle(.wheel)
                .labelsHidden()

                Spacer()
            }
            .padding()
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        let components = Calendar.current.dateComponents([.hour, .minute], from: selectedDate)
                        time = NotificationSettings.NotificationTime(
                            hour: components.hour ?? 0,
                            minute: components.minute ?? 0
                        )
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}
