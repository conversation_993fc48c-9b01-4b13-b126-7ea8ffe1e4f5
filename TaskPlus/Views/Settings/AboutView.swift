//
//  AboutView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    private let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    private let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // App Icon and Info
                    appInfoSection
                    
                    // Features
                    featuresSection
                    
                    // Team
                    teamSection
                    
                    // Legal
                    legalSection
                    
                    // Contact
                    contactSection
                    
                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .navigationTitle("About TaskMate")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(spacing: 16) {
            // App Icon
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: "checkmark.square.fill")
                        .font(.system(size: 40, weight: .bold))
                        .foregroundColor(.white)
                )
                .shadow(color: DesignSystem.Colors.sunriseOrange.opacity(0.3), radius: 10, x: 0, y: 5)
            
            VStack(spacing: 8) {
                Text("TaskMate")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Your Productivity Companion")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("Version \(appVersion) (\(buildNumber))")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
    }
    
    // MARK: - Features Section
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("What's Inside")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                FeatureRow(
                    icon: "checkmark.square.fill",
                    iconColor: .blue,
                    title: "Smart Task Management",
                    description: "Organize and prioritize your tasks with ease"
                )
                
                FeatureRow(
                    icon: "person.2.fill",
                    iconColor: .green,
                    title: "Team Collaboration",
                    description: "Work together with friends and colleagues"
                )
                
                FeatureRow(
                    icon: "heart.fill",
                    iconColor: .red,
                    title: "Motivation System",
                    description: "Stay motivated with our unique reward system"
                )
                
                FeatureRow(
                    icon: "chart.bar.fill",
                    iconColor: .purple,
                    title: "Progress Tracking",
                    description: "Monitor your productivity and achievements"
                )
                
                FeatureRow(
                    icon: "icloud.fill",
                    iconColor: .cyan,
                    title: "Cloud Sync",
                    description: "Access your tasks from anywhere, anytime"
                )
            }
        }
    }
    
    // MARK: - Team Section
    private var teamSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Made with ❤️ by")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                Text("TaskMate Development Team")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("We're passionate about helping people achieve their goals and stay productive. TaskMate is designed to make task management simple, social, and rewarding.")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Legal Section
    private var legalSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Legal")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                LegalRow(title: "Privacy Policy", action: {
                    // TODO: Open privacy policy
                })
                
                LegalRow(title: "Terms of Service", action: {
                    // TODO: Open terms of service
                })
                
                LegalRow(title: "Open Source Licenses", action: {
                    // TODO: Open licenses
                })
            }
        }
    }
    
    // MARK: - Contact Section
    private var contactSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Get in Touch")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                ContactRow(
                    icon: "envelope.fill",
                    iconColor: .blue,
                    title: "Support",
                    subtitle: "<EMAIL>",
                    action: {
                        // TODO: Open email
                    }
                )
                
                ContactRow(
                    icon: "globe",
                    iconColor: .green,
                    title: "Website",
                    subtitle: "www.taskmate.app",
                    action: {
                        // TODO: Open website
                    }
                )
                
                ContactRow(
                    icon: "message.fill",
                    iconColor: .purple,
                    title: "Feedback",
                    subtitle: "We'd love to hear from you!",
                    action: {
                        // TODO: Open feedback form
                    }
                )
            }
        }
    }
}

// MARK: - Feature Row Component
struct FeatureRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(iconColor)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text(description)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Legal Row Component
struct LegalRow: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Contact Row Component
struct ContactRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AboutView()
}
