//
//  PrivacySettingsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

struct PrivacySettingsView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var settingsManager = SettingsManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var profileVisibility: PrivacySettings.ProfileVisibility = .friends
    @State private var activitySharing = true
    @State private var allowFriendRequests = true
    @State private var showOnlineStatus = true
    @State private var allowMotivationalMessages = true
    @State private var allowDataCollection = true
    @State private var allowAnalytics = true
    
    var body: some View {
        NavigationView {
            List {
                // Profile Privacy
                profilePrivacySection
                
                // Activity & Sharing
                activitySection
                
                // Data & Analytics
                dataSection
                
                // Friends & Social
                socialSection
            }
            .navigationTitle("Privacy Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveSettings()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            loadCurrentSettings()
        }
    }
    
    // MARK: - Profile Privacy Section
    private var profilePrivacySection: some View {
        Section {
            VStack(alignment: .leading, spacing: 16) {
                Text("Who can see your profile")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                VStack(spacing: 12) {
                    ForEach(PrivacySettings.ProfileVisibility.allCases, id: \.self) { visibility in
                        profileVisibilityOption(visibility)
                    }
                }
            }
            .padding(.vertical, 8)
        } header: {
            Text("Profile Visibility")
        }
    }
    
    private func profileVisibilityOption(_ visibility: PrivacySettings.ProfileVisibility) -> some View {
        Button(action: { profileVisibility = visibility }) {
            HStack(spacing: 12) {
                Image(systemName: profileVisibility == visibility ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(profileVisibility == visibility ? DesignSystem.Colors.sunriseOrange : DesignSystem.Colors.textSecondary)
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(visibility.displayName)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(visibilityDescription(for: visibility))
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Activity Section
    private var activitySection: some View {
        Section("Activity & Sharing") {
            PrivacyToggle(
                title: "Share Activity",
                subtitle: "Let friends see your task progress",
                icon: "chart.bar.fill",
                iconColor: .blue,
                isOn: $activitySharing
            )
            
            PrivacyToggle(
                title: "Show Online Status",
                subtitle: "Let others see when you're active",
                icon: "circle.fill",
                iconColor: .green,
                isOn: $showOnlineStatus
            )
            
            PrivacyToggle(
                title: "Allow Motivational Messages",
                subtitle: "Receive motivation from friends",
                icon: "heart.fill",
                iconColor: .red,
                isOn: $allowMotivationalMessages
            )
        }
    }
    
    // MARK: - Data Section
    private var dataSection: some View {
        Section("Data & Analytics") {
            PrivacyToggle(
                title: "Data Collection",
                subtitle: "Help improve TaskMate with usage data",
                icon: "chart.pie.fill",
                iconColor: .purple,
                isOn: $allowDataCollection
            )
            
            PrivacyToggle(
                title: "Analytics",
                subtitle: "Share anonymous analytics data",
                icon: "chart.line.uptrend.xyaxis",
                iconColor: .orange,
                isOn: $allowAnalytics
            )
        }
    }
    
    // MARK: - Social Section
    private var socialSection: some View {
        Section("Friends & Social") {
            PrivacyToggle(
                title: "Allow Friend Requests",
                subtitle: "Let others send you friend requests",
                icon: "person.badge.plus.fill",
                iconColor: .cyan,
                isOn: $allowFriendRequests
            )
        }
    }
    
    // MARK: - Helper Methods
    private func visibilityDescription(for visibility: PrivacySettings.ProfileVisibility) -> String {
        switch visibility {
        case .everyone:
            return "Anyone can see your profile and activity"
        case .friends:
            return "Only your friends can see your profile"
        case .privateProfile:
            return "Your profile is completely private"
        }
    }
    
    private func loadCurrentSettings() {
        guard let user = authManager.currentUser else { return }
        
        profileVisibility = user.privacySettings.profileVisibility
        activitySharing = user.privacySettings.activitySharing
        allowFriendRequests = user.privacySettings.allowFriendRequests
        showOnlineStatus = user.privacySettings.showOnlineStatus
        allowMotivationalMessages = user.privacySettings.allowMotivationalMessages
        
        allowDataCollection = settingsManager.appSettings.allowDataCollection
        allowAnalytics = settingsManager.appSettings.allowAnalytics
    }
    
    private func saveSettings() {
        // Update user privacy settings
        guard var user = authManager.currentUser else { return }

        user.privacySettings.profileVisibility = profileVisibility
        user.privacySettings.activitySharing = activitySharing
        user.privacySettings.allowFriendRequests = allowFriendRequests
        user.privacySettings.showOnlineStatus = showOnlineStatus
        user.privacySettings.allowMotivationalMessages = allowMotivationalMessages

        _Concurrency.Task {
            await authManager.updateProfile(user: user)
        }

        // Update app settings
        settingsManager.appSettings.allowDataCollection = allowDataCollection
        settingsManager.appSettings.allowAnalytics = allowAnalytics
    }
}

// MARK: - Privacy Toggle Component
struct PrivacyToggle: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(iconColor)
                .frame(width: 24, height: 24)
            
            // Content
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text(subtitle)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // Toggle
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    PrivacySettingsView()
}
