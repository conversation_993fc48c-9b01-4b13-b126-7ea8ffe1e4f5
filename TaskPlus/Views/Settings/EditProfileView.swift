//
//  EditProfileView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI
import PhotosUI

struct EditProfileView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var imageUploadManager = ImageUploadManager.shared
    @Environment(\.dismiss) private var dismiss

    // MARK: - Form Fields
    @State private var displayName = ""
    @State private var username = ""
    @State private var bio = ""
    @State private var selectedPrivacy: PrivacySettings.ProfileVisibility = .friends

    // MARK: - Avatar Selection
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var avatarImage: UIImage?
    @State private var originalAvatarURL: String?
    @State private var showingImagePicker = false
    @State private var hasAvatarChanged = false

    // MARK: - UI State
    @State private var isLoading = false
    @State private var showingSuccessAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Avatar Section
                    avatarSection
                    
                    // Profile Form
                    profileForm
                    
                    // Privacy Settings
                    privacySection
                    
                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        _Concurrency.Task {
                            await saveProfile()
                        }
                    }
                    .fontWeight(.semibold)
                    .disabled(isLoading || !isFormValid || imageUploadManager.isUploading)
                }
            }
        }
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedPhoto, matching: .images)
        .onChange(of: selectedPhoto) { _, newValue in
            loadSelectedPhoto(newValue)
        }
        .onAppear {
            loadCurrentUserData()
        }
        .alert("Profile Updated", isPresented: $showingSuccessAlert) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your profile has been updated successfully.")
        }
        .alert("Error", isPresented: $showingErrorAlert) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                if !imageUploadManager.isUploading {
                    showingImagePicker = true
                }
            }) {
                ZStack {
                    if let avatarImage = avatarImage {
                        Image(uiImage: avatarImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(Circle())
                    } else if let avatarURL = authManager.currentUser?.avatarURL,
                              !avatarURL.isEmpty {
                        AsyncImage(url: URL(string: avatarURL)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Circle()
                                .fill(Color(.systemGray5))
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(0.8)
                                )
                        }
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 120, height: 120)
                            .overlay(
                                Text(displayName.prefix(2).uppercased())
                                    .font(.system(size: 32, weight: .bold))
                                    .foregroundColor(.white)
                            )
                    }

                    // Upload progress overlay
                    if imageUploadManager.isUploading {
                        Circle()
                            .fill(Color.black.opacity(0.6))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack(spacing: 8) {
                                    ProgressView(value: imageUploadManager.uploadProgress)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(1.2)

                                    Text("\(Int(imageUploadManager.uploadProgress * 100))%")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .fontWeight(.semibold)
                                }
                            )
                    } else {
                        // Edit overlay
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack(spacing: 4) {
                                    Image(systemName: "camera.fill")
                                        .font(.title2)
                                        .foregroundColor(.white)

                                    Text("Edit")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                }
                            )
                    }
                }
            }
            .disabled(imageUploadManager.isUploading)

            if imageUploadManager.isUploading {
                VStack(spacing: 4) {
                    Text("Uploading photo...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.sunriseOrange)

                    if !imageUploadManager.compressionInfo.isEmpty {
                        Text(imageUploadManager.compressionInfo)
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                }
            } else {
                VStack(spacing: 4) {
                    Text("Tap to change profile photo")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("Images are automatically optimized")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.7))
                }
            }
        }
    }
    
    // MARK: - Profile Form
    private var profileForm: some View {
        VStack(spacing: 16) {
            // Email (Read-only)
            VStack(alignment: .leading, spacing: 8) {
                Text("Email")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                HStack {
                    Text(authManager.currentUser?.email ?? "<EMAIL>")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)

                    Image(systemName: "lock.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Text("Email cannot be changed")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            // Display Name
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Display Name")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    if !canChangeDisplayName {
                        Image(systemName: "clock.fill")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.orange)
                    }
                }

                TextField("Enter your display name", text: $displayName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(!canChangeDisplayName)

                if let daysRemaining = authManager.currentUser?.daysUntilDisplayNameChange(), daysRemaining > 0 {
                    Text("You can change your display name in \(daysRemaining) day\(daysRemaining == 1 ? "" : "s")")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.orange)
                } else if canChangeDisplayName {
                    Text("You can change your display name once every 14 days")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            // Username
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Username")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    if !canChangeUsername {
                        Image(systemName: "checkmark.seal.fill")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.green)
                    }
                }

                TextField("Enter your username", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .disabled(!canChangeUsername)

                if canChangeUsername {
                    Text("Username can only be changed once")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                } else {
                    Text("Username has already been changed")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.green)
                }
            }

            // Bio
            VStack(alignment: .leading, spacing: 8) {
                Text("Bio")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                TextField("Tell us about yourself", text: $bio, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)

                Text("Share a bit about yourself with your friends")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
    }
    
    // MARK: - Privacy Section
    private var privacySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Privacy Settings")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                ForEach(PrivacySettings.ProfileVisibility.allCases, id: \.self) { privacy in
                    privacyOption(privacy)
                }
            }
        }
    }
    
    private func privacyOption(_ privacy: PrivacySettings.ProfileVisibility) -> some View {
        Button(action: { selectedPrivacy = privacy }) {
            HStack(spacing: 16) {
                Image(systemName: selectedPrivacy == privacy ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedPrivacy == privacy ? DesignSystem.Colors.sunriseOrange : DesignSystem.Colors.textSecondary)
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(privacy.displayName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(privacyDescription(for: privacy))
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(selectedPrivacy == privacy ? DesignSystem.Colors.sunriseOrange : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    private func privacyDescription(for privacy: PrivacySettings.ProfileVisibility) -> String {
        switch privacy {
        case .everyone:
            return "Anyone can see your profile"
        case .friends:
            return "Only your friends can see your profile"
        case .privateProfile:
            return "Your profile is completely private"
        }
    }

    private var canChangeDisplayName: Bool {
        authManager.currentUser?.canChangeDisplayName() ?? true
    }

    private var canChangeUsername: Bool {
        authManager.currentUser?.canChangeUsername() ?? true
    }

    private var isFormValid: Bool {
        let trimmedDisplayName = displayName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedUsername = username.trimmingCharacters(in: .whitespacesAndNewlines)

        // Basic validation
        guard !trimmedDisplayName.isEmpty && !trimmedUsername.isEmpty else {
            return false
        }

        // Check if changes are allowed
        let currentUser = authManager.currentUser
        let displayNameChanged = trimmedDisplayName != currentUser?.displayName
        let usernameChanged = trimmedUsername != currentUser?.username

        if displayNameChanged && !canChangeDisplayName {
            return false
        }

        if usernameChanged && !canChangeUsername {
            return false
        }

        return true
    }
    
    private func loadCurrentUserData() {
        guard let user = authManager.currentUser else { return }

        displayName = user.displayName
        username = user.username
        bio = user.bio ?? ""
        selectedPrivacy = user.privacySettings.profileVisibility
        originalAvatarURL = user.avatarURL
    }
    
    private func loadSelectedPhoto(_ item: PhotosPickerItem?) {
        guard let item = item else { return }

        _Concurrency.Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {

                // Validate image
                let validationResult = imageUploadManager.validateImage(image)

                await MainActor.run {
                    switch validationResult {
                    case .success:
                        avatarImage = image
                        hasAvatarChanged = true
                    case .failure(let error):
                        errorMessage = error.localizedDescription
                        showingErrorAlert = true
                    }
                }
            }
        }
    }
    
    private func saveProfile() async {
        guard let currentUser = authManager.currentUser else { return }

        isLoading = true

        let trimmedDisplayName = displayName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedUsername = username.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedBio = bio.trimmingCharacters(in: .whitespacesAndNewlines)

        // Create updated user
        var updatedUser = currentUser

        // Check if display name changed
        if trimmedDisplayName != currentUser.displayName {
            if !currentUser.canChangeDisplayName() {
                errorMessage = "You can only change your display name once every 14 days"
                showingErrorAlert = true
                isLoading = false
                return
            }
            updatedUser.displayName = trimmedDisplayName
            updatedUser.lastDisplayNameChange = Date()
        }

        // Check if username changed
        if trimmedUsername != currentUser.username {
            if !currentUser.canChangeUsername() {
                errorMessage = "Username can only be changed once"
                showingErrorAlert = true
                isLoading = false
                return
            }
            updatedUser.username = trimmedUsername
            updatedUser.usernameChanged = true
        }

        // Update other fields
        updatedUser.bio = trimmedBio.isEmpty ? nil : trimmedBio
        updatedUser.privacySettings.profileVisibility = selectedPrivacy
        updatedUser.updatedAt = Date()

        // Upload avatar image if changed
        if hasAvatarChanged, let newAvatarImage = avatarImage {
            do {
                // Delete old avatar if exists
                if let oldAvatarURL = originalAvatarURL, !oldAvatarURL.isEmpty {
                    try? await imageUploadManager.deleteAvatar(at: oldAvatarURL)
                }

                // Upload new avatar
                let newAvatarURL = try await imageUploadManager.uploadAvatar(newAvatarImage, for: currentUser.id)
                updatedUser.avatarURL = newAvatarURL

            } catch {
                errorMessage = "Failed to upload avatar: \(error.localizedDescription)"
                showingErrorAlert = true
                isLoading = false
                return
            }
        }

        let success = await authManager.updateProfile(user: updatedUser)

        isLoading = false

        if success {
            showingSuccessAlert = true
        } else {
            errorMessage = authManager.errorMessage ?? "Failed to update profile"
            showingErrorAlert = true
        }
    }
}

#Preview {
    EditProfileView()
}
