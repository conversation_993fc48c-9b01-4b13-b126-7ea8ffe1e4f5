import SwiftUI

struct MotivationSelectionView: View {
    let friend: Friend
    @ObservedObject var motivationManager: MotivationManager
    @Binding var currentUserPoints: Int
    @Binding var sentMotivations: [String: Int]
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedIcon: MotivationIcon?
    @State private var showingConfirmation = false
    @State private var showingSuccessAlert = false
    @State private var successMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Content
                ScrollView {
                    VStack(spacing: 20) {
                        // Friend Info
                        friendInfoSection
                        
                        // Your Points
                        pointsSection
                        
                        // Available Icons
                        iconsGridSection
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 20)
                }
            }
            .background(Color(.systemGroupedBackground))
        }
        .alert("Success!", isPresented: $showingSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text(successMessage)
        }
        .confirmationDialog(
            "Send Motivation",
            isPresented: $showingConfirmation,
            titleVisibility: .visible
        ) {
            Button("Send \(selectedIcon?.iconEmoji ?? "") (\(selectedIcon?.costPoints ?? 0) ⭐)") {
                sendSelectedMotivation()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Send \(selectedIcon?.iconEmoji ?? "") to \(friend.friendInfo?.name ?? "your friend") for \(selectedIcon?.costPoints ?? 0) points?")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Button("Cancel") {
                dismiss()
            }
            .foregroundColor(DesignSystem.Colors.sunriseOrange)
            
            Spacer()
            
            Text("Send Motivation")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Spacer()
            
            // Placeholder for balance
            Text("")
                .frame(width: 60)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Friend Info Section
    private var friendInfoSection: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(
                    LinearGradient(
                        colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 40, height: 40)
                .overlay(
                    Text(friend.friendInfo?.initials ?? "??")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Send motivation to")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text(friend.friendInfo?.name ?? "Unknown")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
            }
            
            Spacer()
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Points Section
    private var pointsSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Your Points")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("\(currentUserPoints) ⭐")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
            }
            
            Spacer()
            
            if currentUserPoints < 5 {
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Need more points?")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("Complete tasks to earn more!")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.trailing)
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Icons Grid Section
    private var iconsGridSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Choose Motivation")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(motivationManager.availableIcons) { icon in
                    MotivationIconButton(
                        icon: icon,
                        canAfford: currentUserPoints >= icon.costPoints,
                        isSelected: selectedIcon?.id == icon.id
                    ) {
                        selectedIcon = icon
                        showingConfirmation = true
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Actions
    private func sendSelectedMotivation() {
        guard let icon = selectedIcon,
              let friendId = friend.friendInfo?.id else { return }
        
        _Concurrency.Task {
            let success = await motivationManager.sendMotivation(to: friendId, iconId: icon.id)

            if success {
                // Update current user points
                do {
                    currentUserPoints = try await motivationManager.getCurrentUserPoints()
                } catch {
                    print("❌ Error updating points: \(error)")
                }

                // Update sent motivations count
                sentMotivations[icon.iconEmoji, default: 0] += 1

                successMessage = "Sent \(icon.iconEmoji) to \(friend.friendInfo?.name ?? "your friend")!"
                showingSuccessAlert = true
            } else {
                // Handle error - could show error alert
                print("❌ Failed to send motivation")
            }
        }
    }
}

// MARK: - Motivation Icon Button
struct MotivationIconButton: View {
    let icon: MotivationIcon
    let canAfford: Bool
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(icon.iconEmoji)
                    .font(.system(size: 24))
                
                Text("\(icon.costPoints) ⭐")
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(canAfford ? DesignSystem.Colors.sunriseOrange : DesignSystem.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                canAfford ? 
                (isSelected ? DesignSystem.Colors.sunriseOrange.opacity(0.2) : Color(.systemGray6)) :
                Color(.systemGray5)
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isSelected ? DesignSystem.Colors.sunriseOrange : Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .disabled(!canAfford)
        .buttonStyle(PlainButtonStyle())
    }
}
