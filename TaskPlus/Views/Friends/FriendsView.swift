//
//  FriendsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Friends Main View
struct FriendsView: View {
    @StateObject private var friendsManager = FriendsManager.shared
    @StateObject private var networkMonitor = NetworkMonitor.shared
    @State private var selectedTab = 0
    @State private var showingAddFriend = false
    @State private var showingFriendProfile = false
    @State private var selectedFriend: Friend?
    
    var body: some View {
        SwiftUI.Group {
            if networkMonitor.isConnected {
                // Online Mode - Normal Friends View
                VStack(spacing: 0) {
                    // Header
                    headerSection

                    // Tab Navigation
                    tabNavigationSection

                    // Content based on selected tab
                    VStack {
                        switch selectedTab {
                        case 0:
                            myFriendsTab
                        case 1:
                            friendRequestsTab
                        case 2:
                            findFriendsTab
                        default:
                            myFriendsTab
                        }
                    }
                }
            } else {
                // Offline Mode - Show offline message
                OfflineMessageView(
                    icon: "person.2.slash",
                    title: "Friends Unavailable Offline",
                    message: "Friends and social features require internet connection. Please connect to the internet to access your friends list."
                ) {
                    networkMonitor.checkConnection()
                }
            }
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .sheet(isPresented: $showingAddFriend) {
            AddFriendView()
        }
        .sheet(isPresented: $showingFriendProfile) {
            if let friend = selectedFriend {
                FriendProfileView(friend: friend)
            }
        }
        .onAppear {
            _Concurrency.Task {
                await friendsManager.refreshData()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        EmptyView()
    }
    
    // MARK: - Tab Navigation
    private var tabNavigationSection: some View {
        HStack(spacing: 0) {
            FriendsTabButton(
                title: "My Friends",
                icon: "person.2.fill",
                count: friendsManager.getFriends().count,
                isSelected: selectedTab == 0,
                action: { selectedTab = 0 }
            )
            
            FriendsTabButton(
                title: "Requests",
                icon: "envelope.fill",
                count: friendsManager.getPendingRequests().count,
                isSelected: selectedTab == 1,
                action: { selectedTab = 1 }
            )
            
            FriendsTabButton(
                title: "Find",
                icon: "magnifyingglass",
                count: nil,
                isSelected: selectedTab == 2,
                action: { selectedTab = 2 }
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - My Friends Tab
    private var myFriendsTab: some View {
        VStack(spacing: 0) {
            let friends = friendsManager.getFriends()
            
            if friends.isEmpty {
                // Empty State
                VStack(spacing: 20) {
                    Spacer()
                    
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        DesignSystem.Colors.sunriseOrange.opacity(0.1),
                                        DesignSystem.Colors.sunsetCoral.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 100, height: 100)

                        Image(systemName: "person.2.fill")
                            .font(.system(size: 40, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.sunriseOrange)
                    }
                    
                    VStack(spacing: 8) {
                        Text("No Friends Currently")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Text("You haven't added any friends yet. Use the Find tab to search for people to connect with.")
                            .font(.system(size: 15, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 28)
            } else {
                // Friends List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(friends, id: \.id) { friend in
                            FriendRowView(
                                friend: friend,
                                onTap: {
                                    selectedFriend = friend
                                    showingFriendProfile = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 8)
                    .padding(.bottom, 20)
                }
            }
        }
    }
    
    // MARK: - Friend Requests Tab
    private var friendRequestsTab: some View {
        FriendRequestsView()
    }
    
    // MARK: - Find Friends Tab
    private var findFriendsTab: some View {
        FindFriendsView()
    }
}

// MARK: - Friends Tab Button
struct FriendsTabButton: View {
    let title: String
    let icon: String
    let count: Int?
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                HStack(spacing: 4) {
                    Image(systemName: icon)
                        .font(.system(size: 14, weight: isSelected ? .semibold : .medium))
                    
                    if let count = count, count > 0 {
                        Text("\(count)")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemRed))
                            .cornerRadius(8)
                    }
                }
                .foregroundColor(isSelected ? DesignSystem.Colors.sunriseOrange : DesignSystem.Colors.textSecondary)

                Text(title)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? DesignSystem.Colors.sunriseOrange : DesignSystem.Colors.textSecondary)

                Rectangle()
                    .fill(isSelected ? DesignSystem.Colors.sunriseOrange : Color.clear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Friend Row View
struct FriendRowView: View {
    let friend: Friend
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Avatar with image support
                if let avatarURL = friend.friendInfo?.avatarUrl, !avatarURL.isEmpty {
                    AsyncImage(url: URL(string: avatarURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Circle()
                            .fill(Color(.systemGray5))
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.6)
                            )
                    }
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
                } else {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 50, height: 50)

                        Text(friend.friendInfo?.initials ?? "??")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                
                // Friend Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(friend.friendInfo?.name ?? "Unknown")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                        
                        // Online Status
                        HStack(spacing: 4) {
                            Circle()
                                .fill(friend.friendInfo?.isOnline == true ? Color(.systemGreen) : Color(.systemGray4))
                                .frame(width: 8, height: 8)
                            
                            Text(friend.friendInfo?.onlineStatus ?? "Offline")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    
                    Text(friend.friendInfo?.email ?? "")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                    
                    Text("Friends since \(friend.createdAt, style: .date)")
                        .font(.system(size: 11, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                // Chevron
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray6), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    FriendsView()
}
