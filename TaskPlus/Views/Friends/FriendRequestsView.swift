//
//  FriendRequestsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Friend Requests View
struct FriendRequestsView: View {
    @StateObject private var friendsManager = FriendsManager.shared
    @State private var processingRequests: Set<UUID> = []
    
    var body: some View {
        VStack(spacing: 0) {
            let pendingRequests = friendsManager.getPendingRequests()
            
            if pendingRequests.isEmpty {
                // Empty State
                VStack(spacing: 20) {
                    Spacer()
                    
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color(.systemOrange).opacity(0.1),
                                        Color(.systemYellow).opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 100, height: 100)
                        
                        Image(systemName: "envelope.open.fill")
                            .font(.system(size: 40, weight: .medium))
                            .foregroundColor(Color(.systemOrange))
                    }
                    
                    VStack(spacing: 8) {
                        Text("No Friend Requests")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text("When someone sends you a friend request, it will appear here.")
                            .font(.system(size: 15, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 28)
            } else {
                // Requests List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(pendingRequests, id: \.id) { request in
                            FriendRequestRowView(
                                request: request,
                                isProcessing: processingRequests.contains(request.id),
                                onAccept: {
                                    acceptRequest(request)
                                },
                                onDecline: {
                                    declineRequest(request)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 8)
                    .padding(.bottom, 20)
                }
            }
        }
    }
    
    // MARK: - Actions
    private func acceptRequest(_ request: FriendRequest) {
        processingRequests.insert(request.id)
        
        _Concurrency.Task {
            let success = await friendsManager.acceptFriendRequest(request)
            
            await MainActor.run {
                processingRequests.remove(request.id)
                if !success {
                    // Handle error - could show alert
                }
            }
        }
    }
    
    private func declineRequest(_ request: FriendRequest) {
        processingRequests.insert(request.id)
        
        _Concurrency.Task {
            let success = await friendsManager.declineFriendRequest(request)
            
            await MainActor.run {
                processingRequests.remove(request.id)
                if !success {
                    // Handle error - could show alert
                }
            }
        }
    }
}

// MARK: - Friend Request Row View
struct FriendRequestRowView: View {
    let request: FriendRequest
    let isProcessing: Bool
    let onAccept: () -> Void
    let onDecline: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // Avatar with image support
                if let avatarURL = request.senderInfo?.avatarUrl, !avatarURL.isEmpty {
                    AsyncImage(url: URL(string: avatarURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Circle()
                            .fill(Color(.systemGray5))
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.6)
                            )
                    }
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
                } else {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color(.systemGreen), Color(.systemTeal)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 50, height: 50)

                        Text(request.senderInfo?.initials ?? "??")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                
                // Request Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(request.senderInfo?.name ?? "Unknown")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(request.senderInfo?.email ?? "")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                    
                    Text("Sent \(request.createdAt, style: .relative)")
                        .font(.system(size: 11, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
            
            // Message (if any)
            if let message = request.message, !message.isEmpty {
                HStack {
                    Text("💬 \"\(message)\"")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.text)
                        .italic()
                        .lineLimit(2)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            
            // Action Buttons
            HStack(spacing: 12) {
                // Decline Button
                Button(action: onDecline) {
                    HStack(spacing: 6) {
                        if isProcessing {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "xmark")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        
                        Text("Decline")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .foregroundColor(Color(.systemRed))
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(Color(.systemRed).opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color(.systemRed).opacity(0.3), lineWidth: 1)
                    )
                }
                .disabled(isProcessing)
                
                // Accept Button
                Button(action: onAccept) {
                    HStack(spacing: 6) {
                        if isProcessing {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        
                        Text("Accept")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        LinearGradient(
                            colors: [
                                Color(.systemGreen),
                                Color(.systemTeal)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(8)
                }
                .disabled(isProcessing)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray6), lineWidth: 1)
        )
    }
}

#Preview {
    FriendRequestsView()
}
