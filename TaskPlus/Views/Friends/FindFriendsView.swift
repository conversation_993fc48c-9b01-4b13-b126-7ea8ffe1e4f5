//
//  FindFriendsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Find Friends View
struct FindFriendsView: View {
    @StateObject private var friendsManager = FriendsManager.shared
    @State private var searchText = ""
    @State private var sentRequests: Set<UUID> = []
    @State private var showingSuccessAlert = false
    @State private var successMessage = ""
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""

    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Search Bar
            searchBar

            // Content
            if friendsManager.isLoading {
                // Loading State
                loadingState
            } else if !friendsManager.searchResults.isEmpty {
                // Search Results
                searchResults
            } else if !searchText.isEmpty && friendsManager.searchResults.isEmpty {
                // No Results for search
                noResultsState
            } else {
                // Search Prompt - default state
                searchPrompt
            }
        }
        .alert("Friend Request Sent", isPresented: $showingSuccessAlert) {
            Button("OK") { }
        } message: {
            Text(successMessage)
        }
        .alert("Already Sent", isPresented: $showingErrorAlert) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Search Bar
    private var searchBar: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                TextField("Enter @username to search...", text: $searchText)
                    .focused($isSearchFocused)
                    .font(.system(size: 15, weight: .regular))
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .onSubmit {
                        performSearch()
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        performSearch()
                    }) {
                        Text("Search")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                LinearGradient(
                                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(8)
                    }

                    Button(action: {
                        searchText = ""
                        isSearchFocused = false
                        friendsManager.searchResults = []
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSearchFocused ? Color(.systemBlue) : Color(.systemGray5), lineWidth: 1)
            )
            
            // Search Tips
            if searchText.isEmpty {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .font(.system(size: 12))
                        .foregroundColor(Color(.systemYellow))
                    
                    Text("Tip: Enter exact @username and press Search")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Search Prompt
    private var searchPrompt: some View {
        VStack(spacing: 20) {
            Spacer()
            
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.sunriseOrange.opacity(0.1),
                                DesignSystem.Colors.sunsetCoral.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)

                Image(systemName: "person.crop.circle.badge.plus")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
            }
            
            VStack(spacing: 8) {
                Text("Find New Friends")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Enter the exact @username of the person you want to add as a friend.")
                    .font(.system(size: 15, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            Button(action: {
                isSearchFocused = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 14, weight: .semibold))
                    Text("Start Searching")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    LinearGradient(
                        colors: [
                            DesignSystem.Colors.sunriseOrange,
                            DesignSystem.Colors.sunsetCoral
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
            }
            .buttonStyle(PressedButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, 28)
    }
    
    // MARK: - Loading State
    private var loadingState: some View {
        VStack(spacing: 16) {
            Spacer()
            
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Searching...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Spacer()
        }
    }
    
    // MARK: - No Results State
    private var noResultsState: some View {
        VStack(spacing: 16) {
            Spacer()
            
            Image(systemName: "person.crop.circle.badge.questionmark")
                .font(.system(size: 48))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("No Users Found")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Text("Make sure you entered the correct @username.")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
            
            Spacer()
        }
        .padding(.horizontal, 28)
    }
    
    // MARK: - Search Results
    private var searchResults: some View {
        VStack(spacing: 0) {
            // Results Header
            HStack {
                Text("Search Results")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                Spacer()

                Text("\(friendsManager.searchResults.count) found")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))

            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(friendsManager.searchResults, id: \.id) { user in
                        UserSearchResultRow(
                            user: user,
                            friendshipStatus: friendsManager.getFriendshipStatus(with: user.id),
                            onSendRequest: {
                                sendFriendRequest(to: user)
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 12)
                .padding(.bottom, 20)
            }
        }
    }
    
    // MARK: - Actions
    private func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let cleanQuery = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        let searchQuery = cleanQuery.hasPrefix("@") ? String(cleanQuery.dropFirst()) : cleanQuery

        _Concurrency.Task {
            await friendsManager.searchUsers(query: searchQuery)
        }

        isSearchFocused = false
    }

    private func searchUsers(query: String) {
        _Concurrency.Task {
            await friendsManager.searchUsers(query: query)
        }
    }
    
    private func sendFriendRequest(to user: UserInfo) {
        print("🚀 Sending friend request to user: \(user.username) (ID: \(user.id))")
        sentRequests.insert(user.id)

        _Concurrency.Task {
            print("🔄 Starting friend request task...")
            let success = await friendsManager.sendFriendRequest(to: user.id, message: "Hi! Let's be friends!")
            print("✅ Friend request result: \(success)")

            await MainActor.run {
                if !success {
                    print("❌ Friend request failed, removing from sent requests")
                    sentRequests.remove(user.id)

                    // Check if it's a duplicate request error
                    if friendsManager.lastError?.contains("duplicate key") == true {
                        errorMessage = "Friend request already sent to @\(user.username) previously."
                        showingErrorAlert = true
                    } else {
                        errorMessage = "Failed to send friend request. Please try again."
                        showingErrorAlert = true
                    }
                } else {
                    print("✅ Friend request sent successfully!")
                    successMessage = "Friend request sent to @\(user.username) successfully!"
                    showingSuccessAlert = true

                    // Update the UI to show "Sent" state
                    sentRequests.insert(user.id)
                }
            }
        }
    }
}

// MARK: - User Search Result Row
struct UserSearchResultRow: View {
    let user: UserInfo
    let friendshipStatus: Friend.FriendshipStatus?
    @State private var hasSentRequest: Bool = false
    let onSendRequest: () -> Void

    @StateObject private var friendsManager = FriendsManager.shared
    
    var body: some View {
        HStack(spacing: 16) {
            // Avatar with image support
            if let avatarURL = user.avatarUrl, !avatarURL.isEmpty {
                AsyncImage(url: URL(string: avatarURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.5)
                        )
                }
                .frame(width: 44, height: 44)
                .clipShape(Circle())
            } else {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)

                    Text(user.initials)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            
            // User Info
            VStack(alignment: .leading, spacing: 4) {
                Text("@\(user.username)")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
            }
            
            Spacer()
            
            // Action Button
            actionButton
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray6), lineWidth: 1)
        )
        .onAppear {
            _Concurrency.Task {
                hasSentRequest = await friendsManager.hasSentRequest(to: user.id)
            }
        }
    }
    
    @ViewBuilder
    private var actionButton: some View {
        switch friendshipStatus {
        case .accepted:
            // Already Friends
            HStack(spacing: 4) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 12))
                Text("Friends")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(Color(.systemGreen))
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color(.systemGreen).opacity(0.1))
            .cornerRadius(8)
            
        case .pending:
            // Request Pending
            HStack(spacing: 4) {
                Image(systemName: "clock")
                    .font(.system(size: 12))
                Text("Pending")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(Color(.systemOrange))
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color(.systemOrange).opacity(0.1))
            .cornerRadius(8)
            
        case .blocked, .declined:
            // Cannot send request
            EmptyView()
            
        case .none:
            // Can send request
            if hasSentRequest {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .semibold))
                    Text("Sent")
                        .font(.system(size: 12, weight: .medium))
                }
                .foregroundColor(Color(.systemGray))
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            } else {
                Button(action: {
                    print("🔘 Add button tapped for user: \(user.username)")
                    onSendRequest()

                    // Update local state immediately for better UX
                    _Concurrency.Task {
                        hasSentRequest = await friendsManager.hasSentRequest(to: user.id)
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "person.badge.plus")
                            .font(.system(size: 14, weight: .semibold))
                        Text("Add")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(10)
                    .shadow(color: DesignSystem.Colors.sunriseOrange.opacity(0.3), radius: 2, x: 0, y: 1)
                }
                .buttonStyle(PressedButtonStyle())
            }
        }
    }
}

#Preview {
    FindFriendsView()
}
