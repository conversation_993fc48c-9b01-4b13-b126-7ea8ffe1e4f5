//
//  AddFriendView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Add Friend View
struct AddFriendView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var friendsManager = FriendsManager.shared
    
    @State private var searchText = ""
    @State private var message = ""
    @State private var isSending = false
    @State private var showingSuccess = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    @FocusState private var isSearchFocused: Bool
    @FocusState private var isMessageFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 24) {
                    // Search Section
                    searchSection
                    
                    // Message Section
                    messageSection
                    
                    // Send Button
                    sendButton
                    
                    // Quick Add Section
                    quickAddSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .alert("Friend Request Sent!", isPresented: $showingSuccess) {
            <PERSON><PERSON>("OK") { dismiss() }
        } message: {
            Text("Your friend request has been sent successfully.")
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Top Bar
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Friends")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color(.systemBlue))
                }
                
                Spacer()
                
                Text("Add Friend")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                // Placeholder for balance
                Color.clear
                    .frame(width: 80, height: 20)
            }
            
            // Icon and Description
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(.systemBlue).opacity(0.1),
                                    Color(.systemPurple).opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "person.badge.plus.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(Color(.systemBlue))
                }
                
                VStack(spacing: 4) {
                    Text("Connect with Friends")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Send a friend request by email")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Search Section
    private var searchSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📧 Friend's Email")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Email Address")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                TextField("Enter friend's email address", text: $searchText)
                    .focused($isSearchFocused)
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .font(.system(size: 16, weight: .regular))
                    .padding(12)
                    .background(Color(.systemBackground))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(isSearchFocused ? Color(.systemBlue) : Color(.systemGray4), lineWidth: 1)
                    )
            }
        }
    }
    
    // MARK: - Message Section
    private var messageSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("💬 Personal Message (Optional)")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Add a personal touch to your request")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                TextField("Hi! Let's be friends on TaskMate!", text: $message, axis: .vertical)
                    .focused($isMessageFocused)
                    .font(.system(size: 16, weight: .regular))
                    .lineLimit(3...6)
                    .padding(12)
                    .background(Color(.systemBackground))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(isMessageFocused ? Color(.systemBlue) : Color(.systemGray4), lineWidth: 1)
                    )
            }
        }
    }
    
    // MARK: - Send Button
    private var sendButton: some View {
        Button(action: sendFriendRequest) {
            HStack(spacing: 10) {
                if isSending {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(.white)
                } else {
                    Image(systemName: "paperplane.fill")
                        .font(.system(size: 16, weight: .semibold))
                }
                
                Text(isSending ? "Sending Request..." : "Send Friend Request")
                    .font(.system(size: 16, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                LinearGradient(
                    colors: [
                        Color(.systemBlue),
                        Color(.systemPurple)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
            .shadow(color: Color(.systemBlue).opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .disabled(searchText.isEmpty || isSending)
        .opacity(searchText.isEmpty ? 0.6 : 1.0)
        .buttonStyle(PressedButtonStyle())
    }
    
    // MARK: - Quick Add Section
    private var quickAddSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🔍 Or Search for People")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Button(action: {
                // Switch to Find Friends tab
                dismiss()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "magnifyingglass.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(Color(.systemIndigo))
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Browse and Search")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text("Find people by name or email")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(16)
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray6), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Actions
    private func sendFriendRequest() {
        guard !searchText.isEmpty else { return }
        
        isSending = true
        
        _Concurrency.Task {
            // For now, we'll simulate finding the user by email
            // In real implementation, this would search for user by email first
            let mockUserId = UUID() // This would be the actual user ID found by email
            
            let success = await friendsManager.sendFriendRequest(
                to: mockUserId,
                message: message.isEmpty ? nil : message
            )
            
            await MainActor.run {
                isSending = false
                if success {
                    showingSuccess = true
                } else {
                    errorMessage = "Failed to send friend request. Please try again."
                    showingError = true
                }
            }
        }
    }
}

#Preview {
    AddFriendView()
}
