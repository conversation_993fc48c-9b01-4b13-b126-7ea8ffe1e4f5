//
//  InviteMemberView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Invite Member View
struct InviteMemberView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared
    
    @State private var selectedMethod: InviteMethod = .friends
    @State private var emailAddress = ""
    @State private var isInviting = false
    @State private var showingSuccess = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var selectedFriends: Set<UUID> = []

    @StateObject private var friendsManager = FriendsManager.shared
    @FocusState private var isEmailFocused: Bool
    
    enum InviteMethod: String, CaseIterable {
        case friends = "Add Friends"
        case email = "Email Invitation"
        case code = "Share Group Code"

        var icon: String {
            switch self {
            case .friends: return "person.2.fill"
            case .email: return "envelope.fill"
            case .code: return "link"
            }
        }

        var description: String {
            switch self {
            case .friends: return "Invite friends from your friends list"
            case .email: return "Send a direct invitation via email"
            case .code: return "Share the group code for others to join"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 24) {
                    // Invite Methods
                    inviteMethodsSection
                    
                    // Content based on selected method
                    switch selectedMethod {
                    case .friends:
                        friendsInviteSection
                    case .email:
                        emailInviteSection
                    case .code:
                        codeShareSection
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .alert("Invitation Sent!", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("The invitation has been sent successfully.")
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Top Bar
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Members")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color(.systemBlue))
                }
                
                Spacer()
                
                Text("Invite Members")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                // Placeholder for balance
                Color.clear
                    .frame(width: 80, height: 20)
            }
            
            // Group Info
            HStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemGreen), Color(.systemTeal)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)
                    
                    Text(String(group.name.prefix(1)).uppercased())
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(group.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Invite people to join")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Invite Methods Section
    private var inviteMethodsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📧 Invitation Method")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                ForEach(InviteMethod.allCases, id: \.self) { method in
                    InviteMethodRow(
                        method: method,
                        isSelected: selectedMethod == method,
                        action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedMethod = method
                            }
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Email Invite Section
    private var emailInviteSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("✉️ Email Invitation")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                // Email Input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Email Address")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    TextField("Enter email address", text: $emailAddress)
                        .focused($isEmailFocused)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .font(.system(size: 16, weight: .regular))
                        .padding(12)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(isEmailFocused ? Color(.systemBlue) : Color(.systemGray4), lineWidth: 1)
                        )
                }
                
                // Send Button
                Button(action: sendEmailInvitation) {
                    HStack(spacing: 10) {
                        if isInviting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "paperplane.fill")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        
                        Text(isInviting ? "Sending..." : "Send Invitation")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [
                                Color(.systemBlue),
                                Color(.systemPurple)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: Color(.systemBlue).opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .disabled(emailAddress.isEmpty || isInviting)
                .opacity(emailAddress.isEmpty ? 0.6 : 1.0)
                .buttonStyle(PressedButtonStyle())
            }
        }
    }
    
    // MARK: - Code Share Section
    private var codeShareSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🔗 Share Group Code")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 16) {
                // Group Code Display
                VStack(spacing: 12) {
                    Text("Group Code")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    HStack {
                        Text(group.id.uuidString.prefix(8).uppercased())
                            .font(.system(size: 24, weight: .bold, design: .monospaced))
                            .foregroundColor(Color(.systemBlue))
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color(.systemBlue).opacity(0.1))
                            .cornerRadius(12)
                        
                        Button(action: copyGroupCode) {
                            Image(systemName: "doc.on.doc.fill")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                                .background(Color(.systemBlue))
                                .cornerRadius(12)
                        }
                        .buttonStyle(PressedButtonStyle())
                    }
                }
                
                // Share Button
                Button(action: shareGroupCode) {
                    HStack(spacing: 10) {
                        Image(systemName: "square.and.arrow.up.fill")
                            .font(.system(size: 16, weight: .semibold))
                        
                        Text("Share Code")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [
                                Color(.systemGreen),
                                Color(.systemTeal)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: Color(.systemGreen).opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .buttonStyle(PressedButtonStyle())
                
                // Instructions
                VStack(alignment: .leading, spacing: 8) {
                    Text("How to use:")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("1. Share this code with people you want to invite")
                        Text("2. They can join by entering the code in the app")
                        Text("3. The code is unique to this group")
                    }
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(12)
                .background(Color(.systemBackground))
                .cornerRadius(10)
            }
        }
    }

    // MARK: - Friends Invite Section
    private var friendsInviteSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("👥 Add Friends")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)

            let availableFriends = friendsManager.getAvailableFriendsForGroup(group.id)

            if availableFriends.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "person.2.slash")
                        .font(.system(size: 32))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("No Available Friends")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Text("All your friends are already members of this group, or you don't have any friends yet.")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
                .background(Color(.systemBackground))
                .cornerRadius(12)
            } else {
                VStack(spacing: 12) {
                    // Header with selection count
                    HStack {
                        Text("Select Friends to Invite")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Spacer()

                        Text("\(selectedFriends.count)/\(availableFriends.count)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    // Friends List
                    VStack(spacing: 8) {
                        ForEach(availableFriends, id: \.id) { friend in
                            FriendInviteRow(
                                friend: friend,
                                isSelected: selectedFriends.contains(friend.friendId),
                                onToggle: {
                                    if selectedFriends.contains(friend.friendId) {
                                        selectedFriends.remove(friend.friendId)
                                    } else {
                                        selectedFriends.insert(friend.friendId)
                                    }
                                }
                            )
                        }
                    }

                    // Invite Button
                    Button(action: inviteSelectedFriends) {
                        HStack(spacing: 10) {
                            if isInviting {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            } else {
                                Image(systemName: "person.badge.plus.fill")
                                    .font(.system(size: 16, weight: .semibold))
                            }

                            Text(isInviting ? "Inviting..." : "Invite Selected Friends")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(
                            LinearGradient(
                                colors: [
                                    Color(.systemGreen),
                                    Color(.systemTeal)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(12)
                        .shadow(color: Color(.systemGreen).opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .disabled(selectedFriends.isEmpty || isInviting)
                    .opacity(selectedFriends.isEmpty ? 0.6 : 1.0)
                    .buttonStyle(PressedButtonStyle())
                }
            }
        }
    }

    // MARK: - Actions
    private func inviteSelectedFriends() {
        guard !selectedFriends.isEmpty else { return }

        isInviting = true

        _Concurrency.Task {
            let success = await friendsManager.inviteFriendsToGroup(Array(selectedFriends), groupId: group.id)

            await MainActor.run {
                isInviting = false
                if success {
                    showingSuccess = true
                } else {
                    errorMessage = "Failed to invite friends. Please try again."
                    showingError = true
                }
            }
        }
    }

    private func sendEmailInvitation() {
        guard !emailAddress.isEmpty else { return }
        
        isInviting = true
        
        // TODO: Implement email invitation logic
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isInviting = false
            showingSuccess = true
        }
    }
    
    private func copyGroupCode() {
        UIPasteboard.general.string = String(group.id.uuidString.prefix(8).uppercased())
        // TODO: Show toast notification
    }
    
    private func shareGroupCode() {
        let shareText = "Join my group '\(group.name)' using code: \(String(group.id.uuidString.prefix(8).uppercased()))"
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

// MARK: - Invite Method Row
struct InviteMethodRow: View {
    let method: InviteMemberView.InviteMethod
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: method.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(method.rawValue)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(method.description)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color(.systemBlue) : Color(.systemGray6), lineWidth: isSelected ? 2 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Friend Invite Row
struct FriendInviteRow: View {
    let friend: Friend
    let isSelected: Bool
    let onToggle: () -> Void

    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: 12) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemBlue), Color(.systemPurple)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)

                    Text(friend.friendInfo?.initials ?? "??")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }

                // Friend Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(friend.friendInfo?.name ?? "Unknown")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Spacer()

                        // Online Status
                        HStack(spacing: 4) {
                            Circle()
                                .fill(friend.friendInfo?.isOnline == true ? Color(.systemGreen) : Color(.systemGray4))
                                .frame(width: 8, height: 8)

                            Text(friend.friendInfo?.onlineStatus ?? "Offline")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }

                    Text(friend.friendInfo?.email ?? "")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }

                // Selection Indicator
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)
            }
            .padding(12)
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? Color(.systemBlue) : Color(.systemGray6), lineWidth: isSelected ? 2 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    InviteMemberView(group: Group.sampleGroups(for: UUID()).first!)
}
