//
//  GroupDetailView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Group Detail View
struct GroupDetailView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared
    @State private var selectedTab = 0  // 0: Dashboard, 1: Tasks, 2: Members, 3: Invitations, 4: Settings
    @State private var showingCreateTask = false
    @State private var showingInviteMember = false

    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection

            // Tab Selector
            tabSelector

            // Content based on selected tab
            VStack {
                switch selectedTab {
                case 0:
                    dashboardTab
                case 1:
                    tasksTab
                case 2:
                    membersTab
                case 3:
                    invitationsTab
                case 4:
                    settingsTab
                default:
                    dashboardTab
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingCreateTask) {
            CreateGroupTaskView(group: group)
        }
        .sheet(isPresented: $showingInviteMember) {
            InviteMemberView(group: group)
        }
        .onAppear {
            loadGroupDataIfNeeded()
        }
    }

    // MARK: - Helper Methods
    private func loadGroupDataIfNeeded() {
        // جلب مهام المجموعة إذا لم تكن محملة
        let groupTasks = groupManager.getGroupTasks(group.id)
        if groupTasks.isEmpty {
            _Concurrency.Task {
                do {
                    let tasks = try await SupabaseManager.shared.fetchGroupTasks(for: group.id)

                    await MainActor.run {
                        // إضافة المهام للقائمة العامة
                        groupManager.groupTasks.append(contentsOf: tasks)
                    }

                    print("✅ Loaded \(tasks.count) tasks for group: \(group.name)")
                } catch {
                    print("❌ Failed to load tasks for group \(group.name): \(error)")
                }
            }
        }
    }

    // MARK: - Progress Overview Section
    private var progressOverviewSection: some View {
        let tasks = groupManager.getGroupTasks(group.id)
        let totalTasks = tasks.count
        let completedTasks = tasks.filter { $0.isFullyCompleted }.count
        let overallProgress = totalTasks > 0 ? Double(completedTasks) / Double(totalTasks) : 0.0

        return VStack(spacing: 12) {
            HStack {
                Text("Overall Progress")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                Spacer()

                Text("\(Int(overallProgress * 100))%")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(Color(.systemGreen))
            }

            ProgressView(value: overallProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: Color(.systemGreen)))
                .scaleEffect(y: 1.2)

            // Recent Activity
            if !tasks.isEmpty {
                HStack {
                    Text("Recent Activity")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    if let latestTask = tasks.sorted(by: { $0.updatedAt > $1.updatedAt }).first {
                        Text("Last updated: \(latestTask.updatedAt, style: .relative)")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(12)
        .padding(.horizontal, 4)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Top Bar with Back Button
            HStack {
                // Custom Back Button
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Groups")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color(.systemBlue))
                }

                Spacer()

                // Refresh Button (for Dashboard)
                if selectedTab == 0 {
                    Button(action: {
                        _Concurrency.Task {
                            await groupManager.refreshData()
                        }
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(.systemBlue))
                    }
                }
            }
            
            // Compact Group Info
            HStack(spacing: 12) {
                // Smaller Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemGreen), Color(.systemTeal)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                        .shadow(color: Color(.systemGreen).opacity(0.3), radius: 4, x: 0, y: 2)

                    Text(String(group.name.prefix(1)).uppercased())
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }

                // Name and Description
                VStack(alignment: .leading, spacing: 2) {
                    HStack(spacing: 6) {
                        Text(group.name)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.text)

                        if group.isPrivate {
                            Image(systemName: "lock.fill")
                                .font(.system(size: 10))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }

                    if let description = group.description, !description.isEmpty {
                        Text(description)
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(1)
                    }
                }

                Spacer()
            }
                
            // Quick Stats (Minimal)
            HStack(spacing: 20) {
                QuickStatItem(
                    icon: "list.bullet",
                    value: "\(groupManager.getGroupTasks(group.id).count)",
                    color: .blue
                )

                QuickStatItem(
                    icon: "checkmark.circle.fill",
                    value: "\(groupManager.getGroupTasks(group.id).filter { $0.isFullyCompleted }.count)",
                    color: .green
                )

                QuickStatItem(
                    icon: "person.2.fill",
                    value: "\(groupManager.getGroupMembers(group.id).count)",
                    color: .purple
                )

                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 8)
    }
    

    // MARK: - Tab Selector
    private var tabSelector: some View {
        // استخدام SupabaseManager للحصول على ID المستخدم المصادق عليه
        let currentUserId = SupabaseManager.shared.authenticatedUserId ?? UUID()
        let isOwner = group.isOwner(currentUserId)

        // Debug information
        print("🔍 GroupDetailView Debug:")
        print("  - Group: \(group.name)")
        print("  - Group Owner ID: \(group.ownerId)")
        print("  - Supabase User ID: \(currentUserId)")
        print("  - DataManager User ID: \(DataManager.shared.currentUser?.id.uuidString ?? "nil")")
        print("  - Is Owner: \(isOwner)")

        return HStack(spacing: 0) {
            TabButton(title: "Dashboard", icon: "chart.bar.fill", isSelected: selectedTab == 0) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 0
                }
            }

            TabButton(title: "Tasks", icon: "list.bullet", isSelected: selectedTab == 1) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 1
                }
            }

            TabButton(title: "Members", icon: "person.2.fill", isSelected: selectedTab == 2) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 2
                }
            }

            // Invitations tab - only visible to owner
            if isOwner {
                TabButton(title: "Invitations", icon: "envelope.fill", isSelected: selectedTab == 3) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = 3
                    }
                }
            }

            // Settings tab - only visible to owner
            if isOwner {
                TabButton(title: "Settings", icon: "gearshape.fill", isSelected: selectedTab == 4) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = 4
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 4)
    }

    // MARK: - Tabs Content
    private var dashboardTab: some View {
        GroupDashboardContentView(group: group)
    }

    private var tasksTab: some View {
        GroupTasksView(group: group, onCreateTask: {
            showingCreateTask = true
        })
    }

    private var membersTab: some View {
        EnhancedGroupMembersView(group: group, onInviteMember: {
            showingInviteMember = true
        })
    }

    private var invitationsTab: some View {
        GroupInvitationsView(group: group)
    }

    private var settingsTab: some View {
        GroupSettingsView(group: group)
    }
}

// MARK: - Helper Views
struct StatItem: View {
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(color)

            Text(label)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
}

struct CompactStatItem: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(color)

            VStack(alignment: .leading, spacing: 1) {
                Text(value)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(color)

                Text(label)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

struct QuickStatItem: View {
    let icon: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(color)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

struct TabButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)

                Text(title)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)

                Rectangle()
                    .fill(isSelected ? Color(.systemBlue) : Color.clear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Group Tasks View
struct GroupTasksView: View {
    let group: Group
    let onCreateTask: () -> Void
    @StateObject private var groupManager = GroupManager.shared

    private var groupTasks: [GroupTask] {
        groupManager.getGroupTasks(group.id)
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack(alignment: .center) {
                Text("Tasks")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)

                Spacer()

                // Task Stats
                HStack(spacing: 12) {
                    // Total Tasks
                    HStack(spacing: 4) {
                        Image(systemName: "list.bullet")
                            .font(.system(size: 11, weight: .medium))
                        Text("\(groupTasks.count)")
                            .font(.system(size: 13, weight: .semibold))
                    }
                    .foregroundColor(Color(.systemBlue))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemBlue).opacity(0.1))
                    .cornerRadius(6)

                    // Completed Tasks
                    if !groupTasks.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 11, weight: .medium))
                            Text("\(groupTasks.filter { $0.isFullyCompleted }.count)")
                                .font(.system(size: 13, weight: .semibold))
                        }
                        .foregroundColor(Color(.systemGreen))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGreen).opacity(0.1))
                        .cornerRadius(6)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)

            // Tasks List or Empty State
            if groupTasks.isEmpty {
                VStack(spacing: 20) {
                    Spacer()

                    // Animated Icon
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color(.systemGreen).opacity(0.1),
                                        Color(.systemTeal).opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 100, height: 100)

                        Image(systemName: "list.bullet.clipboard.fill")
                            .font(.system(size: 40, weight: .medium))
                            .foregroundColor(Color(.systemGreen))
                    }

                    VStack(spacing: 8) {
                        Text("No Tasks Yet")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Text("Create your first group task to start collaborating!")
                            .font(.system(size: 15, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }

                    Button(action: onCreateTask) {
                        HStack(spacing: 10) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 18, weight: .semibold))
                            Text("Create First Task")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 28)
                        .padding(.vertical, 14)
                        .background(
                            LinearGradient(
                                colors: [
                                    Color(.systemGreen),
                                    Color(.systemGreen).opacity(0.9),
                                    Color(.systemTeal)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(18)
                        .shadow(color: Color(.systemGreen).opacity(0.4), radius: 10, x: 0, y: 5)
                    }
                    .buttonStyle(PressedButtonStyle())

                    Spacer()
                }
                .padding(.horizontal, 28)
            } else {
                ZStack {
                    // Tasks List
                    ScrollView {
                        LazyVStack(spacing: 10) {
                            ForEach(groupTasks) { task in
                                GroupTaskRowView(task: task, group: group)
                            }
                        }
                        .padding(.horizontal, 18)
                        .padding(.top, 4)
                        .padding(.bottom, 90) // Space for FAB
                    }

                    // Floating Action Button
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()

                            Button(action: onCreateTask) {
                                Image(systemName: "plus")
                                    .font(.system(size: 22, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(width: 60, height: 60)
                                    .background(
                                        LinearGradient(
                                            colors: [
                                                Color(.systemGreen),
                                                Color(.systemTeal)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .clipShape(Circle())
                                    .shadow(color: Color(.systemGreen).opacity(0.5), radius: 12, x: 0, y: 6)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                    )
                            }
                            .buttonStyle(PressedButtonStyle())
                            .padding(.trailing, 24)
                            .padding(.bottom, 32)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Group Task Row View
struct GroupTaskRowView: View {
    let task: GroupTask
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    @State private var isCompleting = false

    private var currentUserId: UUID? {
        SupabaseManager.shared.authenticatedUserId
    }

    private var canComplete: Bool {
        guard let userId = currentUserId else { return false }

        switch task.taskType {
        case .groupTask:
            // في المهام الجماعية، أي عضو يمكنه الإكمال
            return group.isMember(userId) && !task.isCompleted(by: userId)
        case .individualTask(let assigneeId):
            // في المهام الفردية، فقط المُعيَّن له يمكنه الإكمال
            return userId == assigneeId && !task.isCompleted(by: userId)
        }
    }

    private var canUncomplete: Bool {
        guard let userId = currentUserId else { return false }
        return task.isCompleted(by: userId)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // Task Header
            HStack(spacing: 12) {
                // Completion Circle (like regular tasks)
                Button(action: {
                    if canComplete {
                        completeTask()
                    } else if canUncomplete {
                        uncompleteTask()
                    }
                }) {
                    ZStack {
                        Circle()
                            .stroke(
                                task.isCompleted(by: currentUserId ?? UUID()) ?
                                Color(.systemGreen) : Color(.systemGray4),
                                lineWidth: 2
                            )
                            .frame(width: 24, height: 24)

                        if isCompleting {
                            ProgressView()
                                .scaleEffect(0.6)
                        } else if task.isCompleted(by: currentUserId ?? UUID()) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(Color(.systemGreen))
                        }
                    }
                }
                .disabled(isCompleting || (!canComplete && !canUncomplete))

                // Task Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                        .lineLimit(2)
                        .strikethrough(task.isCompleted(by: currentUserId ?? UUID()), color: DesignSystem.Colors.textSecondary)

                    if let description = task.description, !description.isEmpty {
                        Text(description)
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                // Priority Badge
                Text(task.priority.displayName)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(priorityColor(task.priority))
                    .cornerRadius(6)
            }

            // Progress Section
            VStack(alignment: .leading, spacing: 6) {
                HStack(alignment: .center) {
                    // Assignment Type Badge
                    HStack(spacing: 4) {
                        Image(systemName: task.taskType.isGroupTask ? "person.3.fill" : "person.fill")
                            .font(.system(size: 10, weight: .medium))
                        Text(task.taskType.isGroupTask ? "Group" : "Individual")
                            .font(.system(size: 11, weight: .medium))
                    }
                    .foregroundColor(task.taskType.isGroupTask ? Color(.systemBlue) : Color(.systemPurple))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background((task.taskType.isGroupTask ? Color(.systemBlue) : Color(.systemPurple)).opacity(0.1))
                    .cornerRadius(4)

                    Spacer()

                    // Progress Counter
                    if task.taskType.isGroupTask {
                        HStack(spacing: 4) {
                            Image(systemName: "chart.bar.fill")
                                .font(.system(size: 10, weight: .medium))
                            Text(task.groupProgress)
                                .font(.system(size: 12, weight: .bold))
                        }
                        .foregroundColor(Color(.systemGreen))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(Color(.systemGreen).opacity(0.1))
                        .cornerRadius(6)
                    } else {
                        HStack(spacing: 4) {
                            Image(systemName: task.isIndividualCompleted ? "checkmark.circle.fill" : "clock.fill")
                                .font(.system(size: 10, weight: .medium))
                            Text(task.isIndividualCompleted ? "Done" : "Pending")
                                .font(.system(size: 11, weight: .semibold))
                        }
                        .foregroundColor(task.isIndividualCompleted ? Color(.systemGreen) : Color(.systemOrange))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background((task.isIndividualCompleted ? Color(.systemGreen) : Color(.systemOrange)).opacity(0.1))
                        .cornerRadius(6)
                    }
                }

                // Progress Bar (for group tasks)
                if task.taskType.isGroupTask {
                    VStack(spacing: 4) {
                        ProgressView(value: task.completionPercentage)
                            .progressViewStyle(LinearProgressViewStyle(tint: Color(.systemGreen)))
                            .scaleEffect(y: 0.7)

                        // Progress Percentage
                        HStack {
                            Spacer()
                            Text("\(Int(task.completionPercentage * 100))% Complete")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                }
            }


            // Due Date
            if let dueDate = task.dueDate {
                HStack(spacing: 6) {
                    Image(systemName: task.isOverdue ? "exclamationmark.triangle.fill" : "calendar")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(task.isOverdue ? Color(.systemRed) : Color(.systemBlue))

                    Text(dueDate, style: .date)
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(task.isOverdue ? Color(.systemRed) : DesignSystem.Colors.textSecondary)

                    if task.isOverdue {
                        Text("Overdue")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemRed))
                            .cornerRadius(4)
                    }

                    Spacer()
                }
                .padding(.top, 2)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 14)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    task.isCompleted(by: currentUserId ?? UUID()) ?
                    Color(.systemGray6).opacity(0.4) : Color(.systemBackground)
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    task.isCompleted(by: currentUserId ?? UUID()) ?
                    Color(.systemGreen).opacity(0.4) : Color(.systemGray5).opacity(0.3),
                    lineWidth: task.isCompleted(by: currentUserId ?? UUID()) ? 2 : 1
                )
        )
        .shadow(
            color: task.isCompleted(by: currentUserId ?? UUID()) ?
            Color(.systemGreen).opacity(0.1) : Color.black.opacity(0.08),
            radius: task.isCompleted(by: currentUserId ?? UUID()) ? 8 : 4,
            x: 0,
            y: task.isCompleted(by: currentUserId ?? UUID()) ? 4 : 2
        )
        .opacity(task.isCompleted(by: currentUserId ?? UUID()) ? 0.85 : 1.0)
    }

    private func priorityColor(_ priority: GroupTask.Priority) -> Color {
        switch priority {
        case .low: return Color(.systemGreen)
        case .medium: return Color(.systemOrange)
        case .high: return Color(.systemRed)
        }
    }

    // MARK: - Actions
    private func completeTask() {
        guard let userId = currentUserId else { return }

        isCompleting = true

        _Concurrency.Task {
            let success = await groupManager.completeTask(task.id, by: userId)

            await MainActor.run {
                isCompleting = false

                if success {
                    print("✅ Task completed successfully")
                } else {
                    print("❌ Failed to complete task")
                }
            }
        }
    }

    private func uncompleteTask() {
        guard let userId = currentUserId else { return }

        isCompleting = true

        _Concurrency.Task {
            let success = await groupManager.uncompleteTask(task.id, by: userId)

            await MainActor.run {
                isCompleting = false

                if success {
                    print("✅ Task uncompleted successfully")
                } else {
                    print("❌ Failed to uncomplete task")
                }
            }
        }
    }
}



// MARK: - Member Row View
struct MemberRowView: View {
    let member: GroupMember
    let group: Group
    let isOwner: Bool

    var body: some View {
        HStack(spacing: 16) {
            // Avatar
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color(.systemBlue), Color(.systemPurple)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 44, height: 44)

                Text("M")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
            }

            // Member Info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("Member \(member.userId.uuidString.prefix(8))")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Spacer()

                    // Role Badge
                    Text(member.role.displayName)
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(member.role == .owner ? Color(.systemGreen) : Color(.systemBlue))
                        .cornerRadius(6)
                }

                Text("Joined \(member.joinedAt, style: .date)")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                // Member Stats
                HStack(spacing: 12) {
                    HStack(spacing: 3) {
                        Image(systemName: "list.bullet")
                            .font(.system(size: 10))
                            .foregroundColor(Color(.systemOrange))
                        Text("\(member.stats.tasksAssigned)")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(Color(.systemOrange))
                    }

                    HStack(spacing: 3) {
                        Image(systemName: "checkmark.circle")
                            .font(.system(size: 10))
                            .foregroundColor(Color(.systemGreen))
                        Text("\(member.stats.tasksCompleted)")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(Color(.systemGreen))
                    }

                    Spacer()
                }
            }

            // Actions (for owner)
            if isOwner && member.role != .owner {
                Menu {
                    Button("Remove from Group", role: .destructive) {
                        // Remove member action
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(Color(.systemGray6))
                        .clipShape(Circle())
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray6), lineWidth: 0.5)
        )
    }
}

// MARK: - Custom Button Style
struct PressedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct CreateGroupTaskView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared

    @State private var taskTitle = ""
    @State private var taskDescription = ""
    @State private var selectedPriority: GroupTask.Priority = .medium
    @State private var selectedTaskType: TaskType = .groupTask
    @State private var selectedAssignee: UUID?
    @State private var selectedMembers: Set<UUID> = []
    @State private var dueDate: Date?
    @State private var hasDueDate = false
    @State private var isCreating = false

    @FocusState private var focusedField: Field?

    enum Field {
        case title, description
    }

    enum TaskType: String, CaseIterable {
        case groupTask = "For Everyone"
        case selectedMembers = "Selected Members"
        case individualTask = "Individual Assignment"

        var description: String {
            switch self {
            case .groupTask:
                return "All group members need to complete this task"
            case .selectedMembers:
                return "Only selected members need to complete this task"
            case .individualTask:
                return "Assign to one specific member"
            }
        }

        var icon: String {
            switch self {
            case .groupTask:
                return "person.3.fill"
            case .selectedMembers:
                return "person.2.fill"
            case .individualTask:
                return "person.fill"
            }
        }
    }

    private var members: [GroupMember] {
        groupManager.getGroupMembers(group.id)
    }

    private var isFormValid: Bool {
        let titleValid = !taskTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty

        switch selectedTaskType {
        case .groupTask:
            return titleValid
        case .selectedMembers:
            return titleValid && !selectedMembers.isEmpty
        case .individualTask:
            return titleValid && selectedAssignee != nil
        }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection

                    // Basic Information
                    basicInfoSection

                    // Task Type
                    taskTypeSection

                    // Assignment (for selected members or individual tasks)
                    if selectedTaskType == .selectedMembers {
                        memberSelectionSection
                    } else if selectedTaskType == .individualTask {
                        assignmentSection
                    }

                    // Priority
                    prioritySection

                    // Due Date
                    dueDateSection

                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("New Group Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createTask()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .fontWeight(.semibold)
                    .disabled(!isFormValid || isCreating)
                }
            }
        }
        .taskMateLoadingState(isCreating)
    }

    // MARK: - Sections
    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color(.systemGreen), Color(.systemTeal)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)

                Image(systemName: "list.bullet.clipboard")
                    .font(.system(size: 24))
                    .foregroundColor(.white)
            }

            VStack(spacing: 4) {
                Text("Create Group Task")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)

                Text("for \(group.name)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
    }

    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Task Information")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            VStack(spacing: 12) {
                // Task Title
                VStack(alignment: .leading, spacing: 8) {
                    Text("Title")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    TextField("Enter task title", text: $taskTitle)
                        .focused($focusedField, equals: .title)
                        .font(.system(size: 16, weight: .regular))
                        .padding(12)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(focusedField == .title ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                        )
                }

                // Task Description
                VStack(alignment: .leading, spacing: 8) {
                    Text("Description (Optional)")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    TextEditor(text: $taskDescription)
                        .focused($focusedField, equals: .description)
                        .font(.system(size: 16, weight: .regular))
                        .frame(minHeight: 80)
                        .padding(8)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(focusedField == .description ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                        )
                }
            }
        }
    }

    private var taskTypeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Task Type")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            VStack(spacing: 12) {
                ForEach(TaskType.allCases, id: \.self) { type in
                    TaskTypeRow(
                        type: type,
                        isSelected: selectedTaskType == type,
                        action: { selectedTaskType = type }
                    )
                }
            }
        }
    }

    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Select Members")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                Spacer()

                Text("\(selectedMembers.count)/\(members.count)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            if members.isEmpty {
                Text("No members available")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
            } else {
                VStack(spacing: 8) {
                    ForEach(members) { member in
                        MemberSelectionRow(
                            member: member,
                            isSelected: selectedMembers.contains(member.userId),
                            action: {
                                if selectedMembers.contains(member.userId) {
                                    selectedMembers.remove(member.userId)
                                } else {
                                    selectedMembers.insert(member.userId)
                                }
                            }
                        )
                    }
                }
            }
        }
    }

    private var assignmentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Assign To")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            if members.isEmpty {
                Text("No members available for assignment")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
            } else {
                VStack(spacing: 8) {
                    ForEach(members) { member in
                        MemberSelectionRow(
                            member: member,
                            isSelected: selectedAssignee == member.userId,
                            action: { selectedAssignee = member.userId }
                        )
                    }
                }
            }
        }
    }

    private var prioritySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Priority")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            HStack(spacing: 12) {
                ForEach(GroupTask.Priority.allCases, id: \.self) { priority in
                    GroupTaskPriorityButton(
                        priority: priority,
                        isSelected: selectedPriority == priority,
                        action: { selectedPriority = priority }
                    )
                }
            }
        }
    }

    private var dueDateSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Due Date")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            VStack(spacing: 12) {
                // Due Date Toggle
                HStack {
                    Text("Set due date")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.text)

                    Spacer()

                    Toggle("", isOn: $hasDueDate)
                        .toggleStyle(SwitchToggleStyle(tint: DesignSystem.Colors.primary))
                }
                .padding(16)
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // Date Picker
                if hasDueDate {
                    DatePicker(
                        "Due Date",
                        selection: Binding(
                            get: { dueDate ?? Date().addingTimeInterval(86400) },
                            set: { dueDate = $0 }
                        ),
                        in: Date()...,
                        displayedComponents: [.date, .hourAndMinute]
                    )
                    .datePickerStyle(.compact)
                    .padding(16)
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
            }
        }
    }

    // MARK: - Actions
    private func createTask() {
        guard isFormValid else { return }

        isCreating = true

        let trimmedTitle = taskTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedDescription = taskDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalDescription = trimmedDescription.isEmpty ? nil : trimmedDescription
        let finalDueDate = hasDueDate ? dueDate : nil

        let taskType: GroupTask.GroupTaskType
        switch selectedTaskType {
        case .groupTask:
            taskType = .groupTask
        case .selectedMembers:
            // للآن سنعامل الأعضاء المحددين كمهمة جماعية
            // يمكن تطوير نوع جديد لاحقاً
            taskType = .groupTask
        case .individualTask:
            taskType = .individualTask(selectedAssignee!)
        }

        _Concurrency.Task {
            let task = await groupManager.createGroupTask(
                title: trimmedTitle,
                description: finalDescription,
                dueDate: finalDueDate,
                priority: selectedPriority,
                groupId: group.id,
                taskType: taskType
            )

            await MainActor.run {
                isCreating = false

                if task != nil {
                    dismiss()
                } else {
                    print("Failed to create group task")
                }
            }
        }
    }
}



// MARK: - Helper Components for Task Creation
struct TaskTypeRow: View {
    let type: CreateGroupTaskView.TaskType
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: type.icon)
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? .white : DesignSystem.Colors.primary)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(type.rawValue)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(isSelected ? .white : DesignSystem.Colors.text)

                    Text(type.description)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(isSelected ? .white.opacity(0.8) : DesignSystem.Colors.textSecondary)
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(16)
            .background(isSelected ? DesignSystem.Colors.primary : Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct MemberSelectionRow: View {
    let member: GroupMember
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemBlue), Color(.systemPurple)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 36, height: 36)

                    Text("M")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }

                // Member Info
                VStack(alignment: .leading, spacing: 2) {
                    Text("Member \(member.userId.uuidString.prefix(8))")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Text(member.role.displayName)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()

                // Selection Indicator
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GroupTaskPriorityButton: View {
    let priority: GroupTask.Priority
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(priority.displayName)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(isSelected ? .white : priorityColor)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? priorityColor : priorityColor.opacity(0.1))
                .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var priorityColor: Color {
        switch priority {
        case .low: return Color(.systemGreen)
        case .medium: return Color(.systemOrange)
        case .high: return Color(.systemRed)
        }
    }
}

#Preview {
    GroupDetailView(group: Group.sampleGroups(for: UUID()).first!)
}
