//
//  GroupDashboardView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Group Dashboard View
struct GroupDashboardView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared
    @State private var selectedTab = 0
    
    private var groupTasks: [GroupTask] {
        groupManager.getGroupTasks(group.id)
    }
    
    private var groupMembers: [GroupMember] {
        groupManager.getGroupMembers(group.id)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Dashboard Tabs
            dashboardTabs
            
            // Content
            ScrollView {
                VStack(spacing: 16) {
                    switch selectedTab {
                    case 0:
                        overviewSection
                    case 1:
                        membersSection
                    case 2:
                        chartsSection
                    case 3:
                        rankingsSection
                    default:
                        overviewSection
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .onAppear {
            // تحديث البيانات عند فتح Dashboard
            _Concurrency.Task {
                await groupManager.refreshData()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Top Bar with Back Button
            HStack {
                // Custom Back Button
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Group")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color(.systemBlue))
                }
                
                Spacer()
                
                // Dashboard Title
                Text("📊 Dashboard")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                // Refresh Button
                Button(action: {
                    _Concurrency.Task {
                        await groupManager.refreshData()
                    }
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(.systemBlue))
                }
            }
            
            // Group Info Compact
            HStack(spacing: 12) {
                // Small Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemGreen), Color(.systemTeal)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)
                    
                    Text(String(group.name.prefix(1)).uppercased())
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(group.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Analytics & Insights")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Dashboard Tabs
    private var dashboardTabs: some View {
        HStack(spacing: 0) {
            DashboardTabButton(title: "Overview", icon: "chart.bar.fill", isSelected: selectedTab == 0) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 0
                }
            }
            
            DashboardTabButton(title: "Members", icon: "person.2.fill", isSelected: selectedTab == 1) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 1
                }
            }
            
            DashboardTabButton(title: "Charts", icon: "chart.pie.fill", isSelected: selectedTab == 2) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 2
                }
            }
            
            DashboardTabButton(title: "Rankings", icon: "trophy.fill", isSelected: selectedTab == 3) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = 3
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Overview Section
    private var overviewSection: some View {
        VStack(spacing: 16) {
            // Key Metrics Cards
            keyMetricsSection
            
            // Progress Overview
            progressOverviewSection
            
            // Recent Activity
            recentActivitySection
        }
    }
    
    // MARK: - Key Metrics
    private var keyMetricsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📊 Key Metrics")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                MetricCard(
                    title: "Total Tasks",
                    value: "\(groupTasks.count)",
                    icon: "list.bullet",
                    color: .blue,
                    trend: "+2 this week"
                )
                
                MetricCard(
                    title: "Completed",
                    value: "\(groupTasks.filter { $0.isFullyCompleted }.count)",
                    icon: "checkmark.circle.fill",
                    color: .green,
                    trend: "+5 this week"
                )
                
                MetricCard(
                    title: "Active Members",
                    value: "\(groupMembers.count)",
                    icon: "person.2.fill",
                    color: .purple,
                    trend: "All active"
                )
                
                MetricCard(
                    title: "Overdue",
                    value: "\(groupTasks.filter { $0.isOverdue }.count)",
                    icon: "exclamationmark.triangle.fill",
                    color: .red,
                    trend: "-1 this week"
                )
            }
        }
    }
    
    // MARK: - Progress Overview
    private var progressOverviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📈 Progress Overview")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            ProgressOverviewCard(
                totalTasks: groupTasks.count,
                completedTasks: groupTasks.filter { $0.isFullyCompleted }.count,
                overdueTasks: groupTasks.filter { $0.isOverdue }.count
            )
        }
    }
    
    // MARK: - Recent Activity
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🕒 Recent Activity")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            RecentActivityCard(groupTasks: groupTasks)
        }
    }
    
    // MARK: - Members Section
    private var membersSection: some View {
        VStack(spacing: 16) {
            // Member Performance Cards
            memberPerformanceSection

            // Member Activity Timeline
            memberActivitySection
        }
    }

    // MARK: - Member Performance
    private var memberPerformanceSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("👥 Member Performance")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)

            if groupMembers.isEmpty {
                Text("No members data available")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    ForEach(groupMembers.prefix(4), id: \.id) { member in
                        MemberPerformanceCard(
                            member: member,
                            completedTasks: groupTasks.filter { task in
                                task.isCompleted(by: member.userId)
                            }.count,
                            totalTasks: groupTasks.count
                        )
                    }
                }
            }
        }
    }

    // MARK: - Member Activity
    private var memberActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📊 Activity Overview")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)

            MemberActivityCard(
                totalMembers: groupMembers.count,
                activeTasks: groupTasks.filter { !$0.isFullyCompleted }.count,
                completionRate: calculateGroupCompletionRate()
            )
        }
    }

    private func calculateGroupCompletionRate() -> Double {
        guard !groupTasks.isEmpty else { return 0.0 }
        let completedTasks = groupTasks.filter { $0.isFullyCompleted }.count
        return Double(completedTasks) / Double(groupTasks.count)
    }
    
    // MARK: - Charts Section (Placeholder)
    private var chartsSection: some View {
        VStack(spacing: 16) {
            Text("📊 Charts & Analytics")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Text("Coming Soon...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
    
    // MARK: - Rankings Section (Placeholder)
    private var rankingsSection: some View {
        VStack(spacing: 16) {
            Text("🏆 Member Rankings")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Text("Coming Soon...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
}

// MARK: - Dashboard Components

// Dashboard Tab Button
struct DashboardTabButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)

                Text(title)
                    .font(.system(size: 11, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(.systemBlue) : DesignSystem.Colors.textSecondary)

                Rectangle()
                    .fill(isSelected ? Color(.systemBlue) : Color.clear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// Metric Card
struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(color)

                Spacer()

                Text(value)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
            }

            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.text)

            Text(trend)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1)
    }
}

// Progress Overview Card
struct ProgressOverviewCard: View {
    let totalTasks: Int
    let completedTasks: Int
    let overdueTasks: Int

    private var completionPercentage: Double {
        guard totalTasks > 0 else { return 0.0 }
        return Double(completedTasks) / Double(totalTasks)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Progress Bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Overall Progress")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Spacer()

                    Text("\(Int(completionPercentage * 100))%")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(Color(.systemGreen))
                }

                ProgressView(value: completionPercentage)
                    .progressViewStyle(LinearProgressViewStyle(tint: Color(.systemGreen)))
                    .scaleEffect(y: 1.5)
            }

            // Stats Row
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(completedTasks)")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(Color(.systemGreen))
                    Text("Completed")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("\(totalTasks - completedTasks)")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(Color(.systemOrange))
                    Text("Remaining")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("\(overdueTasks)")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(Color(.systemRed))
                    Text("Overdue")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1)
    }
}

// Recent Activity Card
struct RecentActivityCard: View {
    let groupTasks: [GroupTask]

    private var recentTasks: [GroupTask] {
        groupTasks
            .sorted { $0.updatedAt > $1.updatedAt }
            .prefix(3)
            .map { $0 }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            if recentTasks.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "clock.badge.questionmark")
                        .font(.system(size: 24))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("No recent activity")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ForEach(recentTasks) { task in
                    HStack(spacing: 12) {
                        // Status Icon
                        Image(systemName: task.isFullyCompleted ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(task.isFullyCompleted ? Color(.systemGreen) : Color(.systemGray4))

                        // Task Info
                        VStack(alignment: .leading, spacing: 2) {
                            Text(task.title)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.text)
                                .lineLimit(1)

                            Text("Updated \(timeAgo(task.updatedAt))")
                                .font(.system(size: 11, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }

                        Spacer()

                        // Progress for group tasks
                        if case .groupTask = task.taskType {
                            Text(task.groupProgress)
                                .font(.system(size: 11, weight: .semibold))
                                .foregroundColor(Color(.systemBlue))
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color(.systemBlue).opacity(0.1))
                                .cornerRadius(4)
                        }
                    }

                    if task.id != recentTasks.last?.id {
                        Divider()
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1)
    }

    private func timeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Member Performance Card
struct MemberPerformanceCard: View {
    let member: GroupMember
    let completedTasks: Int
    let totalTasks: Int

    private var completionRate: Double {
        guard totalTasks > 0 else { return 0.0 }
        return Double(completedTasks) / Double(totalTasks)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Member Avatar & Name
            HStack(spacing: 8) {
                Circle()
                    .fill(Color(.systemBlue))
                    .frame(width: 24, height: 24)
                    .overlay(
                        Text("M")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 1) {
                    Text("Member")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                        .lineLimit(1)

                    Text(member.role.displayName)
                        .font(.system(size: 9, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()
            }

            // Performance Stats
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("\(completedTasks)/\(totalTasks)")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(Color(.systemGreen))

                    Spacer()

                    Text("\(Int(completionRate * 100))%")
                        .font(.system(size: 11, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                ProgressView(value: completionRate)
                    .progressViewStyle(LinearProgressViewStyle(tint: Color(.systemGreen)))
                    .scaleEffect(y: 0.8)
            }
        }
        .padding(12)
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Member Activity Card
struct MemberActivityCard: View {
    let totalMembers: Int
    let activeTasks: Int
    let completionRate: Double

    var body: some View {
        VStack(spacing: 16) {
            // Activity Stats
            HStack(spacing: 20) {
                ActivityStatItem(
                    title: "Total Members",
                    value: "\(totalMembers)",
                    icon: "person.2.fill",
                    color: .blue
                )

                ActivityStatItem(
                    title: "Active Tasks",
                    value: "\(activeTasks)",
                    icon: "clock.fill",
                    color: .orange
                )

                ActivityStatItem(
                    title: "Completion Rate",
                    value: "\(Int(completionRate * 100))%",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .green
                )
            }

            // Team Performance Indicator
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Team Performance")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Spacer()

                    Text(performanceLevel)
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(performanceColor)
                }

                ProgressView(value: completionRate)
                    .progressViewStyle(LinearProgressViewStyle(tint: performanceColor))
                    .scaleEffect(y: 1.2)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1)
    }

    private var performanceLevel: String {
        switch completionRate {
        case 0.8...:
            return "Excellent"
        case 0.6..<0.8:
            return "Good"
        case 0.4..<0.6:
            return "Average"
        default:
            return "Needs Improvement"
        }
    }

    private var performanceColor: Color {
        switch completionRate {
        case 0.8...:
            return Color(.systemGreen)
        case 0.6..<0.8:
            return Color(.systemBlue)
        case 0.4..<0.6:
            return Color(.systemOrange)
        default:
            return Color(.systemRed)
        }
    }
}

// MARK: - Activity Stat Item
struct ActivityStatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)

            Text(title)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    GroupDashboardView(group: Group.sampleGroups(for: UUID()).first!)
}
