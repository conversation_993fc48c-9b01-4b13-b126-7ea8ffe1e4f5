//
//  GroupMembersView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Enhanced Group Members View
struct EnhancedGroupMembersView: View {
    let group: Group
    let onInviteMember: () -> Void
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingMemberDetails = false
    @State private var selectedMember: GroupMember?

    private var members: [GroupMember] {
        groupManager.getGroupMembers(group.id)
    }

    private var isOwner: Bool {
        group.isOwner(DataManager.shared.currentUser?.id ?? UUID())
    }
    
    private var currentUserId: UUID? {
        DataManager.shared.currentUser?.id
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header with Stats
            headerSection
            
            // Members List
            if members.isEmpty {
                emptyMembersState
            } else {
                membersListSection
            }
        }
        .background(Color(.systemGroupedBackground))
        .overlay(
            // Floating Action Button
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    Button(action: onInviteMember) {
                        HStack(spacing: 8) {
                            Image(systemName: "person.badge.plus.fill")
                                .font(.system(size: 16, weight: .bold))
                            Text("Invite")
                                .font(.system(size: 14, weight: .bold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(
                                colors: [
                                    Color(.systemBlue),
                                    Color(.systemPurple)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(25)
                        .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                    }
                    .buttonStyle(PressedButtonStyle())
                    .padding(.trailing, 20)
                    .padding(.bottom, 20)
                }
            }
        )
        .sheet(isPresented: $showingMemberDetails) {
            if let member = selectedMember {
                MemberDetailsView(member: member, group: group)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Title and Invite Button
            HStack {
                Text("Members")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)

                Spacer()

                // Always show invite button (for testing)
                Button(action: onInviteMember) {
                    HStack(spacing: 8) {
                        Image(systemName: "person.badge.plus.fill")
                            .font(.system(size: 14, weight: .semibold))
                        Text("Invite")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [
                                Color(.systemBlue),
                                Color(.systemPurple)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(10)
                    .shadow(color: Color(.systemBlue).opacity(0.3), radius: 3, x: 0, y: 1)
                }
                .buttonStyle(PressedButtonStyle())
            }
            
            // Member Stats
            memberStatsSection
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Member Stats
    private var memberStatsSection: some View {
        HStack(spacing: 16) {
            StatBadge(
                icon: "person.2.fill",
                value: "\(members.count)",
                label: "Total",
                color: .blue
            )
            
            StatBadge(
                icon: "crown.fill",
                value: "1",
                label: "Owner",
                color: .orange
            )
            
            StatBadge(
                icon: "person.fill",
                value: "\(members.filter { $0.role == .member }.count)",
                label: "Members",
                color: .green
            )
            
            Spacer()
        }
    }
    
    // MARK: - Empty State
    private var emptyMembersState: some View {
        VStack(spacing: 20) {
            Spacer()

            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(.systemBlue).opacity(0.1),
                                Color(.systemPurple).opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)

                Image(systemName: "person.2.fill")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(Color(.systemBlue))
            }

            VStack(spacing: 8) {
                Text("No Members Yet")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)

                Text("Invite people to join your group and start collaborating!")
                    .font(.system(size: 15, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }

            // Always show invite button in empty state
            Button(action: onInviteMember) {
                HStack(spacing: 10) {
                    Image(systemName: "person.badge.plus.fill")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Invite First Member")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [
                            Color(.systemBlue),
                            Color(.systemPurple)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
                .shadow(color: Color(.systemBlue).opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(PressedButtonStyle())

            Spacer()
        }
        .padding(.horizontal, 28)
    }
    
    // MARK: - Members List
    private var membersListSection: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(members, id: \.id) { member in
                    EnhancedMemberRowView(
                        member: member,
                        group: group,
                        isCurrentUser: member.userId == currentUserId,
                        canManage: isOwner && member.userId != currentUserId,
                        onTap: {
                            selectedMember = member
                            showingMemberDetails = true
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 8)
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Stat Badge
struct StatBadge: View {
    let icon: String
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(color)
            
            VStack(alignment: .leading, spacing: 1) {
                Text(value)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(color)
                
                Text(label)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Enhanced Member Row View
struct EnhancedMemberRowView: View {
    let member: GroupMember
    let group: Group
    let isCurrentUser: Bool
    let canManage: Bool
    let onTap: () -> Void
    @StateObject private var groupManager = GroupManager.shared
    @StateObject private var userInfoManager = UserInfoManager.shared
    @State private var userInfo: UnifiedUserInfo?
    
    private var memberTasks: [GroupTask] {
        groupManager.getGroupTasks(group.id).filter { task in
            task.isCompleted(by: member.userId)
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Avatar - Using Unified System
                ZStack {
                    UnifiedUserAvatarView(userId: member.userId, size: 44)

                    // Role indicator
                    if member.role == .owner {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                Image(systemName: "crown.fill")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.orange)
                                    .background(
                                        Circle()
                                            .fill(Color.white)
                                            .frame(width: 18, height: 18)
                                    )
                                    .offset(x: -2, y: -2)
                            }
                        }
                    }
                }

                // Member Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(isCurrentUser ? "You" : (userInfo?.displayName ?? "Loading..."))
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Spacer()

                        // Role Badge
                        Text(member.role.displayName)
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(member.role == .owner ? Color(.systemOrange) : Color(.systemBlue))
                            .cornerRadius(6)
                    }

                    Text("Joined \(member.joinedAt, style: .date)")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    // Member Stats
                    HStack(spacing: 12) {
                        HStack(spacing: 3) {
                            Image(systemName: "list.bullet")
                                .font(.system(size: 10))
                                .foregroundColor(Color(.systemOrange))
                            Text("\(member.stats.tasksAssigned)")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(Color(.systemOrange))
                        }

                        HStack(spacing: 3) {
                            Image(systemName: "checkmark.circle")
                                .font(.system(size: 10))
                                .foregroundColor(Color(.systemGreen))
                            Text("\(member.stats.tasksCompleted)")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(Color(.systemGreen))
                        }

                        Spacer()
                        
                        // Tap indicator
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isCurrentUser ? Color(.systemBlue).opacity(0.3) : Color(.systemGray6), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .task {
            await loadUserInfo()
        }
        .onChange(of: member.userId) { _ in
            Task {
                await loadUserInfo()
            }
        }
    }

    private func loadUserInfo() async {
        if let info = await userInfoManager.getUserInfo(member.userId) {
            await MainActor.run {
                self.userInfo = info
            }
        }
    }
}

#Preview {
    EnhancedGroupMembersView(
        group: Group.sampleGroups(for: UUID()).first!,
        onInviteMember: {}
    )
}
