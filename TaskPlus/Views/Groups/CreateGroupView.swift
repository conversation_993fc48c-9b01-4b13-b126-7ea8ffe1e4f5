//
//  CreateGroupView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Create Group View
struct CreateGroupView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared
    
    @State private var groupName = ""
    @State private var groupDescription = ""
    @State private var isPrivate = false
    @State private var isCreating = false
    
    @FocusState private var focusedField: Field?
    
    enum Field {
        case name, description
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Basic Information
                    basicInfoSection
                    
                    // Privacy Settings
                    privacySection
                    
                    // Tips
                    tipsSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Create Group")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Create") {
                        createGroup()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .fontWeight(.semibold)
                    .disabled(!isFormValid || isCreating)
                }
            }
        }
        .taskMateLoadingState(isCreating)
    }
    
    // MARK: - Form Sections
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color(.systemGreen), Color(.systemTeal)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "person.3.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
            }
            
            Text("Create a New Group")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
                .multilineTextAlignment(.center)
            
            Text("Collaborate with your team and friends")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Basic Information")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                // Group Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Group Name")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    TextField("Enter group name", text: $groupName)
                        .focused($focusedField, equals: .name)
                        .font(.system(size: 16, weight: .regular))
                        .padding(12)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(focusedField == .name ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                        )
                }
                
                // Group Description
                VStack(alignment: .leading, spacing: 8) {
                    Text("Description (Optional)")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    TextEditor(text: $groupDescription)
                        .focused($focusedField, equals: .description)
                        .font(.system(size: 16, weight: .regular))
                        .frame(minHeight: 80)
                        .padding(8)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(focusedField == .description ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                        )
                    
                    if groupDescription.isEmpty {
                        Text("Describe the purpose of your group")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
            }
        }
    }
    
    private var privacySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Privacy Settings")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 16) {
                // Privacy Toggle
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Private Group")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text("Only invited members can join")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $isPrivate)
                        .toggleStyle(SwitchToggleStyle(tint: DesignSystem.Colors.primary))
                }
                .padding(16)
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                // Privacy Info
                HStack(spacing: 12) {
                    Image(systemName: isPrivate ? "lock.fill" : "globe")
                        .font(.system(size: 16))
                        .foregroundColor(isPrivate ? Color(.systemOrange) : Color(.systemGreen))
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(isPrivate ? "Private Group" : "Public Group")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text(isPrivate ? "Members join by invitation only" : "Anyone can discover and join")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                }
                .padding(12)
                .background(isPrivate ? Color(.systemOrange).opacity(0.1) : Color(.systemGreen).opacity(0.1))
                .cornerRadius(8)
            }
        }
    }
    
    private var tipsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tips for Success")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                TipRow(
                    icon: "lightbulb.fill",
                    iconColor: .yellow,
                    title: "Choose a Clear Name",
                    description: "Pick a name that clearly describes your group's purpose"
                )
                
                TipRow(
                    icon: "person.3.fill",
                    iconColor: .blue,
                    title: "Invite the Right People",
                    description: "Add members who will actively contribute to your goals"
                )
                
                TipRow(
                    icon: "target",
                    iconColor: .green,
                    title: "Set Clear Goals",
                    description: "Define what you want to achieve together as a group"
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !groupName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // MARK: - Actions
    private func createGroup() {
        guard isFormValid else { return }
        
        isCreating = true
        
        let trimmedName = groupName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedDescription = groupDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalDescription = trimmedDescription.isEmpty ? nil : trimmedDescription
        
        _Concurrency.Task {
            let group = await groupManager.createGroup(
                name: trimmedName,
                description: finalDescription,
                isPrivate: isPrivate
            )
            
            await MainActor.run {
                isCreating = false

                if group != nil {
                    // Success - dismiss the view
                    dismiss()

                    // Refresh groups list in background
                    _Concurrency.Task {
                        await groupManager.refreshData()
                    }
                } else {
                    // Handle error - groupManager.errorMessage should be set
                    print("Failed to create group")
                }
            }
        }
    }
}

// MARK: - Helper Views
struct TipRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(iconColor)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text(description)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    CreateGroupView()
}
