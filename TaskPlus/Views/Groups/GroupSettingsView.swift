//
//  GroupSettingsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Group Settings View
struct GroupSettingsView: View {
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingDeleteConfirmation = false
    @State private var showingEditGroup = false
    @State private var showingLeaveConfirmation = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 20) {
                    // Group Info Section
                    groupInfoSection
                    
                    // Privacy Settings
                    privacySettingsSection
                    
                    // Member Management
                    memberManagementSection
                    
                    // Danger Zone
                    dangerZoneSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showingEditGroup) {
            EditGroupView(group: group)
        }
        .alert("Delete Group", isPresented: $showingDeleteConfirmation) {
            But<PERSON>("Cancel", role: .cancel) { }
            But<PERSON>("Delete", role: .destructive) {
                deleteGroup()
            }
        } message: {
            Text("Are you sure you want to delete this group? This action cannot be undone.")
        }
        .alert("Leave Group", isPresented: $showingLeaveConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Leave", role: .destructive) {
                leaveGroup()
            }
        } message: {
            Text("Are you sure you want to leave this group?")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Settings")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Manage group settings")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // Edit button
            Button(action: { showingEditGroup = true }) {
                Image(systemName: "pencil")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
                    .padding(8)
                    .background(DesignSystem.Colors.sunriseOrange.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Group Info Section
    private var groupInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            GroupSectionHeader(title: "Group Information", icon: "info.circle.fill")
            
            VStack(spacing: 12) {
                GroupSettingsRow(
                    icon: "textformat",
                    iconColor: DesignSystem.Colors.sunriseOrange,
                    title: "Group Name",
                    subtitle: group.name,
                    action: { showingEditGroup = true }
                )
                
                if let description = group.description {
                    GroupSettingsRow(
                        icon: "text.alignleft",
                        iconColor: .blue,
                        title: "Description",
                        subtitle: description,
                        action: { showingEditGroup = true }
                    )
                }
                
                GroupSettingsRow(
                    icon: "number",
                    iconColor: .purple,
                    title: "Group Code",
                    subtitle: group.groupCode,
                    action: { copyGroupCode() }
                )
                
                GroupSettingsRow(
                    icon: "calendar",
                    iconColor: .green,
                    title: "Created",
                    subtitle: group.createdAt.formatted(date: .abbreviated, time: .omitted),
                    action: { }
                )
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Privacy Settings Section
    private var privacySettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            GroupSectionHeader(title: "Privacy & Access", icon: "lock.fill")
            
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: group.isPrivate ? "lock.fill" : "globe")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(group.isPrivate ? .red : .green)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Group Type")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text(group.isPrivate ? "Private Group" : "Public Group")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Button("Change") {
                        showingEditGroup = true
                    }
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
                }
                .padding(12)
                .background(Color(.systemGray6).opacity(0.5))
                .cornerRadius(8)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Member Management Section
    private var memberManagementSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            GroupSectionHeader(title: "Member Management", icon: "person.2.fill")
            
            VStack(spacing: 12) {
                let memberCount = groupManager.getGroupMembers(group.id).count
                
                GroupSettingsRow(
                    icon: "person.2.fill",
                    iconColor: .blue,
                    title: "Total Members",
                    subtitle: "\(memberCount) member\(memberCount == 1 ? "" : "s")",
                    action: { }
                )
                
                Button(action: { /* TODO: Show member management */ }) {
                    HStack {
                        Image(systemName: "person.crop.circle.badge.plus")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.sunriseOrange)
                            .frame(width: 24)
                        
                        Text("Manage Members")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.sunriseOrange)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.sunriseOrange)
                    }
                    .padding(12)
                    .background(DesignSystem.Colors.sunriseOrange.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Danger Zone Section
    private var dangerZoneSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            GroupSectionHeader(title: "Danger Zone", icon: "exclamationmark.triangle.fill", color: .red)
            
            VStack(spacing: 12) {
                Button(action: { showingDeleteConfirmation = true }) {
                    HStack {
                        Image(systemName: "trash.fill")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.red)
                            .frame(width: 24)
                        
                        Text("Delete Group")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.red)
                        
                        Spacer()
                    }
                    .padding(12)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.red.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Actions
    private func copyGroupCode() {
        UIPasteboard.general.string = group.groupCode
        // TODO: Show toast message
        print("📋 Group code copied: \(group.groupCode)")
    }
    
    private func deleteGroup() {
        _Concurrency.Task {
            // TODO: Implement group deletion
            print("🗑️ Deleting group: \(group.name)")
        }
    }
    
    private func leaveGroup() {
        _Concurrency.Task {
            // TODO: Implement leave group
            print("👋 Leaving group: \(group.name)")
        }
    }
}

// MARK: - Supporting Views

// MARK: - Group Section Header
struct GroupSectionHeader: View {
    let title: String
    let icon: String
    let color: Color

    init(title: String, icon: String, color: Color = DesignSystem.Colors.sunriseOrange) {
        self.title = title
        self.icon = icon
        self.color = color
    }

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(color)

            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)

            Spacer()
        }
    }
}

// MARK: - Group Settings Row
struct GroupSettingsRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(iconColor)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Text(subtitle)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(12)
            .background(Color(.systemGray6).opacity(0.5))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Edit Group View (Placeholder)
struct EditGroupView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header Icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: "pencil")
                        .font(.system(size: 32, weight: .semibold))
                        .foregroundColor(.white)
                }

                VStack(spacing: 8) {
                    Text("Edit Group")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Text("Update group information and settings")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }

                // TODO: Add edit form here

                Spacer()
            }
            .padding()
            .navigationTitle("Edit Group")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") { dismiss() }
                        .foregroundColor(DesignSystem.Colors.sunriseOrange)
                        .fontWeight(.semibold)
                }
            }
        }
    }
}
