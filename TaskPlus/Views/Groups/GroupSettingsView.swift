//
//  GroupSettingsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Group Settings View
struct GroupSettingsView: View {
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingDeleteConfirmation = false
    @State private var showingEditGroup = false
    @State private var showingLeaveConfirmation = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 20) {
                    // Group Info Section
                    groupInfoSection
                    
                    // Privacy Settings
                    privacySettingsSection
                    
                    // Member Management
                    memberManagementSection
                    
                    // Danger Zone
                    dangerZoneSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showingEditGroup) {
            EditGroupView(group: group)
        }
        .alert("Delete Group", isPresented: $showingDeleteConfirmation) {
            But<PERSON>("Cancel", role: .cancel) { }
            But<PERSON>("Delete", role: .destructive) {
                deleteGroup()
            }
        } message: {
            Text("Are you sure you want to delete this group? This action cannot be undone.")
        }
        .alert("Leave Group", isPresented: $showingLeaveConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Leave", role: .destructive) {
                leaveGroup()
            }
        } message: {
            Text("Are you sure you want to leave this group?")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Settings")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Manage group settings")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // Edit button
            Button(action: { showingEditGroup = true }) {
                Image(systemName: "pencil")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.blue)
                    .padding(8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Group Info Section
    private var groupInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Group Information", icon: "info.circle.fill")
            
            VStack(spacing: 12) {
                SettingsRow(
                    icon: "textformat",
                    title: "Group Name",
                    value: group.name,
                    action: { showingEditGroup = true }
                )
                
                if let description = group.description {
                    SettingsRow(
                        icon: "text.alignleft",
                        title: "Description",
                        value: description,
                        action: { showingEditGroup = true }
                    )
                }
                
                SettingsRow(
                    icon: "number",
                    title: "Group Code",
                    value: group.groupCode,
                    action: { copyGroupCode() }
                )
                
                SettingsRow(
                    icon: "calendar",
                    title: "Created",
                    value: group.createdAt.formatted(date: .abbreviated, time: .omitted),
                    action: nil
                )
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Privacy Settings Section
    private var privacySettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Privacy & Access", icon: "lock.fill")
            
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: group.isPrivate ? "lock.fill" : "globe")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(group.isPrivate ? .red : .green)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Group Type")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text(group.isPrivate ? "Private Group" : "Public Group")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Button("Change") {
                        showingEditGroup = true
                    }
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.blue)
                }
                .padding(12)
                .background(Color(.systemGray6).opacity(0.5))
                .cornerRadius(8)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Member Management Section
    private var memberManagementSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Member Management", icon: "person.2.fill")
            
            VStack(spacing: 12) {
                let memberCount = groupManager.getGroupMembers(group.id).count
                
                SettingsRow(
                    icon: "person.2.fill",
                    title: "Total Members",
                    value: "\(memberCount) member\(memberCount == 1 ? "" : "s")",
                    action: nil
                )
                
                Button(action: { /* TODO: Show member management */ }) {
                    HStack {
                        Image(systemName: "person.crop.circle.badge.plus")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        Text("Manage Members")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.blue)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.blue)
                    }
                    .padding(12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Danger Zone Section
    private var dangerZoneSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Danger Zone", icon: "exclamationmark.triangle.fill", color: .red)
            
            VStack(spacing: 12) {
                Button(action: { showingDeleteConfirmation = true }) {
                    HStack {
                        Image(systemName: "trash.fill")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.red)
                            .frame(width: 24)
                        
                        Text("Delete Group")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.red)
                        
                        Spacer()
                    }
                    .padding(12)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.red.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Actions
    private func copyGroupCode() {
        UIPasteboard.general.string = group.groupCode
        // TODO: Show toast message
        print("📋 Group code copied: \(group.groupCode)")
    }
    
    private func deleteGroup() {
        _Concurrency.Task {
            // TODO: Implement group deletion
            print("🗑️ Deleting group: \(group.name)")
        }
    }
    
    private func leaveGroup() {
        _Concurrency.Task {
            // TODO: Implement leave group
            print("👋 Leaving group: \(group.name)")
        }
    }
}

// MARK: - Supporting Views

struct SectionHeader: View {
    let title: String
    let icon: String
    let color: Color
    
    init(title: String, icon: String, color: Color = .blue) {
        self.title = title
        self.icon = icon
        self.color = color
    }
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Spacer()
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let value: String
    let action: (() -> Void)?
    
    var body: some View {
        Button(action: action ?? {}) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(value)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                if action != nil {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .padding(12)
            .background(Color(.systemGray6).opacity(0.5))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(action == nil)
    }
}

// MARK: - Edit Group View (Placeholder)
struct EditGroupView: View {
    let group: Group
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Edit Group")
                    .font(.title2)
                    .fontWeight(.bold)
                
                // TODO: Implement edit group form
                
                Spacer()
            }
            .padding()
            .navigationTitle("Edit Group")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") { dismiss() }
                }
            }
        }
    }
}
