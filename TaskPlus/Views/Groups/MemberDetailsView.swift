//
//  MemberDetailsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Member Details View
struct MemberDetailsView: View {
    let member: GroupMember
    let group: Group
    @Environment(\.dismiss) private var dismiss
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingRemoveConfirmation = false
    
    private var isOwner: Bool {
        group.isOwner(DataManager.shared.currentUser?.id ?? UUID())
    }
    
    private var isCurrentUser: Bool {
        member.userId == DataManager.shared.currentUser?.id
    }
    
    private var memberTasks: [GroupTask] {
        groupManager.getGroupTasks(group.id).filter { task in
            task.isCompleted(by: member.userId)
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 20) {
                    // Member Info
                    memberInfoSection
                    
                    // Statistics
                    statisticsSection
                    
                    // Recent Tasks
                    recentTasksSection
                    
                    // Actions (for owner)
                    if isOwner && !isCurrentUser {
                        actionsSection
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .confirmationDialog(
            "Remove Member",
            isPresented: $showingRemoveConfirmation,
            titleVisibility: .visible
        ) {
            Button("Remove from Group", role: .destructive) {
                removeMember()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to remove this member from the group? This action cannot be undone.")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Top Bar
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Members")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color(.systemBlue))
                }
                
                Spacer()
                
                Text("Member Details")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                // Placeholder for balance
                Color.clear
                    .frame(width: 80, height: 20)
            }
            
            // Member Avatar and Basic Info
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    member.role == .owner ? Color(.systemOrange) : Color(.systemBlue),
                                    member.role == .owner ? Color(.systemRed) : Color(.systemPurple)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)

                    Text(member.role == .owner ? "👑" : "👤")
                        .font(.system(size: 32))
                }
                
                VStack(spacing: 4) {
                    Text(isCurrentUser ? "You" : "Group Member")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(member.role.displayName)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(member.role == .owner ? Color(.systemOrange) : Color(.systemBlue))
                        .cornerRadius(8)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Member Info Section
    private var memberInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📋 Member Information")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                InfoRow(
                    icon: "calendar",
                    title: "Joined",
                    value: member.joinedAt.formatted(date: .abbreviated, time: .omitted),
                    color: .blue
                )
                
                InfoRow(
                    icon: "person.badge.shield.checkmark",
                    title: "Role",
                    value: member.role.displayName,
                    color: member.role == .owner ? .orange : .purple
                )
                
                InfoRow(
                    icon: "clock",
                    title: "Last Active",
                    value: "Recently", // يمكن تطويرها لاحقاً
                    color: .green
                )
            }
        }
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📊 Performance")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StatCard(
                    title: "Tasks Assigned",
                    value: "\(member.stats.tasksAssigned)",
                    icon: "list.bullet",
                    color: .orange
                )
                
                StatCard(
                    title: "Tasks Completed",
                    value: "\(member.stats.tasksCompleted)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
                
                StatCard(
                    title: "Completion Rate",
                    value: "\(Int(getCompletionRate() * 100))%",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                )
                
                StatCard(
                    title: "Contribution",
                    value: getContributionLevel(),
                    icon: "star.fill",
                    color: .purple
                )
            }
        }
    }
    
    // MARK: - Recent Tasks Section
    private var recentTasksSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🕒 Recent Activity")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            if memberTasks.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "list.bullet.clipboard")
                        .font(.system(size: 24))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("No recent activity")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
                .background(Color(.systemBackground))
                .cornerRadius(12)
            } else {
                VStack(spacing: 8) {
                    ForEach(memberTasks.prefix(3), id: \.id) { task in
                        TaskActivityRow(task: task, member: member)
                    }
                }
            }
        }
    }
    
    // MARK: - Actions Section
    private var actionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("⚙️ Actions")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Change Role",
                    icon: "person.badge.key",
                    color: .blue,
                    action: {
                        // TODO: Implement role change
                    }
                )
                
                ActionButton(
                    title: "Remove from Group",
                    icon: "person.badge.minus",
                    color: .red,
                    action: {
                        showingRemoveConfirmation = true
                    }
                )
            }
        }
    }
    
    // MARK: - Helper Functions
    private func getCompletionRate() -> Double {
        guard member.stats.tasksAssigned > 0 else { return 0.0 }
        return Double(member.stats.tasksCompleted) / Double(member.stats.tasksAssigned)
    }

    private func getContributionLevel() -> String {
        let rate = getCompletionRate()
        switch rate {
        case 0.8...:
            return "High"
        case 0.5..<0.8:
            return "Medium"
        default:
            return "Low"
        }
    }
    
    private func removeMember() {
        // TODO: Implement member removal
        dismiss()
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.text)
            
            Spacer()
            
            Text(value)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(10)
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

struct TaskActivityRow: View {
    let task: GroupTask
    let member: GroupMember
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: task.isCompleted(by: member.userId) ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 16))
                .foregroundColor(task.isCompleted(by: member.userId) ? Color(.systemGreen) : Color(.systemGray4))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(task.title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.text)
                    .lineLimit(1)
                
                Text(task.updatedAt.formatted(date: .abbreviated, time: .shortened))
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    MemberDetailsView(
        member: GroupMember(
            userId: UUID(),
            groupId: UUID(),
            role: .member
        ),
        group: Group.sampleGroups(for: UUID()).first!
    )
}
