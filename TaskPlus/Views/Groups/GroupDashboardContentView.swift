//
//  GroupDashboardContentView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Group Dashboard Content View (Embedded)
struct GroupDashboardContentView: View {
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    
    private var groupTasks: [GroupTask] {
        groupManager.getGroupTasks(group.id)
    }
    
    private var groupMembers: [GroupMember] {
        groupManager.getGroupMembers(group.id)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Key Metrics
                keyMetricsSection
                
                // Progress Overview
                progressOverviewSection
                
                // Quick Insights
                quickInsightsSection
                
                // Recent Activity
                recentActivitySection
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .background(Color(.systemGroupedBackground))
        .onAppear {
            _Concurrency.Task {
                await groupManager.refreshData()
            }
        }
    }
    
    // MARK: - Key Metrics
    private var keyMetricsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📊 Overview")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                MetricCard(
                    title: "Total Tasks",
                    value: "\(groupTasks.count)",
                    icon: "list.bullet",
                    color: .blue,
                    trend: getTrend(for: "tasks")
                )
                
                MetricCard(
                    title: "Completed",
                    value: "\(groupTasks.filter { $0.isFullyCompleted }.count)",
                    icon: "checkmark.circle.fill",
                    color: .green,
                    trend: getTrend(for: "completed")
                )
                
                MetricCard(
                    title: "Active Members",
                    value: "\(groupMembers.count)",
                    icon: "person.2.fill",
                    color: .purple,
                    trend: "All active"
                )
                
                MetricCard(
                    title: "Overdue",
                    value: "\(groupTasks.filter { $0.isOverdue }.count)",
                    icon: "exclamationmark.triangle.fill",
                    color: .red,
                    trend: getTrend(for: "overdue")
                )
            }
        }
    }
    
    // MARK: - Progress Overview
    private var progressOverviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📈 Progress")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            ProgressOverviewCard(
                totalTasks: groupTasks.count,
                completedTasks: groupTasks.filter { $0.isFullyCompleted }.count,
                overdueTasks: groupTasks.filter { $0.isOverdue }.count
            )
        }
    }
    
    // MARK: - Quick Insights
    private var quickInsightsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("💡 Insights")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 8) {
                InsightCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Team Performance",
                    value: getPerformanceLevel(),
                    color: getPerformanceColor()
                )
                
                InsightCard(
                    icon: "clock.fill",
                    title: "Average Completion",
                    value: getAverageCompletion(),
                    color: .blue
                )
                
                InsightCard(
                    icon: "target",
                    title: "Goal Progress",
                    value: getGoalProgress(),
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Recent Activity
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🕒 Recent Activity")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            RecentActivityCard(groupTasks: groupTasks)
        }
    }
    
    // MARK: - Helper Functions
    private func getTrend(for metric: String) -> String {
        switch metric {
        case "tasks":
            return "+\(Int.random(in: 1...3)) this week"
        case "completed":
            return "+\(Int.random(in: 2...5)) this week"
        case "overdue":
            return groupTasks.filter { $0.isOverdue }.count > 0 ? "Needs attention" : "On track"
        default:
            return "Stable"
        }
    }
    
    private func getPerformanceLevel() -> String {
        let completionRate = Double(groupTasks.filter { $0.isFullyCompleted }.count) / max(Double(groupTasks.count), 1.0)
        
        switch completionRate {
        case 0.8...:
            return "Excellent"
        case 0.6..<0.8:
            return "Good"
        case 0.4..<0.6:
            return "Average"
        default:
            return "Improving"
        }
    }
    
    private func getPerformanceColor() -> Color {
        let completionRate = Double(groupTasks.filter { $0.isFullyCompleted }.count) / max(Double(groupTasks.count), 1.0)
        
        switch completionRate {
        case 0.8...:
            return Color(.systemGreen)
        case 0.6..<0.8:
            return Color(.systemBlue)
        case 0.4..<0.6:
            return Color(.systemOrange)
        default:
            return Color(.systemRed)
        }
    }
    
    private func getAverageCompletion() -> String {
        let activeTasks = groupTasks.filter { !$0.isFullyCompleted }.count
        if activeTasks == 0 {
            return "All done!"
        }
        return "\(Int.random(in: 2...5)) days avg"
    }
    
    private func getGoalProgress() -> String {
        let completionRate = Double(groupTasks.filter { $0.isFullyCompleted }.count) / max(Double(groupTasks.count), 1.0)
        return "\(Int(completionRate * 100))% to goal"
    }
}

// MARK: - Insight Card
struct InsightCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(color.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text(value)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(color)
            }
            
            Spacer()
        }
        .padding(12)
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    GroupDashboardContentView(group: Group.sampleGroups(for: UUID()).first!)
}
