//
//  GroupInvitationsView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Group Invitations View
struct GroupInvitationsView: View {
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingInviteMember = false
    @State private var selectedInvitation: GroupInvitation?
    @State private var showingInvitationDetail = false
    
    private var pendingInvitations: [GroupInvitation] {
        groupManager.groupInvitations.filter { 
            $0.groupId == group.id && $0.status == .pending 
        }
    }
    
    private var recentActivity: [GroupInvitation] {
        groupManager.groupInvitations.filter { 
            $0.groupId == group.id && $0.status != .pending 
        }.sorted { $0.respondedAt ?? Date.distantPast > $1.respondedAt ?? Date.distantPast }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(spacing: 20) {
                    // Pending Invitations Section
                    if !pendingInvitations.isEmpty {
                        pendingInvitationsSection
                    }
                    
                    // Quick Invite Section
                    quickInviteSection
                    
                    // Recent Activity Section
                    if !recentActivity.isEmpty {
                        recentActivitySection
                    }
                    
                    // Empty State
                    if pendingInvitations.isEmpty && recentActivity.isEmpty {
                        emptyStateSection
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showingInviteMember) {
            InviteMemberView(group: group)
        }
        .sheet(isPresented: $showingInvitationDetail) {
            if let invitation = selectedInvitation {
                InvitationDetailView(invitation: invitation, group: group)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Invitations")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Manage group invitations")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // Pending count badge
            if !pendingInvitations.isEmpty {
                HStack(spacing: 4) {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 10, weight: .medium))
                    Text("\(pendingInvitations.count)")
                        .font(.system(size: 12, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(DesignSystem.Colors.sunriseOrange)
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Pending Invitations Section
    private var pendingInvitationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "clock.fill")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)
                
                Text("Pending Invitations")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                Text("\(pendingInvitations.count)")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(DesignSystem.Colors.sunriseOrange)
                    .cornerRadius(4)
            }
            
            VStack(spacing: 8) {
                ForEach(pendingInvitations, id: \.id) { invitation in
                    PendingInvitationCard(
                        invitation: invitation,
                        onAccept: { acceptInvitation(invitation) },
                        onDecline: { declineInvitation(invitation) },
                        onTap: { 
                            selectedInvitation = invitation
                            showingInvitationDetail = true
                        }
                    )
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Quick Invite Section
    private var quickInviteSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "person.badge.plus")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.blue)
                
                Text("Invite New Members")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
            }
            
            Button(action: { showingInviteMember = true }) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.blue)
                    
                    Text("Send Invitation")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(.blue)
                }
                .padding(16)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("Recent Activity")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                ForEach(recentActivity.prefix(5), id: \.id) { invitation in
                    RecentActivityCard(invitation: invitation)
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Empty State Section
    private var emptyStateSection: some View {
        VStack(spacing: 20) {
            Spacer()
            
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                
                Image(systemName: "envelope.badge.person.crop")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(.blue)
            }
            
            VStack(spacing: 8) {
                Text("No Invitations Yet")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Start inviting friends to join your group!")
                    .font(.system(size: 15, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: { showingInviteMember = true }) {
                HStack(spacing: 10) {
                    Image(systemName: "person.badge.plus")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Invite Members")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.blue.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
                .shadow(color: Color.blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(PressedButtonStyle())
            
            Spacer()
        }
    }
    
    // MARK: - Actions
    private func acceptInvitation(_ invitation: GroupInvitation) {
        _Concurrency.Task {
            let success = await groupManager.acceptInvitation(invitation.id)
            if success {
                print("✅ Invitation accepted successfully")
            }
        }
    }
    
    private func declineInvitation(_ invitation: GroupInvitation) {
        _Concurrency.Task {
            let success = await groupManager.declineInvitation(invitation.id)
            if success {
                print("✅ Invitation declined successfully")
            }
        }
    }
}

// MARK: - Supporting Views

// MARK: - Pending Invitation Card
struct PendingInvitationCard: View {
    let invitation: GroupInvitation
    let onAccept: () -> Void
    let onDecline: () -> Void
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with user info
                HStack(spacing: 12) {
                    // Avatar placeholder
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue, Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 40, height: 40)

                        Text("U")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text("User invited by Member")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)

                        Text(invitation.createdAt, style: .relative)
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    Spacer()

                    // Status badge
                    HStack(spacing: 4) {
                        Circle()
                            .fill(DesignSystem.Colors.sunriseOrange)
                            .frame(width: 6, height: 6)
                        Text("Pending")
                            .font(.system(size: 10, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.sunriseOrange)
                    }
                }

                // Message if available
                if let message = invitation.message, !message.isEmpty {
                    Text(message)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 6)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                }

                // Action buttons
                HStack(spacing: 12) {
                    Button(action: onDecline) {
                        HStack(spacing: 6) {
                            Image(systemName: "xmark")
                                .font(.system(size: 12, weight: .semibold))
                            Text("Decline")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.red)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: onAccept) {
                        HStack(spacing: 6) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12, weight: .semibold))
                            Text("Accept")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.green)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Spacer()
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray6), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Recent Activity Card
struct RecentActivityCard: View {
    let invitation: GroupInvitation

    var body: some View {
        HStack(spacing: 12) {
            // Status icon
            Image(systemName: invitation.status == .accepted ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(invitation.status == .accepted ? .green : .red)

            VStack(alignment: .leading, spacing: 2) {
                Text("Invitation \(invitation.status == .accepted ? "accepted" : "declined")")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)

                if let respondedAt = invitation.respondedAt {
                    Text(respondedAt, style: .relative)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            Spacer()
        }
        .padding(12)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
    }
}

// MARK: - Invitation Detail View
struct InvitationDetailView: View {
    let invitation: GroupInvitation
    let group: Group
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Invitation details content
                Text("Invitation Details")
                    .font(.title2)
                    .fontWeight(.bold)

                // TODO: Add detailed invitation view

                Spacer()
            }
            .padding()
            .navigationTitle("Invitation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

// MARK: - Pressed Button Style
struct PressedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
