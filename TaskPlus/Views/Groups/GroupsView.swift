//
//  GroupsView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Groups Main View
struct GroupsView: View {
    @StateObject private var groupManager = GroupManager.shared
    @State private var showingCreateGroup = false
    @State private var searchText = ""
    
    var filteredGroups: [Group] {
        let userGroups = groupManager.getUserGroups()
        
        if searchText.isEmpty {
            return userGroups
        } else {
            return userGroups.filter { group in
                group.name.localizedCaseInsensitiveContains(searchText) ||
                group.description?.localizedCaseInsensitiveContains(searchText) == true
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection

            // Pending Invitations
            pendingInvitationsSection

            // Search Bar
            searchBar

            // Content
            if groupManager.isLoading && groupManager.groups.isEmpty {
                loadingState
            } else if filteredGroups.isEmpty && !groupManager.isLoading {
                emptyState
            } else {
                groupsList
            }
        }
        .sheet(isPresented: $showingCreateGroup) {
            CreateGroupView()
        }
        .onAppear {
            loadGroupsIfNeeded()
        }
        .refreshable {
            await groupManager.refreshData()
        }
        .alert("Error", isPresented: .constant(groupManager.errorMessage != nil)) {
            Button("OK") {
                groupManager.clearError()
            }
            Button("Retry") {
                _Concurrency.Task {
                    await groupManager.loadUserGroups()
                }
            }
        } message: {
            Text(groupManager.errorMessage ?? "")
        }
    }

    // MARK: - Helper Methods
    private func loadGroupsIfNeeded() {
        // جلب المجموعات إذا كانت القائمة فارغة أو عند أول ظهور
        if groupManager.groups.isEmpty && !groupManager.isLoading {
            _Concurrency.Task {
                await groupManager.loadUserGroups()
            }
        }
    }
    
    // MARK: - UI Components
    @ViewBuilder
    private var pendingInvitationsSection: some View {
        let pendingInvitations = groupManager.getPendingInvitations()

        if !pendingInvitations.isEmpty {
            VStack(spacing: 0) {
                // Header
                HStack {
                    Image(systemName: "envelope.fill")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.sunriseOrange)

                    Text("Group Invitations (\(pendingInvitations.count))")
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.text)

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 12)
                .padding(.bottom, 8)

                // Invitations List
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(pendingInvitations, id: \.id) { invitation in
                            GroupInvitationCard(invitation: invitation)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.bottom, 12)
            }
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .fill(Color(.systemGray6))
                    .frame(height: 0.5),
                alignment: .bottom
            )
        }
    }

    private var headerSection: some View {
        HStack {
            Text("\(filteredGroups.count) groups")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Spacer()

            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    showingCreateGroup = true
                }
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color(.systemGreen), Color(.systemTeal)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                    .shadow(color: Color(.systemGreen).opacity(0.3), radius: 4, x: 0, y: 2)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 12)
    }
    
    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            TextField("Search groups...", text: $searchText)
                .font(.system(size: 15, weight: .regular))
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        searchText = ""
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
    }
    
    private var emptyState: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "person.3.fill")
                .font(.system(size: 56))
                .foregroundColor(Color(.systemGreen).opacity(0.7))
            
            Text("No Groups Yet")
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(DesignSystem.Colors.text)
            
            Text("Create your first group to start collaborating!")
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            TaskMateButton("Create Your First Group") {
                showingCreateGroup = true
            }
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .transition(.opacity.combined(with: .scale))
    }

    private var loadingState: some View {
        VStack(spacing: 20) {
            Spacer()

            ProgressView()
                .scaleEffect(1.2)
                .progressViewStyle(CircularProgressViewStyle(tint: Color(.systemGreen)))

            Text("Loading Groups...")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(DesignSystem.Colors.text)

            Text("Fetching your groups from the cloud")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)

            Spacer()
        }
        .transition(.opacity.combined(with: .scale))
    }
    
    private var groupsList: some View {
        List {
            if groupManager.isLoading {
                // Loading indicator
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(0.9)
                    Text("Loading groups...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.leading, 8)
                    Spacer()
                }
                .padding(.vertical, 16)
                .listRowBackground(Color.clear)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets())
            }
            
            ForEach(filteredGroups) { group in
                NavigationLink(value: group) {
                    GroupRowView(group: group)
                }
                .listRowBackground(Color.clear)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 3, leading: 20, bottom: 3, trailing: 20))
                .transition(.asymmetric(
                    insertion: .move(edge: .trailing).combined(with: .opacity),
                    removal: .move(edge: .leading).combined(with: .opacity)
                ))
            }
        }
        .listStyle(PlainListStyle())
        .animation(.easeInOut(duration: 0.3), value: filteredGroups.count)
    }
}

// MARK: - Group Row View
struct GroupRowView: View {
    let group: Group
    @StateObject private var groupManager = GroupManager.shared
    @State private var isPressed = false
    
    private var memberCount: Int {
        groupManager.getGroupMembers(group.id).count
    }
    
    private var taskCount: Int {
        groupManager.getGroupTasks(group.id).count
    }
    
    private var completedTaskCount: Int {
        groupManager.getGroupTasks(group.id).filter { $0.isFullyCompleted }.count
    }
    
    var body: some View {
            HStack(spacing: 16) {
                // Group Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(.systemGreen), Color(.systemTeal)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Text(String(group.name.prefix(2)).uppercased())
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }
                
                // Group Info
                VStack(alignment: .leading, spacing: 6) {
                    // Name and Privacy
                    HStack {
                        Text(group.name)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                            .lineLimit(1)
                        
                        if group.isPrivate {
                            Image(systemName: "lock.fill")
                                .font(.system(size: 10))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        
                        Spacer()
                    }
                    
                    // Description
                    if let description = group.description, !description.isEmpty {
                        Text(description)
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(1)
                    }
                    
                    // Stats
                    HStack(spacing: 12) {
                        // Members
                        HStack(spacing: 3) {
                            Image(systemName: "person.2.fill")
                                .font(.system(size: 10))
                                .foregroundColor(Color(.systemBlue))
                            Text("\(memberCount)")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(Color(.systemBlue))
                        }
                        
                        // Tasks
                        HStack(spacing: 3) {
                            Image(systemName: "list.bullet")
                                .font(.system(size: 10))
                                .foregroundColor(Color(.systemOrange))
                            Text("\(completedTaskCount)/\(taskCount)")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(Color(.systemOrange))
                        }
                        
                        Spacer()
                        
                        // Owner indicator
                        if let authenticatedUserId = SupabaseManager.shared.authenticatedUserId,
                           group.isOwner(authenticatedUserId) {
                            Text("Owner")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(Color(.systemGreen))
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color(.systemGreen).opacity(0.1))
                                .cornerRadius(4)
                        }
                    }
                }
            }
            .padding(.horizontal, 14)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(
                        color: isPressed ? Color(.systemGreen).opacity(0.15) : Color.black.opacity(0.05),
                        radius: isPressed ? 8 : 3,
                        x: 0,
                        y: isPressed ? 4 : 2
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isPressed ? Color(.systemGreen).opacity(0.3) : Color(.systemGray6),
                                lineWidth: isPressed ? 1.5 : 0.5
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

// MARK: - Group Invitation Card
struct GroupInvitationCard: View {
    let invitation: GroupInvitation
    @StateObject private var groupManager = GroupManager.shared
    @State private var isProcessing = false

    private var groupName: String {
        groupManager.groups.first { $0.id == invitation.groupId }?.name ?? "Unknown Group"
    }

    var body: some View {
        VStack(spacing: 12) {
            // Group Info
            VStack(spacing: 6) {
                Image(systemName: "person.3.fill")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.sunriseOrange)

                Text(groupName)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.text)
                    .lineLimit(1)

                if let message = invitation.message {
                    Text(message)
                        .font(.system(size: 11, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
            }

            // Action Buttons
            HStack(spacing: 8) {
                // Decline Button
                Button(action: {
                    declineInvitation()
                }) {
                    Text("Decline")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 32)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
                .disabled(isProcessing)

                // Accept Button
                Button(action: {
                    acceptInvitation()
                }) {
                    if isProcessing {
                        ProgressView()
                            .scaleEffect(0.7)
                            .foregroundColor(.white)
                    } else {
                        Text("Accept")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 32)
                .background(DesignSystem.Colors.sunriseOrange)
                .cornerRadius(8)
                .disabled(isProcessing)
            }
        }
        .padding(12)
        .frame(width: 160)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(DesignSystem.Colors.sunriseOrange.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 2)
    }

    private func acceptInvitation() {
        isProcessing = true

        _Concurrency.Task {
            let success = await groupManager.acceptInvitation(invitation.id)

            await MainActor.run {
                isProcessing = false
                if success {
                    print("✅ Invitation accepted successfully")
                } else {
                    print("❌ Failed to accept invitation")
                }
            }
        }
    }

    private func declineInvitation() {
        isProcessing = true

        _Concurrency.Task {
            let success = await groupManager.declineInvitation(invitation.id)

            await MainActor.run {
                isProcessing = false
                if success {
                    print("✅ Invitation declined successfully")
                } else {
                    print("❌ Failed to decline invitation")
                }
            }
        }
    }
}

#Preview {
    GroupsView()
        .environmentObject(DataManager.shared)
}
