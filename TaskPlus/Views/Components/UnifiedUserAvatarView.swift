//
//  UnifiedUserAvatarView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - Unified User Avatar View
struct UnifiedUserAvatarView: View {
    let userId: UUID
    let size: CGFloat
    let showOnlineStatus: Bool
    
    @StateObject private var userInfoManager = UserInfoManager.shared
    @State private var userInfo: UnifiedUserInfo?
    @State private var avatarImage: UIImage?
    @State private var isLoading = true
    
    init(userId: UUID, size: CGFloat = 40, showOnlineStatus: Bool = false) {
        self.userId = userId
        self.size = size
        self.showOnlineStatus = showOnlineStatus
    }
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(userInfo?.avatarBackgroundColor ?? LinearGradient(
                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: size, height: size)
            
            // Avatar content
            Group {
                if let avatarImage = avatarImage {
                    // User's actual image
                    Image(uiImage: avatarImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: size, height: size)
                        .clipShape(Circle())
                } else if let userInfo = userInfo {
                    // User initials
                    Text(userInfo.initials)
                        .font(.system(size: size * 0.4, weight: .semibold))
                        .foregroundColor(.white)
                } else if isLoading {
                    // Loading state
                    ProgressView()
                        .scaleEffect(0.7)
                        .foregroundColor(.white)
                } else {
                    // Fallback
                    Image(systemName: "person.fill")
                        .font(.system(size: size * 0.5))
                        .foregroundColor(.white)
                }
            }
            
            // Online status indicator
            if showOnlineStatus, let userInfo = userInfo, userInfo.isOnline {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color.green)
                            .frame(width: size * 0.25, height: size * 0.25)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 2)
                            )
                            .offset(x: -2, y: -2)
                    }
                }
            }
        }
        .task {
            await loadUserData()
        }
        .onChange(of: userId) { _ in
            Task {
                await loadUserData()
            }
        }
    }
    
    private func loadUserData() async {
        isLoading = true
        
        // Load user info
        if let info = await userInfoManager.getUserInfo(userId) {
            await MainActor.run {
                self.userInfo = info
            }
            
            // Load avatar image if available
            if let image = await userInfoManager.getUserAvatar(info.avatarURL) {
                await MainActor.run {
                    self.avatarImage = image
                }
            }
        }
        
        await MainActor.run {
            self.isLoading = false
        }
    }
}

// MARK: - User Info Row (for lists)
struct UnifiedUserInfoRow: View {
    let userId: UUID
    let showUsername: Bool
    let avatarSize: CGFloat
    
    @StateObject private var userInfoManager = UserInfoManager.shared
    @State private var userInfo: UnifiedUserInfo?
    
    init(userId: UUID, showUsername: Bool = true, avatarSize: CGFloat = 40) {
        self.userId = userId
        self.showUsername = showUsername
        self.avatarSize = avatarSize
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Avatar
            UnifiedUserAvatarView(userId: userId, size: avatarSize)
            
            // User info
            if showUsername {
                VStack(alignment: .leading, spacing: 2) {
                    if let userInfo = userInfo {
                        Text(userInfo.displayName)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text("@\(userInfo.username)")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    } else {
                        // Loading placeholders
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 100, height: 16)
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 80, height: 14)
                    }
                }
                
                Spacer()
            }
        }
        .task {
            await loadUserInfo()
        }
        .onChange(of: userId) { _ in
            Task {
                await loadUserInfo()
            }
        }
    }
    
    private func loadUserInfo() async {
        if let info = await userInfoManager.getUserInfo(userId) {
            await MainActor.run {
                self.userInfo = info
            }
        }
    }
}

// MARK: - Multiple User Avatars (for group display)
struct MultipleUnifiedUserAvatarsView: View {
    let userIds: [UUID]
    let maxVisible: Int
    let avatarSize: CGFloat
    
    @StateObject private var userInfoManager = UserInfoManager.shared
    @State private var userInfos: [UnifiedUserInfo] = []
    
    init(userIds: [UUID], maxVisible: Int = 3, avatarSize: CGFloat = 32) {
        self.userIds = userIds
        self.maxVisible = maxVisible
        self.avatarSize = avatarSize
    }
    
    var body: some View {
        HStack(spacing: -8) {
            ForEach(Array(userIds.prefix(maxVisible).enumerated()), id: \.offset) { index, userId in
                UnifiedUserAvatarView(userId: userId, size: avatarSize)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                    )
                    .zIndex(Double(maxVisible - index))
            }
            
            // Show count if there are more users
            if userIds.count > maxVisible {
                ZStack {
                    Circle()
                        .fill(Color.gray.opacity(0.8))
                        .frame(width: avatarSize, height: avatarSize)
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                    
                    Text("+\(userIds.count - maxVisible)")
                        .font(.system(size: avatarSize * 0.3, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
        }
        .task {
            await loadUserInfos()
        }
        .onChange(of: userIds) { _ in
            Task {
                await loadUserInfos()
            }
        }
    }
    
    private func loadUserInfos() async {
        let infos = await userInfoManager.getUsersInfo(userIds)
        await MainActor.run {
            self.userInfos = infos
        }
    }
}

// MARK: - Preview
struct UnifiedUserAvatarView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            UnifiedUserAvatarView(userId: UUID(), size: 60)
            
            UnifiedUserInfoRow(userId: UUID())
            
            MultipleUnifiedUserAvatarsView(userIds: [UUID(), UUID(), UUID(), UUID()])
        }
        .padding()
    }
}
