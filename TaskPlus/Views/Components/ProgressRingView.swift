//
//  ProgressRingView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Progress Ring Component for Dashboard Analytics
//

import SwiftUI

// MARK: - Progress Ring View
struct ProgressRingView: View {
    let progress: Double // 0.0 to 1.0
    let title: String
    let subtitle: String
    let size: CGFloat
    let lineWidth: CGFloat
    let ringType: RingType
    
    @State private var animatedProgress: Double = 0.0
    
    enum RingType {
        case today
        case week
        case allTime
        
        var colors: [Color] {
            switch self {
            case .today:
                return [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.goldenHour]
            case .week:
                return [DesignSystem.Colors.goldenHour, DesignSystem.Colors.sunsetCoral]
            case .allTime:
                return [DesignSystem.Colors.sunsetCoral, DesignSystem.Colors.twilightPurple]
            }
        }
        
        var progressColor: Color {
            switch progress {
            case 0.0..<0.3:
                return Color.red.opacity(0.8)
            case 0.3..<0.7:
                return Color.orange.opacity(0.8)
            case 0.7..<0.9:
                return Color.yellow.opacity(0.8)
            case 0.9...1.0:
                return Color.green.opacity(0.8)
            default:
                return Color.gray.opacity(0.8)
            }
        }
        
        private var progress: Double {
            return 0.0 // This will be set from the parent view
        }
    }
    
    init(progress: Double, title: String, subtitle: String, size: CGFloat = 50, lineWidth: CGFloat = 4, ringType: RingType) {
        self.progress = max(0.0, min(1.0, progress))
        self.title = title
        self.subtitle = subtitle
        self.size = size
        self.lineWidth = lineWidth
        self.ringType = ringType
    }
    
    var body: some View {
        VStack(spacing: 4) {
            ZStack {
                // Background Ring
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: lineWidth)
                    .frame(width: size, height: size)
                
                // Progress Ring with Smart Color
                Circle()
                    .trim(from: 0, to: animatedProgress)
                    .stroke(
                        progressGradient,
                        style: StrokeStyle(
                            lineWidth: lineWidth,
                            lineCap: .round
                        )
                    )
                    .frame(width: size, height: size)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.2), value: animatedProgress)
                
                // Center Text
                VStack(spacing: 1) {
                    Text("\(Int(progress * 100))%")
                        .font(.system(size: size * 0.22, weight: .bold, design: .rounded))
                        .foregroundColor(progressTextColor)
                    
                    if !subtitle.isEmpty {
                        Text(subtitle)
                            .font(.system(size: size * 0.12, weight: .medium))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
            }
            
            // Title
            Text(title)
                .font(.system(size: size * 0.16, weight: .medium))
                .foregroundColor(.primary)
                .lineLimit(1)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.2).delay(0.2)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { _, newProgress in
            withAnimation(.easeInOut(duration: 0.8)) {
                animatedProgress = newProgress
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var progressGradient: AngularGradient {
        let colors = smartProgressColors
        return AngularGradient(
            colors: colors,
            center: .center,
            startAngle: .degrees(0),
            endAngle: .degrees(360 * progress)
        )
    }
    
    private var smartProgressColors: [Color] {
        switch progress {
        case 0.0..<0.1:
            return [Color.red.opacity(0.3), Color.red.opacity(0.6)]
        case 0.1..<0.3:
            return [Color.red.opacity(0.6), Color.orange.opacity(0.7)]
        case 0.3..<0.5:
            return [Color.orange.opacity(0.7), Color.yellow.opacity(0.8)]
        case 0.5..<0.7:
            return [Color.yellow.opacity(0.8), DesignSystem.Colors.goldenHour]
        case 0.7..<0.9:
            return [DesignSystem.Colors.goldenHour, DesignSystem.Colors.sunsetCoral]
        case 0.9...1.0:
            return [DesignSystem.Colors.sunsetCoral, Color.green.opacity(0.8)]
        default:
            return [Color.gray.opacity(0.3), Color.gray.opacity(0.6)]
        }
    }
    
    private var progressTextColor: Color {
        switch progress {
        case 0.0..<0.3:
            return Color.red.opacity(0.9)
        case 0.3..<0.7:
            return Color.orange.opacity(0.9)
        case 0.7..<0.9:
            return DesignSystem.Colors.goldenHour
        case 0.9...1.0:
            return Color.green.opacity(0.9)
        default:
            return Color.gray
        }
    }
}

// MARK: - Quick Stats Card
struct QuickStatsCard: View {
    let title: String
    let value: String
    let subtitle: String?
    let icon: String
    let color: Color
    let isAlert: Bool
    
    init(title: String, value: String, subtitle: String? = nil, icon: String, color: Color, isAlert: Bool = false) {
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.icon = icon
        self.color = color
        self.isAlert = isAlert
    }
    
    var body: some View {
        VStack(spacing: 6) {
            // Icon with alert indicator
            ZStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(color)
                
                if isAlert {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 8, height: 8)
                        .offset(x: 8, y: -8)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAlert)
                }
            }
            
            // Value
            Text(value)
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(.primary)
            
            // Title
            Text(title)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // Subtitle
            if let subtitle = subtitle {
                Text(subtitle)
                    .font(.system(size: 8, weight: .regular))
                    .foregroundColor(Color(.tertiaryLabel))
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray6), lineWidth: 0.5)
                )
        )
        .scaleEffect(isAlert ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isAlert)
    }
}

// MARK: - Analytics Period Selector
struct AnalyticsPeriodSelector: View {
    @Binding var selectedPeriod: AnalyticsPeriod
    
    enum AnalyticsPeriod: String, CaseIterable {
        case daily = "Daily"
        case monthly = "Monthly"
        case complete = "Complete"
        
        var icon: String {
            switch self {
            case .daily: return "calendar"
            case .monthly: return "calendar.badge.clock"
            case .complete: return "chart.bar.fill"
            }
        }
        
        var shortTitle: String {
            switch self {
            case .daily: return "Day"
            case .monthly: return "Month"
            case .complete: return "All"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(AnalyticsPeriod.allCases, id: \.self) { period in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedPeriod = period
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: period.icon)
                            .font(.system(size: 10, weight: .medium))
                        
                        Text(period.shortTitle)
                            .font(.system(size: 11, weight: .medium))
                    }
                    .foregroundColor(selectedPeriod == period ? .white : .secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(selectedPeriod == period ? DesignSystem.Colors.sunsetCoral : Color(.systemGray6))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - Task Sort Toggle
struct TaskSortToggle: View {
    @Binding var sortBy: TaskSortType
    
    enum TaskSortType: String, CaseIterable {
        case time = "Time"
        case priority = "Priority"
        
        var icon: String {
            switch self {
            case .time: return "clock"
            case .priority: return "star.fill"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(TaskSortType.allCases, id: \.self) { sortType in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        sortBy = sortType
                    }
                }) {
                    HStack(spacing: 3) {
                        Image(systemName: sortType.icon)
                            .font(.system(size: 10, weight: .medium))
                        
                        Text(sortType.rawValue)
                            .font(.system(size: 11, weight: .medium))
                    }
                    .foregroundColor(sortBy == sortType ? DesignSystem.Colors.sunsetCoral : .secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 5)
                            .fill(sortBy == sortType ? DesignSystem.Colors.sunsetCoral.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Sort direction toggle
            Button(action: {
                // Toggle sort direction logic here
            }) {
                Image(systemName: "arrow.up.arrow.down")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.secondary)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

// MARK: - Preview
struct ProgressRingView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HStack(spacing: 15) {
                ProgressRingView(
                    progress: 0.85,
                    title: "Today",
                    subtitle: "5/8",
                    size: 50,
                    ringType: .today
                )
                
                ProgressRingView(
                    progress: 0.65,
                    title: "Week",
                    subtitle: "22/28",
                    size: 50,
                    ringType: .week
                )
                
                ProgressRingView(
                    progress: 0.45,
                    title: "All Time",
                    subtitle: "Complete",
                    size: 50,
                    ringType: .allTime
                )
            }
            
            HStack(spacing: 8) {
                QuickStatsCard(
                    title: "Today",
                    value: "5/8",
                    icon: "calendar",
                    color: DesignSystem.Colors.sunriseOrange
                )
                
                QuickStatsCard(
                    title: "Streak",
                    value: "7",
                    subtitle: "days",
                    icon: "flame.fill",
                    color: DesignSystem.Colors.goldenHour
                )
                
                QuickStatsCard(
                    title: "Late",
                    value: "2",
                    icon: "exclamationmark.triangle.fill",
                    color: Color.red,
                    isAlert: true
                )
            }
        }
        .padding()
    }
}
