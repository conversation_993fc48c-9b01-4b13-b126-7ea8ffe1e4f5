//
//  DashboardTaskRow.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Task Row Component for Dashboard Display
//

import SwiftUI

// MARK: - Dashboard Task Row
struct DashboardTaskRow: View {
    let task: Task
    let onToggleCompletion: () -> Void
    
    @State private var isCompleting = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Completion Checkbox
            completionCheckbox
            
            // Task Content
            VStack(alignment: .leading, spacing: 4) {
                // Title and Priority
                HStack {
                    Text(task.title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(task.status == .completed ? .secondary : .primary)
                        .strikethrough(task.status == .completed)
                        .lineLimit(2)
                    
                    Spacer()
                    
                    if task.isImportant {
                        Image(systemName: "star.fill")
                            .font(.system(size: 10))
                            .foregroundColor(DesignSystem.Colors.goldenHour)
                    }
                }
                
                // Time and Status Info
                HStack(spacing: 8) {
                    // Due time
                    if let dueDate = task.dueDate {
                        HStack(spacing: 3) {
                            Image(systemName: "clock")
                                .font(.system(size: 9))
                                .foregroundColor(timeColor)
                            
                            Text(formatTime(dueDate))
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(timeColor)
                        }
                    }
                    
                    // Priority indicator
                    if task.priority != .medium {
                        priorityIndicator
                    }
                    
                    // Tags
                    if !task.tags.isEmpty {
                        HStack(spacing: 2) {
                            Image(systemName: "tag.fill")
                                .font(.system(size: 8))
                                .foregroundColor(.secondary)
                            
                            Text(task.tags.first ?? "")
                                .font(.system(size: 9, weight: .medium))
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }
                    
                    Spacer()
                    
                    // Status indicator
                    statusIndicator
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(backgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(borderColor, lineWidth: 0.5)
                )
        )
        .scaleEffect(isCompleting ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isCompleting)
        .animation(.easeInOut(duration: 0.3), value: task.status)
    }
    
    // MARK: - UI Components
    
    private var completionCheckbox: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                isCompleting = true
                onToggleCompletion()
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isCompleting = false
                }
            }
        }) {
            ZStack {
                Circle()
                    .stroke(checkboxBorderColor, lineWidth: 1.5)
                    .frame(width: 20, height: 20)
                
                if task.status == .completed {
                    Circle()
                        .fill(DesignSystem.Colors.success)
                        .frame(width: 20, height: 20)
                    
                    Image(systemName: "checkmark")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var priorityIndicator: some View {
        HStack(spacing: 2) {
            Image(systemName: priorityIcon)
                .font(.system(size: 8))
                .foregroundColor(priorityColor)
            
            Text(task.priority.rawValue.capitalized)
                .font(.system(size: 9, weight: .medium))
                .foregroundColor(priorityColor)
        }
    }
    
    @ViewBuilder
    private var statusIndicator: some View {
        if task.status == .completed {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 12))
                .foregroundColor(DesignSystem.Colors.success)
        } else if isOverdue {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 12))
                .foregroundColor(.red)
        } else if isDueSoon {
            Image(systemName: "clock.badge.exclamationmark")
                .font(.system(size: 12))
                .foregroundColor(.orange)
        }
    }
    
    // MARK: - Computed Properties
    
    private var backgroundColor: Color {
        if task.status == .completed {
            return DesignSystem.Colors.success.opacity(0.05)
        } else if isOverdue {
            return Color.red.opacity(0.05)
        } else if isDueSoon {
            return Color.orange.opacity(0.05)
        } else {
            return Color(.systemGray6).opacity(0.5)
        }
    }
    
    private var borderColor: Color {
        if task.status == .completed {
            return DesignSystem.Colors.success.opacity(0.3)
        } else if isOverdue {
            return Color.red.opacity(0.3)
        } else if isDueSoon {
            return Color.orange.opacity(0.3)
        } else {
            return Color(.systemGray4)
        }
    }
    
    private var checkboxBorderColor: Color {
        if task.status == .completed {
            return DesignSystem.Colors.success
        } else if isOverdue {
            return Color.red
        } else if isDueSoon {
            return Color.orange
        } else {
            return Color(.systemGray3)
        }
    }
    
    private var timeColor: Color {
        if isOverdue {
            return Color.red
        } else if isDueSoon {
            return Color.orange
        } else {
            return .secondary
        }
    }
    
    private var priorityColor: Color {
        switch task.priority {
        case .high:
            return Color.red.opacity(0.8)
        case .medium:
            return Color.orange.opacity(0.8)
        case .low:
            return Color.blue.opacity(0.8)
        }
    }
    
    private var priorityIcon: String {
        switch task.priority {
        case .high:
            return "exclamationmark.2"
        case .medium:
            return "minus"
        case .low:
            return "arrow.down"
        }
    }
    
    private var isOverdue: Bool {
        guard let dueDate = task.dueDate else { return false }
        return dueDate < Date() && task.status != .completed
    }
    
    private var isDueSoon: Bool {
        guard let dueDate = task.dueDate else { return false }
        let now = Date()
        let oneHourFromNow = now.addingTimeInterval(3600) // 1 hour
        return dueDate > now && dueDate <= oneHourFromNow && task.status != .completed
    }
    
    // MARK: - Helper Methods
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Task Priority Extension
extension Task.Priority {
    var priorityDisplayName: String {
        switch self {
        case .high:
            return "High"
        case .medium:
            return "Medium"
        case .low:
            return "Low"
        }
    }

    var priorityColor: Color {
        switch self {
        case .high:
            return .red
        case .medium:
            return .orange
        case .low:
            return .blue
        }
    }

    var priorityIcon: String {
        switch self {
        case .high:
            return "exclamationmark.2"
        case .medium:
            return "minus"
        case .low:
            return "arrow.down"
        }
    }
}

// MARK: - Preview
struct DashboardTaskRow_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 12) {
            // Regular task
            DashboardTaskRow(
                task: {
                    var task = Task(
                        title: "Team meeting with stakeholders",
                        dueDate: Date().addingTimeInterval(3600),
                        priority: .high,
                        createdByUserId: UUID()
                    )
                    task.isImportant = true
                    task.tags = ["work", "meeting"]
                    return task
                }()
            ) {
                print("Toggle completion")
            }

            // Completed task
            DashboardTaskRow(
                task: {
                    var task = Task(
                        title: "Morning workout routine",
                        dueDate: Date().addingTimeInterval(-1800),
                        priority: .medium,
                        createdByUserId: UUID()
                    )
                    task.status = .completed
                    task.completedAt = Date()
                    task.tags = ["health"]
                    return task
                }()
            ) {
                print("Toggle completion")
            }

            // Overdue task
            DashboardTaskRow(
                task: {
                    var task = Task(
                        title: "Submit quarterly report",
                        dueDate: Date().addingTimeInterval(-7200),
                        priority: .high,
                        createdByUserId: UUID()
                    )
                    task.isImportant = true
                    task.tags = ["urgent", "work"]
                    return task
                }()
            ) {
                print("Toggle completion")
            }

            // Due soon task
            DashboardTaskRow(
                task: {
                    var task = Task(
                        title: "Buy groceries for dinner",
                        dueDate: Date().addingTimeInterval(1800),
                        priority: .low,
                        createdByUserId: UUID()
                    )
                    task.tags = ["shopping"]
                    return task
                }()
            ) {
                print("Toggle completion")
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
