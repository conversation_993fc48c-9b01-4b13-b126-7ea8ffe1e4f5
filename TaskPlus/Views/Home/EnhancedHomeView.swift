//
//  EnhancedHomeView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Enhanced Home Dashboard with Analytics and Real-time Updates
//

import SwiftUI

// MARK: - Enhanced Home View
struct EnhancedHomeView: View {
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var offlineTaskManager = OfflineTaskManager.shared
    @StateObject private var networkMonitor = NetworkMonitor.shared
    
    @State private var selectedPeriod: AnalyticsPeriodSelector.AnalyticsPeriod = .complete
    @State private var todaySortBy: TaskSortToggle.TaskSortType = .time
    @State private var tomorrowSortBy: TaskSortToggle.TaskSortType = .time
    @State private var isRefreshing = false
    
    // MARK: - Computed Properties
    
    private var todayTasks: [Task] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return allTasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate >= today && dueDate < tomorrow
        }.sorted(by: sortComparator(for: todaySortBy))
    }
    
    private var tomorrowTasks: [Task] {
        let tomorrow = Calendar.current.startOfDay(for: Calendar.current.date(byAdding: .day, value: 1, to: Date())!)
        let dayAfter = Calendar.current.date(byAdding: .day, value: 1, to: tomorrow)!
        
        return allTasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate >= tomorrow && dueDate < dayAfter
        }.sorted(by: sortComparator(for: tomorrowSortBy))
    }
    
    private var allTasks: [Task] {
        return networkMonitor.isConnected ? dataManager.tasks : offlineTaskManager.tasks
    }
    
    private var overdueTasks: [Task] {
        let now = Date()
        return allTasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate < now && task.status != .completed
        }
    }
    
    // Analytics computed properties
    private var todayProgress: Double {
        guard !todayTasks.isEmpty else { return 0.0 }
        let completed = todayTasks.filter { $0.status == .completed }.count
        return Double(completed) / Double(todayTasks.count)
    }
    
    private var weekProgress: Double {
        let weekTasks = getTasksForPeriod("weekly")
        guard !weekTasks.isEmpty else { return 0.0 }
        let completed = weekTasks.filter { $0.status == .completed }.count
        return Double(completed) / Double(weekTasks.count)
    }
    
    private var allTimeProgress: Double {
        let allTimeTasks = getTasksForPeriod(.complete)
        guard !allTimeTasks.isEmpty else { return 0.0 }
        let completed = allTimeTasks.filter { $0.status == .completed }.count
        return Double(completed) / Double(allTimeTasks.count)
    }
    
    private var currentStreak: Int {
        // Calculate consecutive days with completed tasks
        // This is a simplified version - you might want to implement more sophisticated logic
        return 7 // Placeholder
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Welcome Header
                    welcomeHeader
                    
                    // Quick Stats
                    quickStatsSection
                    
                    // Progress Overview
                    progressOverviewSection
                    
                    // Today's Tasks
                    todayTasksSection
                    
                    // Tomorrow's Tasks
                    tomorrowTasksSection
                    
                    // Weekly Insights
                    weeklyInsightsSection
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
            .refreshable {
                await refreshData()
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.sunriseOrange.opacity(0.03),
                        DesignSystem.Colors.dawnBlue.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
        }
    }
    
    // MARK: - UI Sections
    
    private var welcomeHeader: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(greetingMessage)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text("Ready to make today productive?")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Network status indicator
                if !networkMonitor.isConnected {
                    Image(systemName: "wifi.slash")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.orange)
                }
            }
            .padding(.top, 8)
        }
    }
    
    private var quickStatsSection: some View {
        HStack(spacing: 8) {
            QuickStatsCard(
                title: "Today",
                value: "\(todayTasks.filter { $0.status == .completed }.count)/\(todayTasks.count)",
                icon: "calendar",
                color: DesignSystem.Colors.sunriseOrange
            )
            
            QuickStatsCard(
                title: "Done",
                value: "\(Int(allTimeProgress * 100))%",
                icon: "checkmark.circle.fill",
                color: DesignSystem.Colors.success
            )
            
            QuickStatsCard(
                title: "Streak",
                value: "\(currentStreak)",
                subtitle: "days",
                icon: "flame.fill",
                color: DesignSystem.Colors.goldenHour
            )
            
            QuickStatsCard(
                title: "Late",
                value: "\(overdueTasks.count)",
                icon: "exclamationmark.triangle.fill",
                color: Color.red,
                isAlert: !overdueTasks.isEmpty
            )
        }
    }
    
    private var progressOverviewSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("📈 Progress Overview")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                AnalyticsPeriodSelector(selectedPeriod: $selectedPeriod)
            }
            
            HStack(spacing: 20) {
                ProgressRingView(
                    progress: todayProgress,
                    title: "Today",
                    subtitle: "\(todayTasks.filter { $0.status == .completed }.count)/\(todayTasks.count)",
                    size: 55,
                    ringType: .today
                )
                
                ProgressRingView(
                    progress: weekProgress,
                    title: "This Week",
                    subtitle: getWeekSubtitle(),
                    size: 55,
                    ringType: .week
                )
                
                ProgressRingView(
                    progress: allTimeProgress,
                    title: "All Time",
                    subtitle: "Complete",
                    size: 55,
                    ringType: .allTime
                )
            }
            .padding(.vertical, 8)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var todayTasksSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("📅 Today's Tasks")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                TaskSortToggle(sortBy: $todaySortBy)
            }
            
            if todayTasks.isEmpty {
                emptyTasksView(message: "No tasks for today", icon: "sun.max")
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(todayTasks) { task in
                        DashboardTaskRow(task: task) {
                            toggleTaskCompletion(task)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var tomorrowTasksSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("📅 Tomorrow's Tasks")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                TaskSortToggle(sortBy: $tomorrowSortBy)
            }
            
            if tomorrowTasks.isEmpty {
                emptyTasksView(message: "No tasks for tomorrow", icon: "moon")
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(tomorrowTasks) { task in
                        DashboardTaskRow(task: task) {
                            toggleTaskCompletion(task)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var weeklyInsightsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("📊 Weekly Insights")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                HStack {
                    Text("This Week: \(Int(weekProgress * 100))%")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("Avg: \(getAverageTasksPerDay()) tasks/day")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.secondary)
                }
                
                if !overdueTasks.isEmpty {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.red)
                        
                        Text("\(overdueTasks.count) overdue tasks need attention")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.red)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Helper Views
    
    private func emptyTasksView(message: String, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.secondary)
            
            Text(message)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(height: 60)
    }
    
    // MARK: - Helper Methods
    
    private var greetingMessage: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "Good Morning! 🌅"
        case 12..<17:
            return "Good Afternoon! ☀️"
        case 17..<21:
            return "Good Evening! 🌅"
        default:
            return "Good Night! 🌙"
        }
    }
    
    private func sortComparator(for sortType: TaskSortToggle.TaskSortType) -> (Task, Task) -> Bool {
        switch sortType {
        case .time:
            return { task1, task2 in
                guard let date1 = task1.dueDate, let date2 = task2.dueDate else {
                    return task1.dueDate != nil
                }
                return date1 < date2
            }
        case .priority:
            return { task1, task2 in
                if task1.isImportant && !task2.isImportant {
                    return true
                } else if !task1.isImportant && task2.isImportant {
                    return false
                }
                return task1.createdAt > task2.createdAt
            }
        }
    }
    
    private func getTasksForPeriod(_ period: AnalyticsPeriodSelector.AnalyticsPeriod) -> [Task] {
        let calendar = Calendar.current
        let now = Date()
        
        switch period {
        case .daily:
            let startOfDay = calendar.startOfDay(for: now)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
            return allTasks.filter { task in
                guard let dueDate = task.dueDate else { return false }
                return dueDate >= startOfDay && dueDate < endOfDay
            }
        case .monthly:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.dateInterval(of: .month, for: now)?.end ?? now
            return allTasks.filter { task in
                guard let dueDate = task.dueDate else { return false }
                return dueDate >= startOfMonth && dueDate < endOfMonth
            }
        case .complete:
            return allTasks
        }
    }
    
    private func getWeekSubtitle() -> String {
        let weekTasks = getTasksForPeriod("weekly")
        let completed = weekTasks.filter { $0.status == .completed }.count
        return "\(completed)/\(weekTasks.count)"
    }
    
    private func getAverageTasksPerDay() -> String {
        let weekTasks = getTasksForPeriod("weekly")
        let average = Double(weekTasks.count) / 7.0
        return String(format: "%.1f", average)
    }
    
    private func toggleTaskCompletion(_ task: Task) {
        if networkMonitor.isConnected {
            _Concurrency.Task {
                await dataManager.toggleTaskCompletion(task)
            }
        } else {
            _Concurrency.Task {
                await offlineTaskManager.toggleTaskCompletion(task)
            }
        }
    }
    
    private func refreshData() async {
        isRefreshing = true
        
        if networkMonitor.isConnected {
            await dataManager.syncDataIfNeeded()
        } else {
            await offlineTaskManager.refreshData()
        }
        
        isRefreshing = false
    }
}

// MARK: - Extensions for weekly tasks
extension EnhancedHomeView {
    private func getTasksForPeriod(_ period: String) -> [Task] {
        let calendar = Calendar.current
        let now = Date()
        
        switch period {
        case "weekly":
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.end ?? now
            return allTasks.filter { task in
                guard let dueDate = task.dueDate else { return false }
                return dueDate >= startOfWeek && dueDate < endOfWeek
            }
        default:
            return allTasks
        }
    }
}

// MARK: - Preview
struct EnhancedHomeView_Previews: PreviewProvider {
    static var previews: some View {
        EnhancedHomeView()
            .environmentObject(DataManager.shared)
    }
}
