//
//  DesignSystem.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - TaskMate Design System
struct DesignSystem {
    
    // MARK: - Color Palette
    struct Colors {
        // Primary Sunrise/Sunset Colors
        static let sunriseOrange = Color(hex: "#FFD0A0")
        static let sunsetCoral = Color(hex: "#FC8B61")
        static let goldenHour = Color(hex: "#FFCB77")
        
        // Supporting Colors
        static let dawnBlue = Color(hex: "#E8F4FD")
        static let twilightPurple = Color(hex: "#F5F0FF")
        static let warmGray = Color(hex: "#F8F9FA")
        
        // Semantic Colors
        static let success = Color(hex: "#4CAF50")
        static let warning = Color(hex: "#FF9800")
        static let error = Color(hex: "#F44336")
        static let info = Color(hex: "#2196F3")
        
        // Text Colors
        static let textPrimary = Color(hex: "#1A1A1A")
        static let textSecondary = Color(hex: "#666666")
        static let textTertiary = Color(hex: "#999999")
        static let textInverse = Color.white
        
        // Background Colors
        static let backgroundPrimary = Color.white
        static let backgroundSecondary = warmGray
        static let backgroundCard = Color.white
        
        // Priority Colors
        static let priorityHigh = sunsetCoral
        static let priorityMedium = goldenHour
        static let priorityLow = dawnBlue

        // Convenience aliases for common usage
        static let primary = sunsetCoral
        static let secondary = sunriseOrange
        static let background = backgroundPrimary
        static let surface = backgroundCard
        static let text = textPrimary
        static let border = Color(hex: "#E0E0E0")
    }
    
    // MARK: - Typography
    struct Typography {
        // Font Sizes
        static let displayLarge: CGFloat = 32
        static let displayMedium: CGFloat = 28
        static let displaySmall: CGFloat = 24
        
        static let headlineLarge: CGFloat = 22
        static let headlineMedium: CGFloat = 20
        static let headlineSmall: CGFloat = 18
        
        static let bodyLarge: CGFloat = 16
        static let bodyMedium: CGFloat = 14
        static let bodySmall: CGFloat = 12
        
        static let captionLarge: CGFloat = 12
        static let captionMedium: CGFloat = 11
        static let captionSmall: CGFloat = 10
        
        // Font Weights
        static let weightBold = Font.Weight.bold
        static let weightSemiBold = Font.Weight.semibold
        static let weightMedium = Font.Weight.medium
        static let weightRegular = Font.Weight.regular
        static let weightLight = Font.Weight.light
        
        // Text Styles
        static let displayLargeStyle = Font.system(size: displayLarge, weight: weightBold, design: .rounded)
        static let displayMediumStyle = Font.system(size: displayMedium, weight: weightBold, design: .rounded)
        static let displaySmallStyle = Font.system(size: displaySmall, weight: weightBold, design: .rounded)
        
        static let headlineLargeStyle = Font.system(size: headlineLarge, weight: weightSemiBold, design: .rounded)
        static let headlineMediumStyle = Font.system(size: headlineMedium, weight: weightSemiBold, design: .rounded)
        static let headlineSmallStyle = Font.system(size: headlineSmall, weight: weightSemiBold, design: .rounded)
        
        static let bodyLargeStyle = Font.system(size: bodyLarge, weight: weightRegular, design: .default)
        static let bodyMediumStyle = Font.system(size: bodyMedium, weight: weightRegular, design: .default)
        static let bodySmallStyle = Font.system(size: bodySmall, weight: weightRegular, design: .default)
        
        static let captionLargeStyle = Font.system(size: captionLarge, weight: weightMedium, design: .default)
        static let captionMediumStyle = Font.system(size: captionMedium, weight: weightMedium, design: .default)
        static let captionSmallStyle = Font.system(size: captionSmall, weight: weightMedium, design: .default)

        // Convenience aliases for common usage
        static let largeTitle = displayLargeStyle
        static let title1 = displayMediumStyle
        static let title2 = displaySmallStyle
        static let headline = headlineLargeStyle
        static let body = bodyLargeStyle
        static let bodyBold = Font.system(size: bodyLarge, weight: weightSemiBold, design: .default)
        static let caption = captionLargeStyle
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let small = Shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        static let medium = Shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
        static let large = Shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
    }
    
    struct Shadow {
        let color: Color
        let radius: CGFloat
        let x: CGFloat
        let y: CGFloat
    }
    
    // MARK: - Animation
    struct Animation {
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.2)
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.3)
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.5)
        static let spring = SwiftUI.Animation.spring(response: 0.5, dampingFraction: 0.8)
        static let bouncy = SwiftUI.Animation.spring(response: 0.3, dampingFraction: 0.6)
        static let gentle = SwiftUI.Animation.spring(response: 0.6, dampingFraction: 0.9)
        static let snappy = SwiftUI.Animation.spring(response: 0.4, dampingFraction: 0.7)
    }

    // MARK: - Haptic Feedback
    enum HapticStyle {
        case light, medium, heavy, soft, rigid
    }

    struct HapticFeedback {
        static func impact(_ style: HapticStyle) {
            let generator: UIImpactFeedbackGenerator

            switch style {
            case .light:
                generator = UIImpactFeedbackGenerator(style: .light)
            case .medium:
                generator = UIImpactFeedbackGenerator(style: .medium)
            case .heavy:
                generator = UIImpactFeedbackGenerator(style: .heavy)
            case .soft:
                if #available(iOS 13.0, *) {
                    generator = UIImpactFeedbackGenerator(style: .soft)
                } else {
                    generator = UIImpactFeedbackGenerator(style: .light)
                }
            case .rigid:
                if #available(iOS 13.0, *) {
                    generator = UIImpactFeedbackGenerator(style: .rigid)
                } else {
                    generator = UIImpactFeedbackGenerator(style: .heavy)
                }
            }

            generator.impactOccurred()
        }

        static func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(type)
        }

        static func selection() {
            let generator = UISelectionFeedbackGenerator()
            generator.selectionChanged()
        }
    }
}

// MARK: - Color Extension for Hex Support
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - View Extensions for Design System
extension View {
    // Apply shadow
    func taskMateShadow(_ shadow: DesignSystem.Shadow) -> some View {
        self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }
    
    // Apply card style
    func taskMateCard() -> some View {
        self
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .taskMateShadow(DesignSystem.Shadows.small)
    }
    
    // Apply primary button style
    func taskMatePrimaryButton() -> some View {
        self
            .font(DesignSystem.Typography.bodyLargeStyle)
            .foregroundColor(DesignSystem.Colors.textInverse)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                LinearGradient(
                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .taskMateShadow(DesignSystem.Shadows.medium)
    }
    
    // Apply secondary button style
    func taskMateSecondaryButton() -> some View {
        self
            .font(DesignSystem.Typography.bodyLargeStyle)
            .foregroundColor(DesignSystem.Colors.sunsetCoral)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.backgroundCard)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.sunsetCoral, lineWidth: 1)
            )
    }
    
    // Apply text field style
    func taskMateTextField() -> some View {
        self
            .font(DesignSystem.Typography.bodyLargeStyle)
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.dawnBlue.opacity(0.3))
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.sunriseOrange.opacity(0.3), lineWidth: 1)
            )
    }

    // Apply interactive button style with haptic feedback
    func taskMateInteractiveButton(style: DesignSystem.HapticStyle = .light) -> some View {
        self.onTapGesture {
            DesignSystem.HapticFeedback.impact(style)
        }
    }

    // Apply loading state
    func taskMateLoadingState(_ isLoading: Bool) -> some View {
        self
            .opacity(isLoading ? 0.6 : 1.0)
            .overlay(
                SwiftUI.Group {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            )
            .animation(DesignSystem.Animation.standard, value: isLoading)
    }
}

// MARK: - Priority Color Helper
extension Task.Priority {
    var color: Color {
        switch self {
        case .high: return DesignSystem.Colors.priorityHigh
        case .medium: return DesignSystem.Colors.priorityMedium
        case .low: return DesignSystem.Colors.priorityLow
        }
    }
    
    var backgroundColor: Color {
        color.opacity(0.1)
    }
}
