//
//  TestingDashboardView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Testing Dashboard for Development and QA
//

import SwiftUI

// MARK: - Testing Dashboard View
struct TestingDashboardView: View {
    @StateObject private var testSuite = ComprehensiveTestSuite.shared
    @StateObject private var performanceOptimizer = PerformanceOptimizer.shared
    @State private var selectedCategory: TestCategory = .coreData
    @State private var showingPerformanceDetails = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Main Content
                if testSuite.isRunning {
                    runningTestsView
                } else if !testSuite.testResults.isEmpty {
                    testResultsView
                } else {
                    welcomeView
                }
            }
            .navigationTitle("Testing Dashboard")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Performance") {
                        showingPerformanceDetails = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingPerformanceDetails) {
            PerformanceDetailsView()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Status Card
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Test Suite Status")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(testSuite.isRunning ? "Running Tests..." : "Ready")
                        .font(.headline)
                        .foregroundColor(testSuite.isRunning ? .blue : .primary)
                }
                
                Spacer()
                
                if let summary = testSuite.summary {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Grade: \(summary.grade)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(summary.gradeColor)
                        
                        Text("\(summary.passedTests)/\(summary.totalTests) passed")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // Action Buttons
            HStack(spacing: 12) {
                Button(action: {
                    _Concurrency.Task {
                        await testSuite.runAllTests()
                    }
                }) {
                    HStack {
                        Image(systemName: "play.circle.fill")
                        Text("Run All Tests")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                }
                .disabled(testSuite.isRunning)
                
                Button(action: {
                    _Concurrency.Task {
                        await performanceOptimizer.performComprehensiveOptimization()
                    }
                }) {
                    HStack {
                        Image(systemName: "speedometer")
                        Text("Optimize")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(25)
                }
                .disabled(performanceOptimizer.isOptimizing)
            }
        }
        .padding()
    }
    
    // MARK: - Welcome View
    private var welcomeView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "testtube.2")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            VStack(spacing: 12) {
                Text("Testing Dashboard")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Run comprehensive tests to verify offline mode, Core Data, sync engine, and performance.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            // Test Categories
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(TestCategory.allCases, id: \.self) { category in
                    CategoryCard(category: category)
                }
            }
            .padding(.horizontal)
            
            Spacer()
        }
    }
    
    // MARK: - Running Tests View
    private var runningTestsView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Progress Circle
            ZStack {
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 8)
                    .frame(width: 120, height: 120)
                
                Circle()
                    .trim(from: 0, to: testSuite.progress)
                    .stroke(
                        LinearGradient(
                            colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: testSuite.progress)
                
                Text("\(Int(testSuite.progress * 100))%")
                    .font(.title2)
                    .fontWeight(.bold)
            }
            
            VStack(spacing: 8) {
                Text("Running Tests")
                    .font(.title3)
                    .fontWeight(.semibold)
                
                Text(testSuite.currentTest)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Recent Results
            if !testSuite.testResults.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Recent Results")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(testSuite.testResults.suffix(5), id: \.testName) { result in
                                TestResultRow(result: result)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(maxHeight: 200)
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Test Results View
    private var testResultsView: some View {
        VStack(spacing: 0) {
            // Summary Card
            if let summary = testSuite.summary {
                SummaryCard(summary: summary)
                    .padding()
            }
            
            // Category Filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(TestCategory.allCases, id: \.self) { category in
                        CategoryFilterChip(
                            category: category,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal)
            }
            
            // Test Results List
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(filteredResults, id: \.testName) { result in
                        TestResultRow(result: result)
                    }
                }
                .padding()
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredResults: [TestResult] {
        testSuite.testResults.filter { result in
            result.testName.contains(selectedCategory.rawValue) ||
            selectedCategory == .coreData && result.testName.contains("Core Data") ||
            selectedCategory == .offlineMode && result.testName.contains("Offline") ||
            selectedCategory == .sync && result.testName.contains("Sync") ||
            selectedCategory == .cache && result.testName.contains("Cache") ||
            selectedCategory == .session && result.testName.contains("Session") ||
            selectedCategory == .ui && result.testName.contains("UI") ||
            selectedCategory == .performance && result.testName.contains("Performance") ||
            selectedCategory == .edgeCases && result.testName.contains("Edge")
        }
    }
}

// MARK: - Supporting Views

struct CategoryCard: View {
    let category: TestCategory
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: category.icon)
                .font(.title2)
                .foregroundColor(category.color)
            
            Text(category.rawValue)
                .font(.caption)
                .fontWeight(.medium)
                .multilineTextAlignment(.center)
        }
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct CategoryFilterChip: View {
    let category: TestCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)
                
                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? category.color : Color(.systemGray6))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(20)
        }
    }
}

struct TestResultRow: View {
    let result: TestResult
    
    var body: some View {
        HStack {
            Image(systemName: result.passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(result.statusColor)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(result.testName)
                    .font(.system(size: 14, weight: .medium))
                
                Text(result.details)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(result.status)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(result.statusColor)
                
                Text("\(String(format: "%.2f", result.duration))s")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct SummaryCard: View {
    let summary: TestSummary
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text("Test Summary")
                    .font(.headline)
                
                HStack {
                    Text("Grade:")
                    Text(summary.grade)
                        .fontWeight(.bold)
                        .foregroundColor(summary.gradeColor)
                }
                
                Text("Duration: \(String(format: "%.2f", summary.duration))s")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 8) {
                Text("\(summary.passedTests)/\(summary.totalTests)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Success Rate")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("\(String(format: "%.1f", summary.successRate * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(summary.gradeColor)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct PerformanceDetailsView: View {
    @StateObject private var performanceOptimizer = PerformanceOptimizer.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                if let metrics = performanceOptimizer.currentMetrics {
                    ScrollView {
                        VStack(alignment: .leading, spacing: 16) {
                            Text(metrics.description)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            
                            if !performanceOptimizer.recommendations.isEmpty {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Recommendations")
                                        .font(.headline)
                                    
                                    ForEach(performanceOptimizer.recommendations) { recommendation in
                                        RecommendationRow(recommendation: recommendation)
                                    }
                                }
                            }
                        }
                        .padding()
                    }
                } else {
                    Text("No performance data available")
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Performance")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RecommendationRow: View {
    let recommendation: OptimizationRecommendation
    
    var body: some View {
        HStack {
            Image(systemName: recommendation.type.icon)
                .foregroundColor(recommendation.type.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.system(size: 14, weight: .medium))
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(recommendation.priority.description)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(recommendation.priority.color)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Preview
struct TestingDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        TestingDashboardView()
    }
}
