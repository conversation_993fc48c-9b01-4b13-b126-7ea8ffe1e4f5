//
//  FriendsManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation
import SwiftUI

@MainActor
class FriendsManager: ObservableObject {
    static let shared = FriendsManager()
    
    @Published var friends: [Friend] = []
    @Published var friendRequests: [FriendRequest] = []
    @Published var searchResults: [UserInfo] = []
    @Published var isLoading = false
    @Published var lastError: String?
    @Published var errorMessage: String?
    
    private let supabaseManager = SupabaseManager.shared
    private let dataManager = DataManager.shared
    
    private init() {
        loadFriendsData()
    }
    
    // MARK: - Data Loading
    func loadFriendsData() {
        _Concurrency.Task {
            await refreshData()
        }
    }

    func refreshData() async {
        isLoading = true
        defer { isLoading = false }

        do {
            async let friendsData = loadFriendsFromDatabase()
            async let requestsData = loadFriendRequestsFromDatabase()

            friends = try await friendsData
            friendRequests = try await requestsData

        } catch {
            handleError(error)
            // Clear data on error instead of showing sample data
            friends = []
            friendRequests = []
        }
    }
    
    // MARK: - Friends Management
    func getFriends() -> [Friend] {
        return friends.filter { $0.status == .accepted }
    }
    
    func getPendingRequests() -> [FriendRequest] {
        return friendRequests.filter { $0.isPending }
    }
    
    func getAvailableFriendsForGroup(_ groupId: UUID) -> [Friend] {
        let groupMembers = GroupManager.shared.getGroupMembers(groupId)
        let memberIds = Set(groupMembers.map { $0.userId })
        
        return getFriends().filter { friend in
            !memberIds.contains(friend.friendId)
        }
    }
    
    // MARK: - Search Users
    func searchUsers(query: String) async {
        isLoading = true
        defer { isLoading = false }

        do {
            // If query is empty, show all available users
            let searchQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
            searchResults = try await searchUsersInDatabase(query: searchQuery)
        } catch {
            handleError(error)
            // Clear search results on error
            searchResults = []
        }
    }

    // Load all available users when opening search
    func loadAvailableUsers() async {
        await searchUsers(query: "")
    }
    
    // MARK: - Friend Requests
    func sendFriendRequest(to userId: UUID, message: String? = nil) async -> Bool {
        print("📤 FriendsManager: sendFriendRequest called")
        print("📤 Target user ID: \(userId)")

        guard let currentUserId = supabaseManager.authenticatedUserId else {
            print("❌ No authenticated user ID found")
            return false
        }

        print("📤 Current user ID: \(currentUserId)")

        isLoading = true
        defer { isLoading = false }

        do {
            print("📤 Creating friend request...")
            let request = FriendRequest(fromUserId: currentUserId, toUserId: userId, message: message)
            print("📤 Sending to database...")
            try await sendFriendRequestToDatabase(request)
            print("✅ Friend request sent successfully to database!")
            return true
        } catch {
            print("❌ Error sending friend request: \(error)")
            handleError(error)
            return false
        }
    }
    
    func acceptFriendRequest(_ request: FriendRequest) async -> Bool {
        print("✅ Accepting friend request from: \(request.senderInfo?.username ?? "unknown")")
        isLoading = true
        defer { isLoading = false }

        do {
            print("✅ Calling acceptFriendRequestInDatabase...")
            try await acceptFriendRequestInDatabase(request.id)
            print("✅ Database operation successful!")

            // Update local data
            print("✅ Updating local data...")
            friendRequests.removeAll { $0.id == request.id }

            // Add to friends list
            if let currentUserId = supabaseManager.authenticatedUserId {
                let newFriend = Friend(userId: currentUserId, friendId: request.fromUserId, status: .accepted)
                var updatedFriend = newFriend
                updatedFriend.friendInfo = request.senderInfo
                friends.append(updatedFriend)
                print("✅ Added friend to local list!")
            }

            print("✅ Friend request accepted successfully!")
            return true
        } catch {
            print("❌ Error accepting friend request: \(error)")
            handleError(error)
            return false
        }
    }

    func declineFriendRequest(_ request: FriendRequest) async -> Bool {
        print("❌ Declining friend request from: \(request.senderInfo?.username ?? "unknown")")
        isLoading = true
        defer { isLoading = false }

        do {
            print("❌ Calling declineFriendRequestInDatabase...")
            try await declineFriendRequestInDatabase(request.id)
            print("❌ Database operation successful!")

            // Update local data
            print("❌ Updating local data...")
            friendRequests.removeAll { $0.id == request.id }
            print("❌ Friend request declined successfully!")

            return true
        } catch {
            print("❌ Error declining friend request: \(error)")
            handleError(error)
            return false
        }
    }
    
    // MARK: - Group Invitations
    func inviteFriendsToGroup(_ friendIds: [UUID], groupId: UUID) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        // TODO: Implement real API call to add friends to group
        
        // For now, just simulate success
        return true
    }
    
    // MARK: - Utility Functions
    func isFriend(userId: UUID) -> Bool {
        return friends.contains { $0.friendId == userId && $0.status == .accepted }
    }
    
    func hasPendingRequest(userId: UUID) -> Bool {
        guard let currentUserId = supabaseManager.authenticatedUserId else { return false }
        return friendRequests.contains {
            ($0.fromUserId == currentUserId && $0.toUserId == userId) ||
            ($0.fromUserId == userId && $0.toUserId == currentUserId)
        }
    }

    // Check if current user has sent a friend request to this user
    func hasSentRequest(to userId: UUID) async -> Bool {
        guard let currentUserId = supabaseManager.authenticatedUserId else { return false }

        do {
            let response: [DatabaseFriendRequest] = try await supabaseManager.client
                .from("friend_requests")
                .select()
                .eq("from_user_id", value: currentUserId)
                .eq("to_user_id", value: userId)
                .execute()
                .value

            return !response.isEmpty
        } catch {
            print("❌ Error checking sent request: \(error)")
            return false
        }
    }
    
    func getFriendshipStatus(with userId: UUID) -> Friend.FriendshipStatus? {
        if let friend = friends.first(where: { $0.friendId == userId }) {
            return friend.status
        }
        
        if hasPendingRequest(userId: userId) {
            return .pending
        }
        
        return nil
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
    
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        lastError = error.localizedDescription
        print("❌ FriendsManager Error: \(error)")
    }
}

// MARK: - Database Extensions
extension FriendsManager {

    // MARK: - Load Friends
    private func loadFriendsFromDatabase() async throws -> [Friend] {
        guard let currentUserId = supabaseManager.authenticatedUserId else {
            throw FriendsError.notAuthenticated
        }

        // Get friends from database
        let friendsResponse: [DatabaseFriend] = try await supabaseManager.client
            .from("friends")
            .select("*")
            .eq("user_id", value: currentUserId.uuidString)
            .eq("status", value: "accepted")
            .execute()
            .value

        // Get user info for each friend
        var friends: [Friend] = []
        for dbFriend in friendsResponse {
            if let userInfo = try? await getUserInfo(userId: dbFriend.friendId) {
                var friend = Friend(
                    userId: dbFriend.userId,
                    friendId: dbFriend.friendId,
                    status: dbFriend.status
                )
                friend.friendInfo = userInfo
                friends.append(friend)
            }
        }

        return friends
    }

    // MARK: - Load Friend Requests
    private func loadFriendRequestsFromDatabase() async throws -> [FriendRequest] {
        guard let currentUserId = supabaseManager.authenticatedUserId else {
            throw FriendsError.notAuthenticated
        }

        // Get friend requests from database
        let requestsResponse: [DatabaseFriendRequest] = try await supabaseManager.client
            .from("friend_requests")
            .select("*")
            .eq("to_user_id", value: currentUserId)
            .eq("status", value: "pending")
            .order("created_at", ascending: false)
            .execute()
            .value

        // Get user info for each request sender
        var requests: [FriendRequest] = []
        for dbRequest in requestsResponse {
            if let senderInfo = try? await getUserInfo(userId: dbRequest.fromUserId) {
                var request = FriendRequest(
                    id: dbRequest.id,
                    fromUserId: dbRequest.fromUserId,
                    toUserId: dbRequest.toUserId,
                    message: dbRequest.message,
                    status: .pending,
                    createdAt: dbRequest.createdAt,
                    updatedAt: dbRequest.updatedAt
                )
                request.senderInfo = senderInfo
                requests.append(request)
            }
        }

        return requests
    }

    // MARK: - Search Users
    private func searchUsersInDatabase(query: String) async throws -> [UserInfo] {
        // Use authenticated user ID from Supabase
        guard let currentUserId = supabaseManager.authenticatedUserId else {
            print("❌ No authenticated user ID found")
            throw FriendsError.notAuthenticated
        }

        print("🔍 Searching users with query: '\(query)'")
        print("🔍 Current user ID: \(currentUserId)")
        print("🔍 Supabase client available: Yes")

        // Search users in database
        let usersResponse: [DatabaseUser]

        do {
            if query.isEmpty {
                print("🔍 Getting all users except current user...")
                // Get all users except current user
                usersResponse = try await supabaseManager.client
                    .from("users")
                    .select("*")
                    .neq("id", value: currentUserId)
                    .limit(20)
                    .execute()
                    .value
            } else {
                print("🔍 Searching users with query: '\(query)'...")
                // Search with query
                usersResponse = try await supabaseManager.client
                    .from("users")
                    .select("*")
                    .neq("id", value: currentUserId)
                    .or("display_name.ilike.%\(query)%,email.ilike.%\(query)%,username.ilike.%\(query)%")
                    .limit(20)
                    .execute()
                    .value
            }
        } catch {
            print("❌ Database query error: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            throw error
        }

        print("🔍 Found \(usersResponse.count) users from database")
        for user in usersResponse {
            print("  - User: \(user.displayName ?? "No name") (\(user.email))")
        }

        // Convert to UserInfo and filter out existing friends
        let existingFriendIds = Set(friends.map { $0.friendId })
        let pendingRequestIds = Set(friendRequests.map { $0.fromUserId })

        let filteredUsers: [UserInfo] = usersResponse.compactMap { dbUser in
            // Skip if already friends or has pending request
            if existingFriendIds.contains(dbUser.id) || pendingRequestIds.contains(dbUser.id) {
                print("  - Skipping \(dbUser.email): already friend or pending request")
                return nil
            }

            return UserInfo(
                id: dbUser.id,
                username: dbUser.username,
                email: dbUser.email,
                displayName: dbUser.displayName,
                avatarUrl: dbUser.avatarUrl,
                isOnline: dbUser.isOnline,
                lastSeen: dbUser.lastSeen,
                createdAt: dbUser.createdAt
            )
        }

        print("🔍 Returning \(filteredUsers.count) filtered users")
        return filteredUsers
    }

    // MARK: - Check Existing Friend Request
    private func checkExistingFriendRequest(from fromUserId: UUID, to toUserId: UUID) async throws -> Bool {
        print("🔍 Checking for existing friend request...")

        let response: [DatabaseFriendRequest] = try await supabaseManager.client
            .from("friend_requests")
            .select()
            .eq("from_user_id", value: fromUserId)
            .eq("to_user_id", value: toUserId)
            .execute()
            .value

        let exists = !response.isEmpty
        print("🔍 Existing request found: \(exists)")
        return exists
    }

    // MARK: - Send Friend Request
    private func sendFriendRequestToDatabase(_ request: FriendRequest) async throws {
        print("💾 Sending friend request to database...")
        print("💾 From: \(request.fromUserId)")
        print("💾 To: \(request.toUserId)")
        print("💾 Message: \(request.message ?? "nil")")

        let requestData = DatabaseFriendRequestInsert(
            fromUserId: request.fromUserId,
            toUserId: request.toUserId,
            message: request.message,
            status: "pending"
        )

        print("💾 Inserting into friend_requests table...")
        try await supabaseManager.client
            .from("friend_requests")
            .insert(requestData)
            .execute()
        print("✅ Successfully inserted friend request into database!")
    }

    // MARK: - Accept Friend Request
    private func acceptFriendRequestInDatabase(_ requestId: UUID) async throws {
        print("💾 Getting friend request from database...")
        // Get the friend request first
        let requestsResponse: [DatabaseFriendRequest] = try await supabaseManager.client
            .from("friend_requests")
            .select("*")
            .eq("id", value: requestId)
            .execute()
            .value

        guard let request = requestsResponse.first else {
            print("❌ Friend request not found in database!")
            throw FriendsError.operationFailed
        }

        print("💾 Found friend request: \(request.fromUserId) → \(request.toUserId)")

        // Update request status to accepted
        print("💾 Updating friend request status to accepted...")
        try await supabaseManager.client
            .from("friend_requests")
            .update(["status": "accepted"])
            .eq("id", value: requestId)
            .execute()
        print("💾 Friend request status updated!")

        // Create friendship records (bidirectional)
        print("💾 Creating bidirectional friendship records...")
        let friendshipData = [
            DatabaseFriendInsert(
                userId: request.fromUserId,
                friendId: request.toUserId,
                status: "accepted"
            ),
            DatabaseFriendInsert(
                userId: request.toUserId,
                friendId: request.fromUserId,
                status: "accepted"
            )
        ]

        try await supabaseManager.client
            .from("friends")
            .insert(friendshipData)
            .execute()
        print("💾 Friendship records created successfully!")
    }

    // MARK: - Decline Friend Request
    private func declineFriendRequestInDatabase(_ requestId: UUID) async throws {
        print("💾 Deleting friend request from database...")
        try await supabaseManager.client
            .from("friend_requests")
            .delete()
            .eq("id", value: requestId)
            .execute()
        print("💾 Friend request deleted successfully!")
    }

    // MARK: - Helper Functions
    private func getUserInfo(userId: UUID) async throws -> UserInfo {
        let usersResponse: [DatabaseUser] = try await supabaseManager.client
            .from("users")
            .select("*")
            .eq("id", value: userId)
            .execute()
            .value

        guard let dbUser = usersResponse.first else {
            throw FriendsError.userNotFound
        }

        return UserInfo(
            id: dbUser.id,
            username: dbUser.username,
            email: dbUser.email,
            displayName: dbUser.displayName,
            avatarUrl: dbUser.avatarUrl,
            isOnline: dbUser.isOnline,
            lastSeen: dbUser.lastSeen,
            createdAt: dbUser.createdAt
        )
    }
}

// MARK: - Database Models
private struct DatabaseUser: Codable {
    let id: UUID
    let username: String
    let email: String
    let displayName: String?
    let avatarUrl: String?
    let isOnline: Bool
    let lastSeen: Date?
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, username, email
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case isOnline = "is_online"
        case lastSeen = "last_seen"
        case createdAt = "created_at"
    }
}

private struct DatabaseFriend: Codable {
    let id: UUID
    let userId: UUID
    let friendId: UUID
    let status: Friend.FriendshipStatus
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, status
        case userId = "user_id"
        case friendId = "friend_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

private struct DatabaseFriendInsert: Codable {
    let userId: UUID
    let friendId: UUID
    let status: String

    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case friendId = "friend_id"
        case status
    }
}

private struct DatabaseFriendRequest: Codable {
    let id: UUID
    let fromUserId: UUID
    let toUserId: UUID
    let message: String?
    let status: String
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, message, status
        case fromUserId = "from_user_id"
        case toUserId = "to_user_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

private struct DatabaseFriendRequestInsert: Codable {
    let fromUserId: UUID
    let toUserId: UUID
    let message: String?
    let status: String

    enum CodingKeys: String, CodingKey {
        case fromUserId = "from_user_id"
        case toUserId = "to_user_id"
        case message, status
    }
}

// MARK: - Friends Errors
enum FriendsError: LocalizedError {
    case notAuthenticated
    case operationFailed
    case userNotFound
    case alreadyFriends
    case requestAlreadySent

    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .operationFailed:
            return "Operation failed"
        case .userNotFound:
            return "User not found"
        case .alreadyFriends:
            return "Already friends with this user"
        case .requestAlreadySent:
            return "Friend request already sent"
        }
    }
}
