//
//  SmartCache.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Smart Caching System with Expiration and Memory Management
//

import Foundation
import SwiftUI

// MARK: - Cache Entry
class CacheEntry<T> {
    let value: T
    let timestamp: Date
    let expirationInterval: TimeInterval
    
    init(value: T, expirationInterval: TimeInterval = 300) { // 5 minutes default
        self.value = value
        self.timestamp = Date()
        self.expirationInterval = expirationInterval
    }
    
    var isExpired: Bool {
        Date().timeIntervalSince(timestamp) > expirationInterval
    }
    
    var age: TimeInterval {
        Date().timeIntervalSince(timestamp)
    }
}

// MARK: - Smart Cache
@MainActor
class SmartCache<Key: Hashable, Value>: ObservableObject {
    
    // MARK: - Properties
    private var cache: [Key: CacheEntry<Value>] = [:]
    private let maxSize: Int
    private let defaultExpiration: TimeInterval
    private let cleanupInterval: TimeInterval
    
    // Statistics
    @Published var hitCount: Int = 0
    @Published var missCount: Int = 0
    @Published var evictionCount: Int = 0
    
    // MARK: - Initialization
    init(maxSize: Int = 1000, 
         defaultExpiration: TimeInterval = 300, // 5 minutes
         cleanupInterval: TimeInterval = 60) { // 1 minute
        self.maxSize = maxSize
        self.defaultExpiration = defaultExpiration
        self.cleanupInterval = cleanupInterval
        
        startPeriodicCleanup()
        print("🧠 SmartCache initialized - maxSize: \(maxSize), expiration: \(defaultExpiration)s")
    }
    
    // MARK: - Cache Operations
    
    /// Store value in cache
    func set(_ value: Value, forKey key: Key, expiration: TimeInterval? = nil) {
        let expirationTime = expiration ?? defaultExpiration
        let entry = CacheEntry(value: value, expirationInterval: expirationTime)
        
        cache[key] = entry
        
        // Cleanup if needed
        if cache.count > maxSize {
            performCleanup()
        }
        
        print("💾 Cached value for key: \(key) (expires in \(expirationTime)s)")
    }
    
    /// Retrieve value from cache
    func get(_ key: Key) -> Value? {
        guard let entry = cache[key] else {
            missCount += 1
            print("❌ Cache miss for key: \(key)")
            return nil
        }
        
        if entry.isExpired {
            cache.removeValue(forKey: key)
            missCount += 1
            print("⏰ Cache expired for key: \(key) (age: \(Int(entry.age))s)")
            return nil
        }
        
        hitCount += 1
        print("✅ Cache hit for key: \(key) (age: \(Int(entry.age))s)")
        return entry.value
    }
    
    /// Remove value from cache
    func remove(_ key: Key) {
        cache.removeValue(forKey: key)
        print("🗑️ Removed from cache: \(key)")
    }
    
    /// Clear all cache
    func clear() {
        let count = cache.count
        cache.removeAll()
        print("🧹 Cleared cache (\(count) items)")
    }
    
    /// Check if key exists and is not expired
    func contains(_ key: Key) -> Bool {
        guard let entry = cache[key] else { return false }
        
        if entry.isExpired {
            cache.removeValue(forKey: key)
            return false
        }
        
        return true
    }
    
    // MARK: - Cache Management
    
    /// Perform cleanup of expired entries
    private func performCleanup() {
        let initialCount = cache.count
        
        // Remove expired entries
        cache = cache.filter { !$0.value.isExpired }
        
        let expiredCount = initialCount - cache.count
        
        // If still over limit, remove oldest entries
        if cache.count > maxSize {
            let sortedEntries = cache.sorted { $0.value.timestamp < $1.value.timestamp }
            let toRemove = cache.count - maxSize
            
            for i in 0..<toRemove {
                cache.removeValue(forKey: sortedEntries[i].key)
                evictionCount += 1
            }
        }
        
        if expiredCount > 0 || evictionCount > 0 {
            print("🧹 Cache cleanup: \(expiredCount) expired, \(evictionCount) evicted")
        }
    }
    
    /// Start periodic cleanup
    private func startPeriodicCleanup() {
        Timer.scheduledTimer(withTimeInterval: cleanupInterval, repeats: true) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                self?.performCleanup()
            }
        }
    }
    
    // MARK: - Statistics
    
    /// Get cache statistics
    func getStatistics() -> CacheStatistics {
        let totalRequests = hitCount + missCount
        let hitRate = totalRequests > 0 ? Double(hitCount) / Double(totalRequests) : 0.0
        
        return CacheStatistics(
            size: cache.count,
            maxSize: maxSize,
            hitCount: hitCount,
            missCount: missCount,
            evictionCount: evictionCount,
            hitRate: hitRate
        )
    }
    
    /// Reset statistics
    func resetStatistics() {
        hitCount = 0
        missCount = 0
        evictionCount = 0
        print("📊 Cache statistics reset")
    }
    
    /// Get all keys in cache
    var keys: [Key] {
        return Array(cache.keys)
    }
    
    /// Get cache size
    var size: Int {
        return cache.count
    }
    
    /// Check if cache is empty
    var isEmpty: Bool {
        return cache.isEmpty
    }
}

// MARK: - Cache Statistics
struct CacheStatistics {
    let size: Int
    let maxSize: Int
    let hitCount: Int
    let missCount: Int
    let evictionCount: Int
    let hitRate: Double
    
    var utilizationPercentage: Double {
        return maxSize > 0 ? Double(size) / Double(maxSize) * 100 : 0
    }
    
    var description: String {
        return """
        Cache Statistics:
        - Size: \(size)/\(maxSize) (\(String(format: "%.1f", utilizationPercentage))%)
        - Hit Rate: \(String(format: "%.1f", hitRate * 100))%
        - Hits: \(hitCount), Misses: \(missCount)
        - Evictions: \(evictionCount)
        """
    }
}

// MARK: - Image Cache (Specialized)
@MainActor
class ImageCache: ObservableObject {
    static let shared = ImageCache()
    
    private let cache = SmartCache<String, UIImage>(
        maxSize: 200, // 200 images max
        defaultExpiration: 600, // 10 minutes
        cleanupInterval: 120 // 2 minutes cleanup
    )
    
    @Published var isLoading: Set<String> = []
    
    private init() {
        print("🖼️ ImageCache initialized")
    }
    
    /// Get image from cache or load from URL
    func getImage(from urlString: String) async -> UIImage? {
        // Check cache first
        if let cachedImage = cache.get(urlString) {
            return cachedImage
        }
        
        // Avoid duplicate requests
        if isLoading.contains(urlString) {
            while isLoading.contains(urlString) {
                try? await _Concurrency.Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            }
            return cache.get(urlString)
        }
        
        isLoading.insert(urlString)
        
        do {
            guard let url = URL(string: urlString) else {
                isLoading.remove(urlString)
                return nil
            }
            
            let (data, _) = try await URLSession.shared.data(from: url)
            
            if let image = UIImage(data: data) {
                cache.set(image, forKey: urlString)
                isLoading.remove(urlString)
                return image
            }
        } catch {
            print("❌ Failed to load image from \(urlString): \(error)")
        }
        
        isLoading.remove(urlString)
        return nil
    }
    
    /// Preload images
    func preloadImages(_ urls: [String]) {
        for url in urls {
            if !cache.contains(url) && !isLoading.contains(url) {
                _Concurrency.Task {
                    _ = await getImage(from: url)
                }
            }
        }
    }
    
    /// Get cache statistics
    func getStatistics() -> CacheStatistics {
        return cache.getStatistics()
    }
    
    /// Clear image cache
    func clearCache() {
        cache.clear()
    }
}

// MARK: - Cache Manager (Global)
@MainActor
class CacheManager: ObservableObject {
    static let shared = CacheManager()
    
    // Specialized caches
    let userCache = SmartCache<UUID, UnifiedUserInfo>(maxSize: 500, defaultExpiration: 600) // 10 minutes
    let groupCache = SmartCache<UUID, Group>(maxSize: 100, defaultExpiration: 300) // 5 minutes
    let imageCache = ImageCache.shared
    
    @Published var totalMemoryUsage: Int = 0
    
    private init() {
        print("🎯 CacheManager initialized")
        startMemoryMonitoring()
    }
    
    /// Get all cache statistics
    func getAllStatistics() -> [String: CacheStatistics] {
        return [
            "Users": userCache.getStatistics(),
            "Groups": groupCache.getStatistics(),
            "Images": imageCache.getStatistics()
        ]
    }
    
    /// Clear all caches
    func clearAllCaches() {
        userCache.clear()
        groupCache.clear()
        imageCache.clearCache()
        print("🧹 All caches cleared")
    }
    
    /// Monitor memory usage
    private func startMemoryMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                self?.updateMemoryUsage()
            }
        }
    }
    
    private func updateMemoryUsage() {
        // Simplified memory calculation
        let userCacheSize = userCache.size * 1024 // Rough estimate
        let groupCacheSize = groupCache.size * 2048 // Rough estimate
        let imageCacheSize = imageCache.getStatistics().size * 50000 // Rough estimate for images
        
        totalMemoryUsage = userCacheSize + groupCacheSize + imageCacheSize
    }
}
