//
//  TaskSyncEngine.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Task Synchronization Engine for Local-Cloud Data Sync
//

import Foundation
import SwiftUI
import Combine

// MARK: - Sync Status
enum SyncStatus {
    case idle
    case syncing
    case success
    case failed(Error)
    case conflict
    
    var description: String {
        switch self {
        case .idle: return "Ready"
        case .syncing: return "Syncing..."
        case .success: return "Synced"
        case .failed(let error): return "Failed: \(error.localizedDescription)"
        case .conflict: return "Conflict detected"
        }
    }
    
    var icon: String {
        switch self {
        case .idle: return "checkmark.circle"
        case .syncing: return "arrow.triangle.2.circlepath"
        case .success: return "checkmark.circle.fill"
        case .failed: return "exclamationmark.triangle.fill"
        case .conflict: return "exclamationmark.2"
        }
    }
    
    var color: Color {
        switch self {
        case .idle: return .gray
        case .syncing: return .blue
        case .success: return .green
        case .failed: return .red
        case .conflict: return .orange
        }
    }
}

// MARK: - Sync Conflict
struct SyncConflict {
    let taskId: UUID
    let localTask: Task
    let remoteTask: Task
    let conflictType: ConflictType
    let detectedAt: Date
    
    enum ConflictType {
        case bothModified
        case localDeletedRemoteModified
        case localModifiedRemoteDeleted
        case duplicateCreation
        
        var description: String {
            switch self {
            case .bothModified: return "Both local and remote versions were modified"
            case .localDeletedRemoteModified: return "Local deleted, remote modified"
            case .localModifiedRemoteDeleted: return "Local modified, remote deleted"
            case .duplicateCreation: return "Task created on both devices"
            }
        }
    }
}

// MARK: - Task Sync Engine
@MainActor
class TaskSyncEngine: ObservableObject {
    static let shared = TaskSyncEngine()
    
    // MARK: - Published Properties
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncTime: Date?
    @Published var syncProgress: Double = 0.0
    @Published var pendingConflicts: [SyncConflict] = []
    @Published var syncStatistics: SyncStatistics = SyncStatistics()
    
    // MARK: - Private Properties
    private let taskRepository: TaskRepository
    private let supabaseManager: SupabaseManager
    private let networkMonitor: NetworkMonitor
    private var cancellables = Set<AnyCancellable>()
    private var syncTimer: Timer?
    private var isCurrentlySyncing = false
    
    // MARK: - Sync Configuration
    private let syncInterval: TimeInterval = 300 // 5 minutes
    private let maxRetryAttempts = 3
    private let batchSize = 50
    
    // MARK: - Initialization
    private init() {
        self.taskRepository = TaskRepository()
        self.supabaseManager = SupabaseManager.shared
        self.networkMonitor = NetworkMonitor.shared
        
        print("🔄 TaskSyncEngine initialized")
        setupNetworkMonitoring()
        setupPeriodicSync()
    }
    
    // MARK: - Network Monitoring
    private func setupNetworkMonitoring() {
        networkMonitor.$isConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                if isConnected {
                    _Concurrency.Task {
                        await self?.performFullSync()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Periodic Sync
    private func setupPeriodicSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: syncInterval, repeats: true) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                await self?.performIncrementalSync()
            }
        }
    }
    
    // MARK: - Main Sync Operations
    
    /// تنفيذ مزامنة كاملة
    func performFullSync() async {
        guard networkMonitor.isConnected && !isCurrentlySyncing else {
            print("⚠️ Sync skipped - no connection or already syncing")
            return
        }
        
        print("🔄 Starting full sync...")
        isCurrentlySyncing = true
        syncStatus = .syncing
        syncProgress = 0.0
        
        do {
            // Step 1: Push local changes (25%)
            await pushLocalChanges()
            syncProgress = 0.25
            
            // Step 2: Pull remote changes (50%)
            await pullRemoteChanges()
            syncProgress = 0.50
            
            // Step 3: Resolve conflicts (75%)
            await resolveConflicts()
            syncProgress = 0.75
            
            // Step 4: Cleanup and finalize (100%)
            await finalizeSync()
            syncProgress = 1.0
            
            syncStatus = .success
            lastSyncTime = Date()
            syncStatistics.successfulSyncs += 1
            
            print("✅ Full sync completed successfully")
            
        } catch {
            syncStatus = .failed(error)
            syncStatistics.failedSyncs += 1
            print("❌ Full sync failed: \(error)")
        }
        
        isCurrentlySyncing = false
    }
    
    /// تنفيذ مزامنة تدريجية
    func performIncrementalSync() async {
        guard networkMonitor.isConnected && !isCurrentlySyncing else { return }
        
        print("🔄 Starting incremental sync...")
        isCurrentlySyncing = true
        syncStatus = .syncing
        
        do {
            // Only sync tasks that need syncing
            let pendingTasks = try await taskRepository.getTasksNeedingSync()
            
            if !pendingTasks.isEmpty {
                print("📤 Syncing \(pendingTasks.count) pending tasks")
                
                for task in pendingTasks {
                    try await syncSingleTask(task)
                }
                
                syncStatus = .success
                lastSyncTime = Date()
                syncStatistics.incrementalSyncs += 1
                
                print("✅ Incremental sync completed")
            } else {
                print("📱 No tasks need syncing")
            }
            
        } catch {
            syncStatus = .failed(error)
            syncStatistics.failedSyncs += 1
            print("❌ Incremental sync failed: \(error)")
        }
        
        isCurrentlySyncing = false
    }
    
    // MARK: - Push Operations
    
    /// رفع التغييرات المحلية للخادم
    private func pushLocalChanges() async {
        do {
            let pendingTasks = try await taskRepository.getTasksNeedingSync()
            print("📤 Pushing \(pendingTasks.count) local changes")
            
            for (index, task) in pendingTasks.enumerated() {
                try await syncSingleTask(task)
                
                // Update progress
                let progress = Double(index + 1) / Double(pendingTasks.count) * 0.25
                syncProgress = progress
            }
            
            syncStatistics.tasksPushed += pendingTasks.count
            
        } catch {
            print("❌ Failed to push local changes: \(error)")
            syncStatistics.failedSyncs += 1
        }
    }
    
    /// مزامنة مهمة واحدة
    private func syncSingleTask(_ task: Task) async throws {
        do {
            // Check if task exists on server
            if let remoteTask = try await fetchRemoteTask(task.id) {
                // Task exists - check for conflicts
                if hasConflict(local: task, remote: remoteTask) {
                    await handleConflict(local: task, remote: remoteTask)
                } else {
                    // No conflict - update remote
                    try await supabaseManager.updateTask(task)
                    try await taskRepository.markTaskSynced(task.id)
                    print("✅ Task updated on server: \(task.title)")
                }
            } else {
                // Task doesn't exist - create new
                try await supabaseManager.createTask(task)
                try await taskRepository.markTaskSynced(task.id)
                print("✅ Task created on server: \(task.title)")
            }
            
        } catch {
            print("❌ Failed to sync task \(task.title): \(error)")
            throw error
        }
    }
    
    // MARK: - Pull Operations
    
    /// سحب التغييرات من الخادم
    private func pullRemoteChanges() async {
        do {
            let remoteTasks = try await supabaseManager.fetchTasks()
            print("📥 Pulling \(remoteTasks.count) remote tasks")
            
            for (index, remoteTask) in remoteTasks.enumerated() {
                await processRemoteTask(remoteTask)
                
                // Update progress
                let progress = 0.25 + (Double(index + 1) / Double(remoteTasks.count) * 0.25)
                syncProgress = progress
            }
            
            syncStatistics.tasksPulled += remoteTasks.count
            
        } catch {
            print("❌ Failed to pull remote changes: \(error)")
            syncStatistics.failedSyncs += 1
        }
    }
    
    /// معالجة مهمة من الخادم
    private func processRemoteTask(_ remoteTask: Task) async {
        do {
            if let localTask = try await taskRepository.getTask(by: remoteTask.id) {
                // Task exists locally - check for conflicts
                if hasConflict(local: localTask, remote: remoteTask) {
                    await handleConflict(local: localTask, remote: remoteTask)
                } else if remoteTask.updatedAt > localTask.updatedAt {
                    // Remote is newer - update local
                    _ = try await taskRepository.updateTask(remoteTask)
                    print("✅ Local task updated from server: \(remoteTask.title)")
                }
            } else {
                // Task doesn't exist locally - create new
                _ = try await taskRepository.createTask(remoteTask)
                print("✅ New task created from server: \(remoteTask.title)")
            }
            
        } catch {
            print("❌ Failed to process remote task \(remoteTask.title): \(error)")
        }
    }
    
    // MARK: - Conflict Resolution
    
    /// التحقق من وجود تضارب
    private func hasConflict(local: Task, remote: Task) -> Bool {
        // Simple conflict detection based on update times
        let timeDifference = abs(local.updatedAt.timeIntervalSince(remote.updatedAt))
        
        // If both were updated within 5 seconds of each other, consider it a conflict
        return timeDifference < 5.0 && local.updatedAt != remote.updatedAt
    }
    
    /// معالجة التضارب
    private func handleConflict(local: Task, remote: Task) async {
        let conflictType: SyncConflict.ConflictType
        
        // Determine conflict type
        if local.status == .completed && remote.status != .completed {
            conflictType = .bothModified
        } else if local.status != .completed && remote.status == .completed {
            conflictType = .bothModified
        } else {
            conflictType = .bothModified
        }
        
        let conflict = SyncConflict(
            taskId: local.id,
            localTask: local,
            remoteTask: remote,
            conflictType: conflictType,
            detectedAt: Date()
        )
        
        pendingConflicts.append(conflict)
        syncStatus = .conflict
        
        print("⚠️ Conflict detected for task: \(local.title)")
    }
    
    /// حل التضارب تلقائياً
    func resolveConflict(_ conflict: SyncConflict, resolution: ConflictResolution) async {
        do {
            let resolvedTask: Task
            
            switch resolution {
            case .useLocal:
                resolvedTask = conflict.localTask
                try await supabaseManager.updateTask(resolvedTask)
                
            case .useRemote:
                resolvedTask = conflict.remoteTask
                _ = try await taskRepository.updateTask(resolvedTask)
                
            case .merge:
                resolvedTask = mergeConflictedTasks(local: conflict.localTask, remote: conflict.remoteTask)
                try await supabaseManager.updateTask(resolvedTask)
                _ = try await taskRepository.updateTask(resolvedTask)
            }
            
            try await taskRepository.markTaskSynced(resolvedTask.id)
            
            // Remove from pending conflicts
            pendingConflicts.removeAll { $0.taskId == conflict.taskId }
            
            if pendingConflicts.isEmpty {
                syncStatus = .success
            }
            
            print("✅ Conflict resolved for task: \(resolvedTask.title)")
            
        } catch {
            print("❌ Failed to resolve conflict: \(error)")
        }
    }
    
    /// دمج المهام المتضاربة
    private func mergeConflictedTasks(local: Task, remote: Task) -> Task {
        var mergedTask = local
        
        // Use the most recent completion status
        if remote.updatedAt > local.updatedAt {
            mergedTask.status = remote.status
            mergedTask.completedAt = remote.completedAt
        }
        
        // Merge tags
        let allTags = Set(local.tags + remote.tags)
        mergedTask.tags = Array(allTags)
        
        // Use the latest update time
        mergedTask.updatedAt = max(local.updatedAt, remote.updatedAt)
        
        return mergedTask
    }
    
    // MARK: - Helper Methods
    
    /// جلب مهمة من الخادم
    private func fetchRemoteTask(_ taskId: UUID) async throws -> Task? {
        // This would be implemented in SupabaseManager
        // For now, return nil to indicate task doesn't exist
        return nil
    }
    
    /// حل التضارب
    private func resolveConflicts() async {
        // Auto-resolve simple conflicts
        for conflict in pendingConflicts {
            if conflict.conflictType == .bothModified {
                // Auto-merge if possible
                await resolveConflict(conflict, resolution: .merge)
            }
        }
    }
    
    /// إنهاء المزامنة
    private func finalizeSync() async {
        // Cleanup and optimization
        syncProgress = 1.0
        print("🎯 Sync finalized")
    }
    
    deinit {
        syncTimer?.invalidate()
    }
}

// MARK: - Conflict Resolution Options
enum ConflictResolution {
    case useLocal
    case useRemote
    case merge
}

// MARK: - Sync Statistics
struct SyncStatistics {
    var successfulSyncs: Int = 0
    var failedSyncs: Int = 0
    var incrementalSyncs: Int = 0
    var tasksPushed: Int = 0
    var tasksPulled: Int = 0
    var conflictsResolved: Int = 0
    
    var totalSyncs: Int {
        return successfulSyncs + failedSyncs
    }
    
    var successRate: Double {
        guard totalSyncs > 0 else { return 0.0 }
        return Double(successfulSyncs) / Double(totalSyncs)
    }
}
