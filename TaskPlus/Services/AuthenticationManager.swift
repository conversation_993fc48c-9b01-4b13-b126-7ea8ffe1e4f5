//
//  AuthenticationManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import SwiftUI
import Security

// MARK: - Authentication Manager
@MainActor
class AuthenticationManager: ObservableObject {
    static let shared = AuthenticationManager()
    
    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let keychainService = "com.taskmate.auth"
    private let userDefaultsKey = "TaskMateCurrentUser"
    private let supabaseManager = SupabaseManager.shared

    private init() {
        checkAuthenticationStatus()
    }

    // MARK: - Authentication Status
    private func checkAuthenticationStatus() {
        // First, try to restore Supabase session
        _Concurrency.Task {
            await supabaseManager.restoreSession()

            // Then check if user session exists locally
            await MainActor.run {
                if let userData = UserDefaults.standard.data(forKey: userDefaultsKey),
                   let user = try? JSONDecoder().decode(User.self, from: userData) {
                    currentUser = user
                    isAuthenticated = true

                    // Update DataManager with current user
                    DataManager.shared.setCurrentUser(user)

                    // If we have a local user but no Supabase session, try to restore it
                    if supabaseManager.currentUser == nil {
                        _Concurrency.Task {
                            await attemptSessionRestore(for: user)
                        }
                    }
                } else if supabaseManager.currentUser != nil {
                    // If Supabase has a user but we don't have local data, use Supabase user
                    currentUser = supabaseManager.currentUser
                    isAuthenticated = true

                    // Save to local storage
                    if let user = supabaseManager.currentUser {
                        try? saveUserSession(user: user)
                        DataManager.shared.setCurrentUser(user)
                    }
                }
            }
        }
    }

    // MARK: - Session Restore Helper
    private func attemptSessionRestore(for user: User) async {
        // Try to restore Supabase session using saved credentials
        if let password = getPasswordFromKeychain(email: user.email) {
            print("🔄 Attempting to restore Supabase session for \(user.email)")
            do {
                let _ = try await supabaseManager.signIn(email: user.email, password: password)
                print("✅ Supabase session restored successfully")
            } catch {
                print("⚠️ Failed to restore Supabase session: \(error)")
            }
        }
    }
    
    // MARK: - Registration
    func register(email: String, password: String, firstName: String, lastName: String) async -> Bool {
        isLoading = true
        errorMessage = nil

        do {
            // Validate input
            try validateRegistrationInput(email: email, password: password, firstName: firstName, lastName: lastName)

            // Prepare user data for Supabase
            let userData: [String: Any] = [
                "username": generateUsername(from: firstName, lastName: lastName),
                "display_name": "\(firstName) \(lastName)",
                "first_name": firstName,
                "last_name": lastName
            ]

            // Register with Supabase
            let newUser = try await supabaseManager.signUp(
                email: email,
                password: password,
                userData: userData
            )

            // Save password to keychain for convenience
            try savePasswordToKeychain(email: email, password: password)

            // Save user session
            try saveUserSession(user: newUser)

            // Update state
            currentUser = newUser
            isAuthenticated = true

            // Update DataManager with current user
            DataManager.shared.setCurrentUser(newUser)

            isLoading = false
            return true

        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            return false
        }
    }
    
    // MARK: - Login
    func login(email: String, password: String) async -> Bool {
        isLoading = true
        errorMessage = nil

        do {
            // Validate input
            try validateLoginInput(email: email, password: password)

            // Sign in with Supabase
            let user = try await supabaseManager.signIn(
                email: email,
                password: password
            )

            // Save password to keychain for convenience
            try savePasswordToKeychain(email: email, password: password)

            // Save user session
            try saveUserSession(user: user)

            // Update state
            currentUser = user
            isAuthenticated = true

            // Update DataManager with current user
            DataManager.shared.setCurrentUser(user)

            isLoading = false
            return true

        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            return false
        }
    }
    
    // MARK: - Logout
    func logout() async {
        do {
            // Sign out from Supabase
            try await supabaseManager.signOut()
        } catch {
            print("Failed to sign out from Supabase: \(error)")
        }

        // Clear user session
        UserDefaults.standard.removeObject(forKey: userDefaultsKey)

        // Clear keychain (optional - keep for convenience)
        // clearPasswordFromKeychain()

        // Update state
        currentUser = nil
        isAuthenticated = false
        errorMessage = nil

        // Clear DataManager user and tasks
        DataManager.shared.setCurrentUser(nil)
    }
    
    // MARK: - Password Reset
    func resetPassword(email: String) async -> Bool {
        isLoading = true
        errorMessage = nil
        
        do {
            // Validate email
            try validateEmail(email)
            
            // Simulate network delay
            try await _Concurrency.Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            // Check if email exists
            guard await emailExists(email) else {
                throw AuthenticationError.emailNotFound
            }
            
            // Simulate sending reset email
            // In real implementation, this would send an actual email
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            return false
        }
    }
    
    // MARK: - Profile Update
    func updateProfile(user: User) async -> Bool {
        isLoading = true
        errorMessage = nil

        do {
            // Update profile in Supabase
            try await supabaseManager.updateProfile(user)

            // Save updated user session
            try saveUserSession(user: user)

            // Update state
            currentUser = user

            // Update DataManager
            DataManager.shared.updateUser(user)

            isLoading = false
            return true

        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            return false
        }
    }
    
    // MARK: - Helper Methods
    private func validateRegistrationInput(email: String, password: String, firstName: String, lastName: String) throws {
        try validateEmail(email)
        try validatePassword(password)
        
        if firstName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw AuthenticationError.invalidFirstName
        }
        
        if lastName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw AuthenticationError.invalidLastName
        }
    }
    
    private func validateLoginInput(email: String, password: String) throws {
        try validateEmail(email)
        
        if password.isEmpty {
            throw AuthenticationError.emptyPassword
        }
    }
    
    private func validateEmail(_ email: String) throws {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        
        if !emailPredicate.evaluate(with: email) {
            throw AuthenticationError.invalidEmail
        }
    }
    
    private func validatePassword(_ password: String) throws {
        if password.count < 6 {
            throw AuthenticationError.passwordTooShort
        }
        
        if password.count > 128 {
            throw AuthenticationError.passwordTooLong
        }
    }
    
    private func generateUsername(from firstName: String, lastName: String) -> String {
        let cleanFirst = firstName.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let cleanLast = lastName.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let randomNumber = Int.random(in: 100...999)
        return "\(cleanFirst)\(cleanLast)\(randomNumber)"
    }
    
    // MARK: - Simulation Methods (Replace with real API calls)
    private func emailExists(_ email: String) async -> Bool {
        // Simulate checking if email exists
        // In real implementation, this would be an API call
        return email == "<EMAIL>" // Simulate existing email
    }
    
    private func verifyCredentials(email: String, password: String) async -> Bool {
        // Simulate credential verification
        // In real implementation, this would be an API call
        guard let storedPassword = getPasswordFromKeychain(email: email) else {
            return false
        }
        return storedPassword == password
    }
    
    private func loadUserData(email: String) async -> User {
        // Simulate loading user data
        // In real implementation, this would be an API call
        return User(
            username: "testuser123",
            displayName: "Test User",
            email: email
        )
    }
    
    // MARK: - Keychain Operations
    private func savePasswordToKeychain(email: String, password: String) throws {
        let passwordData = password.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: email,
            kSecValueData as String: passwordData
        ]
        
        // Delete existing item first
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status != errSecSuccess {
            throw AuthenticationError.keychainError
        }
    }
    
    private func getPasswordFromKeychain(email: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: email,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let passwordData = result as? Data,
           let password = String(data: passwordData, encoding: .utf8) {
            return password
        }
        
        return nil
    }
    
    private func saveUserSession(user: User) throws {
        let userData = try JSONEncoder().encode(user)
        UserDefaults.standard.set(userData, forKey: userDefaultsKey)
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Authentication Errors
enum AuthenticationError: LocalizedError {
    case invalidEmail
    case emailAlreadyExists
    case emailNotFound
    case passwordTooShort
    case passwordTooLong
    case emptyPassword
    case invalidFirstName
    case invalidLastName
    case invalidCredentials
    case keychainError
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .invalidEmail:
            return "Please enter a valid email address"
        case .emailAlreadyExists:
            return "An account with this email already exists"
        case .emailNotFound:
            return "No account found with this email address"
        case .passwordTooShort:
            return "Password must be at least 6 characters long"
        case .passwordTooLong:
            return "Password must be less than 128 characters"
        case .emptyPassword:
            return "Please enter your password"
        case .invalidFirstName:
            return "Please enter your first name"
        case .invalidLastName:
            return "Please enter your last name"
        case .invalidCredentials:
            return "Invalid email or password"
        case .keychainError:
            return "Failed to save credentials securely"
        case .networkError:
            return "Network connection error. Please try again."
        case .unknownError:
            return "An unexpected error occurred"
        }
    }
}
