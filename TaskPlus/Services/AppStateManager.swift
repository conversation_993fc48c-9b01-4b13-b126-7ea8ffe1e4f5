//
//  AppStateManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Centralized App State Management for Offline/Online Modes
//

import Foundation
import SwiftUI
import Combine

// MARK: - App State
enum AppState {
    case launching
    case onlineReady
    case offlineReady
    case syncing
    case error(String)
    case maintenance
    
    var description: String {
        switch self {
        case .launching: return "Starting up..."
        case .onlineReady: return "Ready (Online)"
        case .offlineReady: return "Ready (Offline)"
        case .syncing: return "Syncing data..."
        case .error(let message): return "Error: \(message)"
        case .maintenance: return "Under maintenance"
        }
    }
    
    var isReady: Bool {
        switch self {
        case .onlineReady, .offlineReady:
            return true
        default:
            return false
        }
    }
    
    var allowsTaskCreation: Bool {
        switch self {
        case .onlineReady, .offlineReady:
            return true
        default:
            return false
        }
    }
    
    var allowsSync: Bool {
        switch self {
        case .onlineReady:
            return true
        default:
            return false
        }
    }
}

// MARK: - Feature Availability
struct FeatureAvailability {
    let tasks: Bool
    let groups: Bool
    let friends: Bool
    let profile: Bool
    let sync: Bool
    let notifications: Bool
    
    static let online = FeatureAvailability(
        tasks: true,
        groups: true,
        friends: true,
        profile: true,
        sync: true,
        notifications: true
    )
    
    static let offline = FeatureAvailability(
        tasks: true,
        groups: false,
        friends: false,
        profile: false,
        sync: false,
        notifications: true
    )
    
    static let limited = FeatureAvailability(
        tasks: false,
        groups: false,
        friends: false,
        profile: false,
        sync: false,
        notifications: false
    )
}

// MARK: - App State Manager
@MainActor
class AppStateManager: ObservableObject {
    static let shared = AppStateManager()
    
    // MARK: - Published Properties
    @Published var appState: AppState = .launching
    @Published var featureAvailability: FeatureAvailability = .limited
    @Published var isInitialized = false
    @Published var initializationProgress: Double = 0.0
    @Published var statusMessage = ""
    
    // MARK: - Dependencies
    private let networkMonitor = NetworkMonitor.shared
    private let sessionManager = OfflineSessionManager.shared
    private let taskSyncEngine = TaskSyncEngine.shared
    private let offlineTaskManager = OfflineTaskManager.shared
    private let cacheManager = CacheManager.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        print("🎯 AppStateManager initialized")
        setupObservers()
        
        _Concurrency.Task {
            await initializeApp()
        }
    }
    
    // MARK: - Setup Observers
    private func setupObservers() {
        // Monitor network changes
        networkMonitor.$isConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.handleNetworkChange(isConnected)
            }
            .store(in: &cancellables)
        
        // Monitor session changes
        sessionManager.$sessionState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] sessionState in
                self?.handleSessionChange(sessionState)
            }
            .store(in: &cancellables)
        
        // Monitor sync status
        taskSyncEngine.$syncStatus
            .receive(on: DispatchQueue.main)
            .sink { [weak self] syncStatus in
                self?.handleSyncStatusChange(syncStatus)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - App Initialization
    
    /// تهيئة التطبيق
    func initializeApp() async {
        print("🚀 Starting app initialization...")
        appState = .launching
        initializationProgress = 0.0
        
        do {
            // Step 1: Initialize Core Data (20%)
            statusMessage = "Initializing local storage..."
            await initializeCoreData()
            initializationProgress = 0.2
            
            // Step 2: Load cached data (40%)
            statusMessage = "Loading cached data..."
            await loadCachedData()
            initializationProgress = 0.4
            
            // Step 3: Check network and session (60%)
            statusMessage = "Checking connectivity..."
            await checkConnectivityAndSession()
            initializationProgress = 0.6
            
            // Step 4: Initialize services (80%)
            statusMessage = "Starting services..."
            await initializeServices()
            initializationProgress = 0.8
            
            // Step 5: Finalize (100%)
            statusMessage = "Finalizing..."
            await finalizeInitialization()
            initializationProgress = 1.0
            
            isInitialized = true
            statusMessage = "Ready"
            
            print("✅ App initialization completed successfully")
            
        } catch {
            appState = .error("Initialization failed: \(error.localizedDescription)")
            print("❌ App initialization failed: \(error)")
        }
    }
    
    /// تهيئة Core Data
    private func initializeCoreData() async {
        // Core Data is already initialized in CoreDataManager
        // Just verify it's working
        do {
            let taskRepository = TaskRepository()
            _ = try await taskRepository.getAllTasks()
            print("✅ Core Data initialized successfully")
        } catch {
            print("❌ Core Data initialization failed: \(error)")
            appState = .error("Core Data initialization failed")
        }
    }
    
    /// تحميل البيانات المخزنة
    private func loadCachedData() async {
        // Load cached user data, tasks, etc.
        await offlineTaskManager.refreshData()
        print("✅ Cached data loaded")
    }
    
    /// فحص الاتصال والجلسة
    private func checkConnectivityAndSession() async {
        if networkMonitor.isConnected {
            // Online mode
            if sessionManager.sessionState.isAuthenticated {
                appState = .onlineReady
                featureAvailability = .online
            } else {
                // Need authentication
                appState = .onlineReady
                featureAvailability = .limited
            }
        } else {
            // Offline mode
            if sessionManager.sessionState.isAuthenticated {
                appState = .offlineReady
                featureAvailability = .offline
            } else {
                // Limited offline access
                appState = .offlineReady
                featureAvailability = .limited
            }
        }
        
        print("✅ Connectivity and session checked")
    }
    
    /// تهيئة الخدمات
    private func initializeServices() async {
        // Initialize notification manager, sync engine, etc.
        if networkMonitor.isConnected && sessionManager.sessionState.isAuthenticated {
            // Start sync engine
            await taskSyncEngine.performIncrementalSync()
        }
        
        print("✅ Services initialized")
    }
    
    /// إنهاء التهيئة
    private func finalizeInitialization() async {
        // Final setup and optimizations
        // Memory usage will be updated automatically by timer
        print("✅ Initialization finalized")
    }
    
    // MARK: - State Change Handlers
    
    /// معالجة تغيير الشبكة
    private func handleNetworkChange(_ isConnected: Bool) {
        if isConnected {
            // Switched to online
            if sessionManager.sessionState.isAuthenticated {
                appState = .onlineReady
                featureAvailability = .online
                
                // Start sync
                _Concurrency.Task {
                    await taskSyncEngine.performIncrementalSync()
                }
            }
        } else {
            // Switched to offline
            if sessionManager.sessionState.isAuthenticated {
                appState = .offlineReady
                featureAvailability = .offline
            } else {
                featureAvailability = .limited
            }
        }
        
        print("🌐 Network state changed - Connected: \(isConnected)")
    }
    
    /// معالجة تغيير الجلسة
    private func handleSessionChange(_ sessionState: SessionState) {
        switch sessionState {
        case .authenticated:
            if networkMonitor.isConnected {
                appState = .onlineReady
                featureAvailability = .online
            } else {
                appState = .offlineReady
                featureAvailability = .offline
            }
            
        case .offline:
            appState = .offlineReady
            featureAvailability = .offline
            
        case .unauthenticated, .expired:
            featureAvailability = .limited
            
        case .locked:
            featureAvailability = .limited
            
        case .authenticating:
            // Keep current state during authentication
            break
        }
        
        print("👤 Session state changed: \(sessionState.description)")
    }
    
    /// معالجة تغيير حالة المزامنة
    private func handleSyncStatusChange(_ syncStatus: SyncStatus) {
        switch syncStatus {
        case .syncing:
            if appState.isReady {
                appState = .syncing
            }
            
        case .success, .idle:
            // Return to ready state
            if networkMonitor.isConnected && sessionManager.sessionState.isAuthenticated {
                appState = .onlineReady
            } else if sessionManager.sessionState.isAuthenticated {
                appState = .offlineReady
            }
            
        case .failed(let error):
            print("⚠️ Sync failed: \(error)")
            // Don't change app state for sync failures
            
        case .conflict:
            print("⚠️ Sync conflicts detected")
            // Handle conflicts but keep app functional
        }
    }
    
    // MARK: - Public Methods
    
    /// إعادة تهيئة التطبيق
    func reinitialize() async {
        isInitialized = false
        await initializeApp()
    }
    
    /// فرض المزامنة
    func forceSync() async {
        guard appState.allowsSync else {
            print("⚠️ Sync not available in current state")
            return
        }
        
        await taskSyncEngine.performFullSync()
    }
    
    /// التحقق من توفر ميزة
    func isFeatureAvailable(_ feature: String) -> Bool {
        switch feature.lowercased() {
        case "tasks":
            return featureAvailability.tasks
        case "groups":
            return featureAvailability.groups
        case "friends":
            return featureAvailability.friends
        case "profile":
            return featureAvailability.profile
        case "sync":
            return featureAvailability.sync
        case "notifications":
            return featureAvailability.notifications
        default:
            return false
        }
    }
    
    /// الحصول على رسالة حالة مفصلة
    func getDetailedStatus() -> String {
        var status = appState.description
        
        if !networkMonitor.isConnected {
            status += " (No Internet)"
        }
        
        if !sessionManager.sessionState.isAuthenticated {
            status += " (Not Signed In)"
        }
        
        return status
    }
    
    /// التحقق من إمكانية إنشاء المهام
    var canCreateTasks: Bool {
        return appState.allowsTaskCreation && sessionManager.sessionState.isAuthenticated
    }
    
    /// التحقق من إمكانية الوصول للمجموعات
    var canAccessGroups: Bool {
        return featureAvailability.groups && networkMonitor.isConnected
    }
    
    /// التحقق من إمكانية الوصول للأصدقاء
    var canAccessFriends: Bool {
        return featureAvailability.friends && networkMonitor.isConnected
    }
}

// MARK: - App State Extensions
extension AppState {
    var color: Color {
        switch self {
        case .launching:
            return .blue
        case .onlineReady:
            return .green
        case .offlineReady:
            return .orange
        case .syncing:
            return .blue
        case .error:
            return .red
        case .maintenance:
            return .yellow
        }
    }
    
    var icon: String {
        switch self {
        case .launching:
            return "gear"
        case .onlineReady:
            return "checkmark.circle.fill"
        case .offlineReady:
            return "wifi.slash"
        case .syncing:
            return "arrow.triangle.2.circlepath"
        case .error:
            return "exclamationmark.triangle.fill"
        case .maintenance:
            return "wrench.fill"
        }
    }
}
