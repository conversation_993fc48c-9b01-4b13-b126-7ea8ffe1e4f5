//
//  GroupManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import SwiftUI

// MARK: - Group Manager
@MainActor
class GroupManager: ObservableObject {
    static let shared = GroupManager()
    
    @Published var groups: [Group] = []
    @Published var groupTasks: [GroupTask] = []
    @Published var groupMembers: [UUID: [GroupMember]] = [:] // [groupId: [members]]
    @Published var groupInvitations: [GroupInvitation] = []
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let notificationManager = NotificationManager.shared
    
    private init() {
        // تحميل بيانات تجريبية للتطوير
        loadSampleData()

        // تحميل نظام الدعوات المشتركة
        loadSharedInvitations()
    }
    
    // MARK: - Group Management
    
    /// إنشاء مجموعة جديدة
    func createGroup(name: String, description: String?, isPrivate: Bool = false) async -> Group? {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else {
            setError("User not authenticated")
            return nil
        }
        
        print("🔄 Creating group: \(name)")
        
        let group = Group(name: name, description: description, ownerId: authenticatedUserId, isPrivate: isPrivate)
        
        // إضافة المجموعة محلياً
        groups.append(group)

        // إنشاء عضوية المالك
        let ownerMember = GroupMember(userId: authenticatedUserId, groupId: group.id, role: .owner)
        groupMembers[group.id] = [ownerMember]

        // مزامنة مع قاعدة البيانات
        _Concurrency.Task {
            do {
                // التحقق من تسجيل الدخول قبل المزامنة
                if !SupabaseManager.shared.isAuthenticated {
                    print("🔄 No active session, attempting to restore before group sync...")
                    await SupabaseManager.shared.restoreSession()
                }

                if SupabaseManager.shared.isAuthenticated {
                    let syncedGroup = try await SupabaseManager.shared.createGroup(group)

                    await MainActor.run {
                        // تحديث المجموعة بالبيانات المزامنة (ID صحيح من قاعدة البيانات)
                        if let index = self.groups.firstIndex(where: { $0.id == group.id }) {
                            self.groups[index] = syncedGroup

                            // تحديث أعضاء المجموعة بالـ ID الصحيح
                            if let oldMembers = self.groupMembers[group.id] {
                                self.groupMembers.removeValue(forKey: group.id)
                                self.groupMembers[syncedGroup.id] = oldMembers.map { member in
                                    GroupMember(userId: member.userId, groupId: syncedGroup.id, role: member.role)
                                }
                            }
                        }
                    }

                    print("✅ Group synced to database: \(syncedGroup.name)")
                    print("🔍 Database Group ID: \(syncedGroup.id.uuidString)")
                } else {
                    print("❌ Cannot sync group - user not authenticated")
                }
            } catch {
                print("❌ Failed to sync group to database: \(error)")
            }
        }

        // إشعار النجاح
        notificationManager.scheduleGroupActivityNotification(
            groupName: group.name,
            activity: "تم إنشاء المجموعة بنجاح"
        )

        print("✅ Group created successfully: \(name)")
        return group
    }
    
    /// تحديث معلومات المجموعة
    func updateGroup(_ group: Group) async {
        guard let currentUser = DataManager.shared.currentUser,
              group.isOwner(currentUser.id) else {
            setError("Only group owner can update group settings")
            return
        }
        
        print("🔄 Updating group: \(group.name)")
        
        if let index = groups.firstIndex(where: { $0.id == group.id }) {
            groups[index] = group
            print("✅ Group updated successfully: \(group.name)")
        }
    }
    
    /// حذف المجموعة
    func deleteGroup(_ group: Group) async {
        guard let currentUser = DataManager.shared.currentUser,
              group.isOwner(currentUser.id) else {
            setError("Only group owner can delete the group")
            return
        }
        
        print("🔄 Deleting group: \(group.name)")
        
        // حذف المجموعة
        groups.removeAll { $0.id == group.id }
        
        // حذف المهام المرتبطة
        groupTasks.removeAll { $0.groupId == group.id }
        
        // حذف الأعضاء
        groupMembers.removeValue(forKey: group.id)
        
        // حذف الدعوات
        groupInvitations.removeAll { $0.groupId == group.id }
        
        print("✅ Group deleted successfully: \(group.name)")
    }
    
    /// نقل ملكية المجموعة
    func transferOwnership(groupId: UUID, to newOwnerId: UUID) async -> Bool {
        guard let currentUser = DataManager.shared.currentUser,
              let groupIndex = groups.firstIndex(where: { $0.id == groupId }),
              groups[groupIndex].isOwner(currentUser.id) else {
            setError("Only current owner can transfer ownership")
            return false
        }
        
        print("🔄 Transferring ownership of group: \(groups[groupIndex].name)")
        
        // تحديث المالك
        groups[groupIndex].ownerId = newOwnerId
        groups[groupIndex].updatedAt = Date()
        
        // تحديث أدوار الأعضاء
        if var members = groupMembers[groupId] {
            // تغيير المالك السابق إلى عضو عادي
            if let oldOwnerIndex = members.firstIndex(where: { $0.userId == currentUser.id }) {
                members[oldOwnerIndex].role = .member
            }
            
            // تغيير العضو الجديد إلى مالك
            if let newOwnerIndex = members.firstIndex(where: { $0.userId == newOwnerId }) {
                members[newOwnerIndex].role = .owner
            }
            
            groupMembers[groupId] = members
        }
        
        // إشعار الأعضاء
        notificationManager.scheduleGroupActivityNotification(
            groupName: groups[groupIndex].name,
            activity: "تم نقل ملكية المجموعة"
        )
        
        print("✅ Ownership transferred successfully")
        return true
    }
    
    // MARK: - Member Management
    
    /// دعوة عضو جديد
    func inviteMember(to groupId: UUID, userId: UUID, message: String? = nil) async -> Bool {
        // التحقق من المصادقة أولاً
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else {
            setError("User not authenticated")
            return false
        }

        // البحث عن المجموعة
        guard let group = groups.first(where: { $0.id == groupId }) else {
            setError("Group not found")
            return false
        }

        // طباعة معلومات debugging
        print("🔍 Debug - Invite Member:")
        print("🔍 Authenticated User ID: \(authenticatedUserId.uuidString)")
        print("🔍 Group Owner ID: \(group.ownerId.uuidString)")
        print("🔍 Group Name: \(group.name)")
        print("🔍 Is Owner Check: \(group.isOwner(authenticatedUserId))")

        // التحقق من الملكية
        guard group.isOwner(authenticatedUserId) else {
            setError("Only group owner can invite members")
            return false
        }
        
        // التحقق من أن المستخدم ليس عضواً بالفعل
        guard !group.isMember(userId) else {
            setError("User is already a member")
            return false
        }
        
        print("🔄 Inviting member to group: \(group.name)")
        
        let invitation = GroupInvitation(
            groupId: groupId,
            invitedUserId: userId,
            invitedByUserId: authenticatedUserId,
            message: message
        )
        
        groupInvitations.append(invitation)

        // إضافة الدعوة للبيانات المشتركة (محاكاة قاعدة البيانات)
        addInvitationToSharedData(invitation)

        // إشعار المدعو
        notificationManager.scheduleGroupActivityNotification(
            groupName: group.name,
            activity: "تم دعوتك للانضمام للمجموعة"
        )

        print("✅ Member invited successfully")
        return true
    }
    
    /// قبول دعوة المجموعة
    func acceptInvitation(_ invitationId: UUID) async -> Bool {
        guard let currentUser = DataManager.shared.currentUser,
              let invitationIndex = groupInvitations.firstIndex(where: { $0.id == invitationId }),
              groupInvitations[invitationIndex].invitedUserId == currentUser.id else {
            setError("Invalid invitation")
            return false
        }
        
        let invitation = groupInvitations[invitationIndex]
        
        print("🔄 Accepting group invitation")
        
        // تحديث حالة الدعوة
        groupInvitations[invitationIndex].status = .accepted
        groupInvitations[invitationIndex].respondedAt = Date()
        
        // إضافة العضو للمجموعة
        if let groupIndex = groups.firstIndex(where: { $0.id == invitation.groupId }) {
            groups[groupIndex].addMember(currentUser.id)
            
            // إنشاء عضوية جديدة
            let newMember = GroupMember(userId: currentUser.id, groupId: invitation.groupId)
            if groupMembers[invitation.groupId] != nil {
                groupMembers[invitation.groupId]?.append(newMember)
            } else {
                groupMembers[invitation.groupId] = [newMember]
            }
            
            // إشعار المجموعة
            notificationManager.scheduleGroupActivityNotification(
                groupName: groups[groupIndex].name,
                activity: "انضم عضو جديد للمجموعة"
            )
            
            print("✅ Invitation accepted successfully")
            return true
        }
        
        return false
    }
    
    /// رفض دعوة المجموعة
    func declineInvitation(_ invitationId: UUID) async -> Bool {
        guard let currentUser = DataManager.shared.currentUser,
              let invitationIndex = groupInvitations.firstIndex(where: { $0.id == invitationId }),
              groupInvitations[invitationIndex].invitedUserId == currentUser.id else {
            setError("Invalid invitation")
            return false
        }
        
        print("🔄 Declining group invitation")
        
        // تحديث حالة الدعوة
        groupInvitations[invitationIndex].status = .declined
        groupInvitations[invitationIndex].respondedAt = Date()
        
        print("✅ Invitation declined")
        return true
    }
    
    /// إزالة عضو من المجموعة
    func removeMember(from groupId: UUID, userId: UUID) async -> Bool {
        guard let currentUser = DataManager.shared.currentUser,
              let groupIndex = groups.firstIndex(where: { $0.id == groupId }),
              groups[groupIndex].isOwner(currentUser.id) else {
            setError("Only group owner can remove members")
            return false
        }
        
        // لا يمكن إزالة المالك
        guard userId != groups[groupIndex].ownerId else {
            setError("Cannot remove group owner")
            return false
        }
        
        print("🔄 Removing member from group: \(groups[groupIndex].name)")
        
        // إزالة العضو من المجموعة
        groups[groupIndex].removeMember(userId)
        
        // إزالة العضوية
        groupMembers[groupId]?.removeAll { $0.userId == userId }
        
        // إلغاء إكمال المهام للعضو المُزال
        for i in 0..<groupTasks.count {
            if groupTasks[i].groupId == groupId {
                groupTasks[i].markIncomplete(by: userId)
                groupTasks[i].totalMembers = groups[groupIndex].memberCount
            }
        }
        
        print("✅ Member removed successfully")
        return true
    }

    /// دعوة صديق إلى مجموعة (wrapper للأصدقاء)
    func inviteFriendToGroup(_ friendId: UUID, groupId: UUID) async -> Bool {
        print("🚀 Inviting friend to group:")
        print("🔍 Friend ID: \(friendId.uuidString)")
        print("🔍 Group ID: \(groupId.uuidString)")

        let result = await inviteMember(to: groupId, userId: friendId, message: "You've been invited to join this group!")

        print("🔍 Invitation result: \(result)")
        if !result {
            print("❌ Error message: \(errorMessage ?? "Unknown error")")
        }

        return result
    }

    // MARK: - Helper Methods
    
    /// الحصول على مجموعات المستخدم
    func getUserGroups() -> [Group] {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else { return [] }
        return groups.filter { $0.isMember(authenticatedUserId) || $0.isOwner(authenticatedUserId) }
    }

    /// الحصول على المجموعات المشتركة مع صديق
    func getMutualGroups(with friendId: UUID) -> [Group] {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else { return [] }

        return groups.filter { group in
            // التحقق من أن المستخدم الحالي والصديق كلاهما عضو في المجموعة
            let currentUserIsMember = group.isMember(authenticatedUserId) || group.isOwner(authenticatedUserId)
            let friendIsMember = group.isMember(friendId) || group.isOwner(friendId)

            return currentUserIsMember && friendIsMember
        }
    }
    
    /// الحصول على أعضاء المجموعة
    func getGroupMembers(_ groupId: UUID) -> [GroupMember] {
        return groupMembers[groupId] ?? []
    }
    
    /// الحصول على دعوات المستخدم المعلقة
    func getPendingInvitations() -> [GroupInvitation] {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else { return [] }
        return groupInvitations.filter {
            $0.invitedUserId == authenticatedUserId && $0.status == .pending
        }
    }
    
    /// التحقق من صلاحيات المستخدم في المجموعة
    func getUserRole(in groupId: UUID) -> GroupMember.MemberRole? {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else { return nil }
        return groupMembers[groupId]?.first { $0.userId == authenticatedUserId }?.role
    }
    
    /// تحميل بيانات تجريبية
    private func loadSampleData() {
        guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId else { return }
        
        // مجموعات تجريبية
        groups = Group.sampleGroups(for: authenticatedUserId)
        
        // أعضاء تجريبيون
        for group in groups {
            let sampleMembers = GroupMember.sampleMembers(for: group.id, userIds: [authenticatedUserId])
            groupMembers[group.id] = sampleMembers
        }

        // دعوات تجريبية (محاكاة دعوات من أصدقاء)
        loadSampleInvitations(for: authenticatedUserId)

        print("✅ Sample group data loaded")
    }

    /// تحميل دعوات تجريبية
    private func loadSampleInvitations(for userId: UUID) {
        // إنشاء مجموعة وهمية للدعوة
        let sampleGroup = Group(name: "Friends Study Group", description: "Join us for collaborative learning!", ownerId: UUID())

        // إنشاء دعوة تجريبية
        let sampleInvitation = GroupInvitation(
            groupId: sampleGroup.id,
            invitedUserId: userId,
            invitedByUserId: UUID(),
            message: "Hey! Would you like to join our study group? We meet twice a week to work on assignments together."
        )

        // إضافة المجموعة والدعوة للبيانات التجريبية
        groups.append(sampleGroup)
        groupInvitations.append(sampleInvitation)

        print("✅ Sample invitations loaded")
    }
    
    /// تعيين رسالة خطأ
    private func setError(_ message: String) {
        errorMessage = message
        print("❌ GroupManager Error: \(message)")
    }
    
    /// مسح رسالة الخطأ
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Shared Data Management (محاكاة قاعدة البيانات)

    /// إضافة دعوة للبيانات المشتركة
    private func addInvitationToSharedData(_ invitation: GroupInvitation) {
        // محاكاة إضافة الدعوة لقاعدة البيانات المشتركة
        // في التطبيق الحقيقي، هذا سيكون API call

        // إضافة الدعوة لجميع instances من GroupManager
        DispatchQueue.main.async {
            // محاكاة تحديث البيانات للمستخدم المدعو
            NotificationCenter.default.post(
                name: NSNotification.Name("NewGroupInvitation"),
                object: invitation
            )
        }

        print("📤 Invitation added to shared data: \(invitation.id)")
    }

    /// تحميل الدعوات من البيانات المشتركة
    private func loadSharedInvitations() {
        // الاستماع للدعوات الجديدة
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NewGroupInvitation"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let invitation = notification.object as? GroupInvitation,
               let self = self {

                // التحقق من المصادقة في main thread
                Task { @MainActor in
                    guard let authenticatedUserId = SupabaseManager.shared.authenticatedUserId,
                          invitation.invitedUserId == authenticatedUserId else { return }

                    // إضافة الدعوة للقائمة المحلية
                    if !self.groupInvitations.contains(where: { $0.id == invitation.id }) {
                        self.groupInvitations.append(invitation)
                        print("📥 Received new group invitation: \(invitation.id)")
                    }
                }
            }
        }
    }

    // MARK: - Data Loading

    /// جلب المجموعات من قاعدة البيانات
    func loadUserGroups() async {
        guard SupabaseManager.shared.authenticatedUserId != nil else {
            setError("User not authenticated")
            return
        }

        print("🔄 Loading user groups from database...")
        isLoading = true

        do {
            // جلب المجموعات من قاعدة البيانات
            let fetchedGroups = try await SupabaseManager.shared.fetchUserGroups()

            await MainActor.run {
                self.groups = fetchedGroups
                print("✅ Loaded \(fetchedGroups.count) groups from database")

                // جلب الأعضاء لكل مجموعة
                loadGroupMembers()

                // جلب المهام لكل مجموعة
                loadGroupTasks()

                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.setError("Failed to load groups: \(error.localizedDescription)")
                self.isLoading = false
                print("❌ Failed to load groups: \(error)")
            }
        }
    }

    /// جلب أعضاء المجموعات
    private func loadGroupMembers() {
        for group in groups {
            // إنشاء أعضاء تجريبيين للآن (سنطور هذا لاحقاً)
            let ownerMember = GroupMember(userId: group.ownerId, groupId: group.id, role: .owner)
            var members = [ownerMember]

            // إضافة باقي الأعضاء
            for memberId in group.memberIds where memberId != group.ownerId {
                let member = GroupMember(userId: memberId, groupId: group.id, role: .member)
                members.append(member)
            }

            groupMembers[group.id] = members
        }
        print("✅ Loaded members for \(groups.count) groups")
    }

    /// جلب مهام المجموعات
    private func loadGroupTasks() {
        for group in groups {
            _Concurrency.Task {
                do {
                    let tasks = try await SupabaseManager.shared.fetchGroupTasks(for: group.id)

                    await MainActor.run {
                        // إضافة المهام للقائمة العامة
                        self.groupTasks.append(contentsOf: tasks)
                    }

                    print("✅ Loaded \(tasks.count) tasks for group: \(group.name)")
                } catch {
                    print("❌ Failed to load tasks for group \(group.name): \(error)")
                }
            }
        }
    }

    /// إعادة تحميل البيانات
    func refreshData() async {
        await loadUserGroups()

        // إعادة تحميل مهام المجموعات للحصول على أحدث البيانات
        await MainActor.run {
            groupTasks.removeAll()
        }

        loadGroupTasks()
    }

    // MARK: - Group Task Management

    /// إنشاء مهمة جماعية
    func createGroupTask(
        title: String,
        description: String? = nil,
        dueDate: Date? = nil,
        priority: GroupTask.Priority = .medium,
        groupId: UUID,
        taskType: GroupTask.GroupTaskType
    ) async -> GroupTask? {
        // استخدام نفس النهج المستخدم في المهام العادية
        guard let authUserId = SupabaseManager.shared.authenticatedUserId,
              let group = groups.first(where: { $0.id == groupId }) else {
            setError("User is not authenticated or group not found")
            return nil
        }

        print("🔄 Creating group task: \(title)")
        print("🔍 Using Supabase Auth UUID: \(authUserId.uuidString)")

        let groupTask = GroupTask(
            title: title,
            description: description,
            dueDate: dueDate,
            priority: priority,
            groupId: groupId,
            groupCode: group.groupCode,  // استخدام رمز المجموعة
            createdById: authUserId,     // استخدام UUID من Supabase Auth مباشرة
            taskType: taskType,
            totalMembers: group.memberCount
        )

        // إضافة المهمة محلياً
        groupTasks.append(groupTask)

        // تحديث إحصائيات المجموعة
        if let groupIndex = groups.firstIndex(where: { $0.id == groupId }) {
            groups[groupIndex].updateStats(taskCreated: true)
        }

        // مزامنة مع قاعدة البيانات (انتظار التحديث)
        do {
                // التحقق من المصادقة قبل المزامنة
                if !SupabaseManager.shared.isAuthenticated {
                    print("🔄 No active session, attempting to restore before task sync...")
                    await SupabaseManager.shared.restoreSession()
                }

                if SupabaseManager.shared.isAuthenticated {
                    // التأكد من وجود المجموعة في قاعدة البيانات أولاً
                    print("🔄 Ensuring group exists in database before creating task...")

                    var finalGroup = group
                    do {
                        // محاولة إنشاء المجموعة إذا لم تكن موجودة
                        let syncedGroup = try await SupabaseManager.shared.createGroup(group)
                        finalGroup = syncedGroup

                        // تحديث المجموعة محلياً بالـ ID الصحيح
                        await MainActor.run {
                            if let index = self.groups.firstIndex(where: { $0.id == group.id }) {
                                self.groups[index] = syncedGroup

                                // تحديث أعضاء المجموعة
                                if let oldMembers = self.groupMembers[group.id] {
                                    self.groupMembers.removeValue(forKey: group.id)
                                    self.groupMembers[syncedGroup.id] = oldMembers.map { member in
                                        GroupMember(userId: member.userId, groupId: syncedGroup.id, role: member.role)
                                    }
                                }
                            }
                        }

                        print("✅ Group ensured in database: \(syncedGroup.name)")
                        print("🔍 Updated Group ID: \(syncedGroup.id.uuidString)")
                    } catch {
                        // إذا فشل الإنشاء، قد تكون المجموعة موجودة بالفعل
                        print("ℹ️ Group might already exist in database: \(error)")

                        // محاولة جلب المجموعة الموجودة بـ group_code
                        do {
                            if let existingGroup = try await SupabaseManager.shared.findGroupByCode(group.groupCode) {
                                finalGroup = existingGroup

                                await MainActor.run {
                                    if let index = self.groups.firstIndex(where: { $0.id == group.id }) {
                                        self.groups[index] = existingGroup

                                        // تحديث أعضاء المجموعة
                                        if let oldMembers = self.groupMembers[group.id] {
                                            self.groupMembers.removeValue(forKey: group.id)
                                            self.groupMembers[existingGroup.id] = oldMembers.map { member in
                                                GroupMember(userId: member.userId, groupId: existingGroup.id, role: member.role)
                                            }
                                        }
                                    }
                                }

                                print("✅ Found existing group by code: \(existingGroup.name)")
                                print("🔍 Existing Group ID: \(existingGroup.id.uuidString)")
                                print("🔍 Group Code: \(existingGroup.groupCode)")
                            } else {
                                print("❌ Could not find existing group with code: \(group.groupCode)")
                            }
                        } catch {
                            print("❌ Failed to fetch existing group by code: \(error)")
                        }
                    }

                    // إنشاء المهمة الجماعية بالـ ID الصحيح
                    let updatedTask = GroupTask(
                        title: groupTask.title,
                        description: groupTask.description,
                        dueDate: groupTask.dueDate,
                        priority: groupTask.priority,
                        groupId: finalGroup.id,  // استخدام الـ ID الصحيح
                        groupCode: finalGroup.groupCode,
                        createdById: groupTask.createdById,
                        taskType: groupTask.taskType,
                        totalMembers: finalGroup.memberCount
                    )

                    let _ = try await SupabaseManager.shared.createGroupTask(updatedTask)
                    print("✅ Group task synced to database: \(title)")
            } else {
                print("❌ Cannot sync group task - user not authenticated")
            }
        } catch {
            print("❌ Failed to sync group task to database: \(error)")
        }

        // جدولة الإشعارات
        scheduleTaskNotifications(for: groupTask)

        // إشعار الأعضاء
        let taskTypeText = taskType.isGroupTask ? "مهمة جماعية" : "مهمة فردية"
        notificationManager.scheduleGroupActivityNotification(
            groupName: group.name,
            activity: "تم إنشاء \(taskTypeText) جديدة: \(title)"
        )

        print("✅ Group task created successfully: \(title)")
        return groupTask
    }

    /// تحديث مهمة جماعية
    func updateGroupTask(_ task: GroupTask) async {
        guard let currentUser = DataManager.shared.currentUser,
              let group = groups.first(where: { $0.id == task.groupId }),
              group.isMember(currentUser.id) else {
            setError("User is not a member of this group")
            return
        }

        print("🔄 Updating group task: \(task.title)")

        if let index = groupTasks.firstIndex(where: { $0.id == task.id }) {
            groupTasks[index] = task

            // مزامنة مع قاعدة البيانات
            _Concurrency.Task {
                do {
                    try await SupabaseManager.shared.updateGroupTask(task)
                    print("✅ Group task update synced to database: \(task.title)")
                } catch {
                    print("❌ Failed to sync group task update to database: \(error)")
                }
            }

            // إشعار التحديث
            notificationManager.scheduleGroupActivityNotification(
                groupName: group.name,
                activity: "تم تحديث المهمة: \(task.title)"
            )

            print("✅ Group task updated successfully: \(task.title)")
        }
    }

    /// حذف مهمة جماعية
    func deleteGroupTask(_ task: GroupTask) async {
        guard let currentUser = DataManager.shared.currentUser,
              let group = groups.first(where: { $0.id == task.groupId }),
              (group.isOwner(currentUser.id) || task.createdById == currentUser.id) else {
            setError("Only group owner or task creator can delete the task")
            return
        }

        print("🔄 Deleting group task: \(task.title)")

        // حذف المهمة محلياً
        groupTasks.removeAll { $0.id == task.id }

        // مزامنة الحذف مع قاعدة البيانات
        _Concurrency.Task {
            do {
                try await SupabaseManager.shared.deleteGroupTask(task.id)
                print("✅ Group task deletion synced to database: \(task.title)")
            } catch {
                print("❌ Failed to sync group task deletion to database: \(error)")
            }
        }

        // إلغاء الإشعارات
        cancelTaskNotifications(for: task)

        print("✅ Group task deleted successfully: \(task.title)")
    }

    /// إكمال مهمة لعضو محدد
    func completeTask(_ taskId: UUID, by memberId: UUID) async -> Bool {
        guard let taskIndex = groupTasks.firstIndex(where: { $0.id == taskId }),
              let group = groups.first(where: { $0.id == groupTasks[taskIndex].groupId }),
              group.isMember(memberId) else {
            setError("Invalid task or member")
            return false
        }

        let task = groupTasks[taskIndex]
        print("🔄 Completing task '\(task.title)' for member: \(memberId)")

        // محاولة إكمال المهمة
        let wasCompleted = groupTasks[taskIndex].markCompleted(by: memberId)

        if wasCompleted {
            let updatedTask = groupTasks[taskIndex]

            // مزامنة مع قاعدة البيانات (انتظار التحديث)
            do {
                try await SupabaseManager.shared.updateGroupTask(updatedTask)
                print("✅ Task completion synced to database: \(updatedTask.title)")

                // تحديث فوري للـ UI
                await MainActor.run {
                    objectWillChange.send()
                }
            } catch {
                print("❌ Failed to sync task completion to database: \(error)")
                // في حالة فشل المزامنة، نعيد المهمة لحالتها السابقة
                await MainActor.run {
                    groupTasks[taskIndex].markIncomplete(by: memberId)
                    objectWillChange.send()
                }
                setError("Failed to sync completion to database")
                return false
            }

            // إشعار الإكمال
            if updatedTask.isFullyCompleted {
                // المهمة مكتملة بالكامل
                notificationManager.scheduleGroupActivityNotification(
                    groupName: group.name,
                    activity: "🎉 تم إكمال المهمة بالكامل: \(task.title)"
                )

                // تحديث إحصائيات المجموعة
                if let groupIndex = groups.firstIndex(where: { $0.id == task.groupId }) {
                    groups[groupIndex].updateStats(taskCompleted: true)
                }
            } else {
                // إكمال جزئي
                let progress = updatedTask.groupProgress
                notificationManager.scheduleGroupActivityNotification(
                    groupName: group.name,
                    activity: "✅ تقدم في المهمة '\(task.title)': \(progress)"
                )
            }

            print("✅ Task completed successfully")
            return true
        }

        return false
    }

    /// إلغاء إكمال مهمة لعضو محدد
    func uncompleteTask(_ taskId: UUID, by memberId: UUID) async -> Bool {
        guard let taskIndex = groupTasks.firstIndex(where: { $0.id == taskId }),
              let group = groups.first(where: { $0.id == groupTasks[taskIndex].groupId }),
              group.isMember(memberId) else {
            setError("Invalid task or member")
            return false
        }

        let task = groupTasks[taskIndex]
        print("🔄 Uncompleting task '\(task.title)' for member: \(memberId)")

        // محاولة إلغاء الإكمال
        let wasUncompleted = groupTasks[taskIndex].markIncomplete(by: memberId)

        if wasUncompleted {
            let updatedTask = groupTasks[taskIndex]

            // مزامنة مع قاعدة البيانات (انتظار التحديث)
            do {
                try await SupabaseManager.shared.updateGroupTask(updatedTask)
                print("✅ Task uncompletion synced to database: \(updatedTask.title)")

                // تحديث فوري للـ UI
                await MainActor.run {
                    objectWillChange.send()
                }
            } catch {
                print("❌ Failed to sync task uncompletion to database: \(error)")
                // في حالة فشل المزامنة، نعيد المهمة لحالتها السابقة
                await MainActor.run {
                    groupTasks[taskIndex].markCompleted(by: memberId)
                    objectWillChange.send()
                }
                setError("Failed to sync uncompletion to database")
                return false
            }

            let progress = updatedTask.groupProgress

            // إشعار إلغاء الإكمال
            notificationManager.scheduleGroupActivityNotification(
                groupName: group.name,
                activity: "↩️ تم إلغاء إكمال المهمة '\(task.title)': \(progress)"
            )

            print("✅ Task uncompleted successfully")
            return true
        }

        return false
    }

    /// الحصول على مهام المجموعة
    func getGroupTasks(_ groupId: UUID) -> [GroupTask] {
        return groupTasks.filter { $0.groupId == groupId }
    }

    /// الحصول على مهام المستخدم في المجموعة
    func getUserTasksInGroup(_ groupId: UUID, userId: UUID) -> [GroupTask] {
        return groupTasks.filter { task in
            task.groupId == groupId && (
                task.taskType.isGroupTask ||
                task.taskType.assigneeId == userId
            )
        }
    }

    // MARK: - Notification Helpers

    private func scheduleTaskNotifications(for task: GroupTask) {
        guard let dueDate = task.dueDate else { return }

        // جدولة إشعارات للمهمة الجماعية
        if notificationManager.notificationSettings.taskRemindersEnabled {
            notificationManager.scheduleGroupTaskReminder(for: task)
        }
    }

    private func cancelTaskNotifications(for task: GroupTask) {
        // إلغاء إشعارات المهمة
        notificationManager.cancelGroupTaskReminder(for: task)
    }
}
