//
//  PerformanceOptimizer.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Performance Optimization and Memory Management
//

import Foundation
import SwiftUI
import Combine

// MARK: - Performance Metrics
struct PerformanceMetrics {
    let appLaunchTime: TimeInterval
    let coreDataInitTime: TimeInterval
    let cacheHitRate: Double
    let memoryUsage: Int64
    let syncPerformance: TimeInterval
    let uiResponseTime: TimeInterval
    
    var description: String {
        return """
        Performance Metrics:
        - App Launch: \(String(format: "%.2f", appLaunchTime))s
        - Core Data Init: \(String(format: "%.2f", coreDataInitTime))s
        - Cache Hit Rate: \(String(format: "%.1f", cacheHitRate * 100))%
        - Memory Usage: \(memoryUsage / 1024 / 1024)MB
        - Sync Performance: \(String(format: "%.2f", syncPerformance))s
        - UI Response: \(String(format: "%.2f", uiResponseTime))s
        """
    }
    
    var isOptimal: Bool {
        return appLaunchTime < 2.0 &&
               coreDataInitTime < 0.5 &&
               cacheHitRate > 0.8 &&
               memoryUsage < 100 * 1024 * 1024 && // 100MB
               syncPerformance < 5.0 &&
               uiResponseTime < 0.1
    }
}

// MARK: - Performance Optimizer
@MainActor
class PerformanceOptimizer: ObservableObject {
    static let shared = PerformanceOptimizer()
    
    // MARK: - Published Properties
    @Published var currentMetrics: PerformanceMetrics?
    @Published var isOptimizing = false
    @Published var optimizationProgress: Double = 0.0
    @Published var recommendations: [OptimizationRecommendation] = []
    
    // MARK: - Private Properties
    private var startTime: Date?
    private var coreDataInitStart: Date?
    private var syncStartTime: Date?
    private var uiActionStart: Date?
    
    // MARK: - Dependencies
    private let cacheManager = CacheManager.shared
    private let coreDataManager = CoreDataManager.shared
    private let taskSyncEngine = TaskSyncEngine.shared
    
    // MARK: - Initialization
    private init() {
        print("⚡ PerformanceOptimizer initialized")
        startPerformanceMonitoring()
    }
    
    // MARK: - Performance Monitoring
    
    /// بدء مراقبة الأداء
    private func startPerformanceMonitoring() {
        // Monitor app launch time
        startTime = Date()
        
        // Setup periodic performance checks
        Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                await self?.collectMetrics()
            }
        }
    }
    
    /// جمع مقاييس الأداء
    func collectMetrics() async {
        let appLaunchTime = startTime?.timeIntervalSinceNow.magnitude ?? 0
        let coreDataInitTime = coreDataInitStart?.timeIntervalSinceNow.magnitude ?? 0
        let memoryUsage = getMemoryUsage()
        let cacheStats = cacheManager.getAllStatistics()
        
        // Calculate average cache hit rate
        let totalHits = cacheStats.values.reduce(0) { $0 + $1.hitCount }
        let totalRequests = cacheStats.values.reduce(0) { $0 + $1.hitCount + $1.missCount }
        let cacheHitRate = totalRequests > 0 ? Double(totalHits) / Double(totalRequests) : 0.0
        
        currentMetrics = PerformanceMetrics(
            appLaunchTime: appLaunchTime,
            coreDataInitTime: coreDataInitTime,
            cacheHitRate: cacheHitRate,
            memoryUsage: memoryUsage,
            syncPerformance: syncStartTime?.timeIntervalSinceNow.magnitude ?? 0,
            uiResponseTime: uiActionStart?.timeIntervalSinceNow.magnitude ?? 0
        )
        
        // Generate recommendations
        generateRecommendations()
        
        print("📊 Performance metrics collected")
    }
    
    /// الحصول على استخدام الذاكرة
    private func getMemoryUsage() -> Int64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Int64(info.resident_size)
        } else {
            return 0
        }
    }
    
    // MARK: - Performance Optimization
    
    /// تنفيذ تحسين شامل للأداء
    func performComprehensiveOptimization() async {
        print("🚀 Starting comprehensive performance optimization...")
        isOptimizing = true
        optimizationProgress = 0.0
        
        // Step 1: Core Data Optimization (25%)
        await optimizeCoreData()
        optimizationProgress = 0.25
        
        // Step 2: Cache Optimization (50%)
        await optimizeCaches()
        optimizationProgress = 0.50
        
        // Step 3: Memory Optimization (75%)
        await optimizeMemory()
        optimizationProgress = 0.75
        
        // Step 4: UI Optimization (100%)
        await optimizeUI()
        optimizationProgress = 1.0
        
        // Collect final metrics
        await collectMetrics()
        
        isOptimizing = false
        print("✅ Comprehensive optimization completed")
    }
    
    /// تحسين Core Data
    private func optimizeCoreData() async {
        print("🗄️ Optimizing Core Data performance...")
        
        // Optimize Core Data queries
        let context = coreDataManager.viewContext
        
        // Enable query generation token for better performance
        do {
            try context.setQueryGenerationFrom(.current)
        } catch {
            print("⚠️ Failed to set query generation: \(error)")
        }
        
        // Batch delete old completed tasks (older than 30 days)
        let thirtyDaysAgo = Date().addingTimeInterval(-30 * 24 * 60 * 60)
        let predicate = NSPredicate(format: "isCompleted == YES AND completedAt < %@", thirtyDaysAgo as NSDate)
        
        do {
            try await coreDataManager.batchDelete(TaskEntity.self, predicate: predicate)
            print("✅ Cleaned up old completed tasks")
        } catch {
            print("⚠️ Failed to cleanup old tasks: \(error)")
        }
        
        // Optimize persistent store
        print("✅ Core Data optimization completed")
    }
    
    /// تحسين الـ Caches
    private func optimizeCaches() async {
        print("🧠 Optimizing cache performance...")
        
        // Get all cache statistics
        let cacheStats = cacheManager.getAllStatistics()
        
        for (cacheName, stats) in cacheStats {
            print("📊 \(cacheName) Cache: \(stats.size) items, \(String(format: "%.1f", stats.hitRate * 100))% hit rate")
            
            // If hit rate is low, clear and rebuild cache
            if stats.hitRate < 0.5 && stats.size > 10 {
                switch cacheName {
                case "Users":
                    cacheManager.userCache.clear()
                    print("🧹 Cleared user cache due to low hit rate")
                    
                case "Groups":
                    cacheManager.groupCache.clear()
                    print("🧹 Cleared group cache due to low hit rate")
                    
                case "Images":
                    cacheManager.imageCache.clearCache()
                    print("🧹 Cleared image cache due to low hit rate")
                    
                default:
                    break
                }
            }
        }
        
        // Optimize cache sizes based on usage
        await optimizeCacheSizes()
    }
    
    /// تحسين أحجام الـ Cache
    private func optimizeCacheSizes() async {
        let memoryUsage = getMemoryUsage()
        let memoryMB = memoryUsage / 1024 / 1024
        
        if memoryMB > 80 { // If using more than 80MB
            // Reduce cache sizes
            print("⚠️ High memory usage (\(memoryMB)MB), reducing cache sizes")
            
            // This would require modifying cache max sizes
            // For now, just clear some caches
            cacheManager.imageCache.clearCache()
        }
    }
    
    /// تحسين الذاكرة
    private func optimizeMemory() async {
        print("💾 Optimizing memory usage...")
        
        // Force garbage collection
        autoreleasepool {
            // Clear any temporary objects
        }
        
        // Optimize image cache
        let imageStats = cacheManager.imageCache.getStatistics()
        if imageStats.size > 100 { // If more than 100 images cached
            // Clear half of the oldest images
            print("🖼️ Optimizing image cache - clearing old images")
            cacheManager.imageCache.clearCache()
        }
        
        // Check for memory leaks
        let currentMemory = getMemoryUsage()
        print("💾 Current memory usage: \(currentMemory / 1024 / 1024)MB")
        
        if currentMemory > 100 * 1024 * 1024 { // More than 100MB
            print("⚠️ High memory usage detected - performing cleanup")
            
            // Clear all caches
            cacheManager.clearAllCaches()
            
            // Force Core Data to release memory
            coreDataManager.viewContext.refreshAllObjects()
        }
    }
    
    /// تحسين واجهة المستخدم
    private func optimizeUI() async {
        print("🎨 Optimizing UI performance...")
        
        // This would involve UI-specific optimizations
        // For now, just log the completion
        print("✅ UI optimization completed")
    }
    
    // MARK: - Recommendations
    
    /// توليد توصيات التحسين
    private func generateRecommendations() {
        guard let metrics = currentMetrics else { return }
        
        recommendations.removeAll()
        
        if metrics.appLaunchTime > 2.0 {
            recommendations.append(OptimizationRecommendation(
                type: .performance,
                title: "Slow App Launch",
                description: "App launch time is \(String(format: "%.2f", metrics.appLaunchTime))s. Consider reducing initialization work.",
                priority: .high
            ))
        }
        
        if metrics.cacheHitRate < 0.8 {
            recommendations.append(OptimizationRecommendation(
                type: .cache,
                title: "Low Cache Hit Rate",
                description: "Cache hit rate is \(String(format: "%.1f", metrics.cacheHitRate * 100))%. Consider optimizing cache strategy.",
                priority: .medium
            ))
        }
        
        if metrics.memoryUsage > 100 * 1024 * 1024 {
            recommendations.append(OptimizationRecommendation(
                type: .memory,
                title: "High Memory Usage",
                description: "Memory usage is \(metrics.memoryUsage / 1024 / 1024)MB. Consider reducing cache sizes.",
                priority: .high
            ))
        }
        
        if metrics.syncPerformance > 5.0 {
            recommendations.append(OptimizationRecommendation(
                type: .sync,
                title: "Slow Sync Performance",
                description: "Sync takes \(String(format: "%.2f", metrics.syncPerformance))s. Consider optimizing sync strategy.",
                priority: .medium
            ))
        }
        
        if metrics.uiResponseTime > 0.1 {
            recommendations.append(OptimizationRecommendation(
                type: .ui,
                title: "Slow UI Response",
                description: "UI response time is \(String(format: "%.2f", metrics.uiResponseTime))s. Consider optimizing UI updates.",
                priority: .medium
            ))
        }
    }
    
    // MARK: - Timing Methods
    
    /// بدء قياس وقت Core Data
    func startCoreDataTiming() {
        coreDataInitStart = Date()
    }
    
    /// انتهاء قياس وقت Core Data
    func endCoreDataTiming() {
        coreDataInitStart = nil
    }
    
    /// بدء قياس وقت المزامنة
    func startSyncTiming() {
        syncStartTime = Date()
    }
    
    /// انتهاء قياس وقت المزامنة
    func endSyncTiming() {
        syncStartTime = nil
    }
    
    /// بدء قياس وقت استجابة UI
    func startUITiming() {
        uiActionStart = Date()
    }
    
    /// انتهاء قياس وقت استجابة UI
    func endUITiming() {
        uiActionStart = nil
    }
}

// MARK: - Optimization Recommendation
struct OptimizationRecommendation: Identifiable {
    let id = UUID()
    let type: RecommendationType
    let title: String
    let description: String
    let priority: Priority
    
    enum RecommendationType {
        case performance
        case memory
        case cache
        case sync
        case ui
        
        var icon: String {
            switch self {
            case .performance: return "speedometer"
            case .memory: return "memorychip"
            case .cache: return "externaldrive"
            case .sync: return "arrow.triangle.2.circlepath"
            case .ui: return "paintbrush"
            }
        }
        
        var color: Color {
            switch self {
            case .performance: return .blue
            case .memory: return .orange
            case .cache: return .purple
            case .sync: return .green
            case .ui: return .pink
            }
        }
    }
    
    enum Priority {
        case low, medium, high
        
        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .orange
            case .high: return .red
            }
        }
        
        var description: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            }
        }
    }
}
