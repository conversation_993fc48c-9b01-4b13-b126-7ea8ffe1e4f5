//
//  OfflineTaskManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Offline-First Task Manager using Core Data Repository
//

import Foundation
import SwiftUI

// MARK: - Offline Task Manager
@MainActor
class OfflineTaskManager: ObservableObject {
    static let shared = OfflineTaskManager()
    
    // MARK: - Properties
    private let taskRepository: TaskRepository
    private let supabaseManager: SupabaseManager
    private let networkMonitor: NetworkMonitor
    
    // Published Properties
    @Published var tasks: [Task] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var isOfflineMode = false
    
    // Statistics
    @Published var taskStatistics: TaskStatistics?
    
    // MARK: - Initialization
    private init() {
        self.taskRepository = TaskRepository()
        self.supabaseManager = SupabaseManager.shared
        self.networkMonitor = NetworkMonitor.shared
        
        print("🚀 OfflineTaskManager initialized")
        
        // Setup network monitoring
        setupNetworkMonitoring()
        
        // Load initial data
        _Concurrency.Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Network Monitoring
    private func setupNetworkMonitoring() {
        // Monitor network changes
        networkMonitor.$isConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.isOfflineMode = !isConnected
                if isConnected {
                    _Concurrency.Task {
                        await self?.syncPendingTasks()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Data Loading
    private func loadInitialData() async {
        print("📱 Loading initial task data...")
        setLoading(true)
        defer { setLoading(false) }
        
        do {
            // Always load from Core Data first (offline-first approach)
            let localTasks = try await taskRepository.getAllTasks()
            tasks = localTasks
            print("✅ Loaded \(localTasks.count) tasks from Core Data")
            
            // Update statistics
            if let currentUserId = getCurrentUserId() {
                taskStatistics = try await taskRepository.getTaskStatistics(for: currentUserId)
            }
            
            // Sync with server if online
            if networkMonitor.isConnected {
                await syncWithServer()
            } else {
                print("📱 Offline mode - using local data only")
            }
            
        } catch {
            print("❌ Failed to load initial data: \(error)")
            setError("Failed to load tasks: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Task Operations (Offline-First)
    
    /// إضافة مهمة جديدة
    func addTask(_ task: Task) async {
        print("➕ Adding task (offline-first): \(task.title)")
        setLoading(true)
        defer { setLoading(false) }
        
        do {
            // 1. Save to Core Data immediately (offline-first)
            let savedTask = try await taskRepository.createTask(task)
            
            // 2. Update local UI
            tasks.append(savedTask)
            await updateStatistics()
            
            print("✅ Task saved locally: \(savedTask.title)")
            
            // 3. Sync with server if online
            if networkMonitor.isConnected {
                await syncTaskToServer(savedTask)
            } else {
                print("📱 Offline - task will sync when online")
            }
            
        } catch {
            print("❌ Failed to add task: \(error)")
            setError("Failed to add task: \(error.localizedDescription)")
        }
    }
    
    /// تحديث مهمة موجودة
    func updateTask(_ task: Task) async {
        print("🔄 Updating task (offline-first): \(task.title)")
        setLoading(true)
        defer { setLoading(false) }
        
        do {
            // 1. Update in Core Data immediately
            let updatedTask = try await taskRepository.updateTask(task)
            
            // 2. Update local UI
            if let index = tasks.firstIndex(where: { $0.id == task.id }) {
                tasks[index] = updatedTask
            }
            await updateStatistics()
            
            print("✅ Task updated locally: \(updatedTask.title)")
            
            // 3. Sync with server if online
            if networkMonitor.isConnected {
                await syncTaskToServer(updatedTask)
            } else {
                print("📱 Offline - task will sync when online")
            }
            
        } catch {
            print("❌ Failed to update task: \(error)")
            setError("Failed to update task: \(error.localizedDescription)")
        }
    }
    
    /// حذف مهمة
    func deleteTask(_ task: Task) async {
        print("🗑️ Deleting task (offline-first): \(task.title)")
        setLoading(true)
        defer { setLoading(false) }
        
        do {
            // 1. Mark as deleted in Core Data (soft delete)
            try await taskRepository.deleteTask(task.id)
            
            // 2. Remove from local UI
            tasks.removeAll { $0.id == task.id }
            await updateStatistics()
            
            print("✅ Task deleted locally: \(task.title)")
            
            // 3. Sync deletion with server if online
            if networkMonitor.isConnected {
                await syncTaskDeletionToServer(task.id)
            } else {
                print("📱 Offline - deletion will sync when online")
            }
            
        } catch {
            print("❌ Failed to delete task: \(error)")
            setError("Failed to delete task: \(error.localizedDescription)")
        }
    }
    
    /// تبديل حالة إكمال المهمة
    func toggleTaskCompletion(_ task: Task) async {
        var updatedTask = task
        
        if task.status == .completed {
            updatedTask.status = .inProgress
            updatedTask.completedAt = nil
        } else {
            updatedTask.status = .completed
            updatedTask.completedAt = Date()
        }
        
        await updateTask(updatedTask)
    }
    
    // MARK: - Search and Filter
    
    /// البحث في المهام
    func searchTasks(_ query: String) async -> [Task] {
        guard !query.isEmpty else { return tasks }
        
        do {
            let searchResults = try await taskRepository.searchTasks(query)
            print("🔍 Search completed: found \(searchResults.count) tasks")
            return searchResults
        } catch {
            print("❌ Search failed: \(error)")
            return tasks.filter { task in
                task.title.localizedCaseInsensitiveContains(query) ||
                (task.description?.localizedCaseInsensitiveContains(query) ?? false)
            }
        }
    }
    
    /// جلب المهام حسب الحالة
    func getTasksByStatus(_ status: Task.TaskStatus) async -> [Task] {
        return tasks.filter { $0.status == status }
    }
    
    /// جلب المهام المتأخرة
    func getOverdueTasks() async -> [Task] {
        do {
            return try await taskRepository.getOverdueTasks()
        } catch {
            print("❌ Failed to get overdue tasks: \(error)")
            return tasks.filter { task in
                guard let dueDate = task.dueDate else { return false }
                return dueDate < Date() && task.status != .completed
            }
        }
    }
    
    /// جلب مهام اليوم
    func getTodayTasks() async -> [Task] {
        do {
            return try await taskRepository.getTasksDueToday()
        } catch {
            print("❌ Failed to get today tasks: \(error)")
            return []
        }
    }
    
    // MARK: - Sync Operations
    
    /// مزامنة مع الخادم
    private func syncWithServer() async {
        print("🔄 Syncing with server...")
        
        do {
            // Sync pending tasks to server
            await syncPendingTasks()
            
            // Fetch updates from server
            await fetchServerUpdates()
            
            print("✅ Sync completed successfully")
            
        } catch {
            print("❌ Sync failed: \(error)")
        }
    }
    
    /// مزامنة المهام المعلقة
    private func syncPendingTasks() async {
        do {
            let pendingTasks = try await taskRepository.getTasksNeedingSync()
            print("📤 Syncing \(pendingTasks.count) pending tasks")
            
            for task in pendingTasks {
                await syncTaskToServer(task)
            }
            
        } catch {
            print("❌ Failed to sync pending tasks: \(error)")
        }
    }
    
    /// مزامنة مهمة واحدة مع الخادم
    private func syncTaskToServer(_ task: Task) async {
        do {
            // Try to sync with Supabase
            try await supabaseManager.updateTask(task)
            
            // Mark as synced in Core Data
            try await taskRepository.markTaskSynced(task.id)
            
            print("✅ Task synced to server: \(task.title)")
            
        } catch {
            print("❌ Failed to sync task to server: \(error)")
        }
    }
    
    /// مزامنة حذف مهمة مع الخادم
    private func syncTaskDeletionToServer(_ taskId: UUID) async {
        do {
            try await supabaseManager.deleteTask(taskId)
            print("✅ Task deletion synced to server")
        } catch {
            print("❌ Failed to sync task deletion: \(error)")
        }
    }
    
    /// جلب التحديثات من الخادم
    private func fetchServerUpdates() async {
        do {
            let serverTasks = try await supabaseManager.fetchTasks()
            
            // Update Core Data with server data
            for serverTask in serverTasks {
                let existingTask = try await taskRepository.getTask(by: serverTask.id)
                
                if existingTask == nil {
                    // New task from server
                    _ = try await taskRepository.createTask(serverTask)
                } else {
                    // Update existing task if server version is newer
                    _ = try await taskRepository.updateTask(serverTask)
                }
            }
            
            // Reload local tasks
            tasks = try await taskRepository.getAllTasks()
            await updateStatistics()
            
            print("✅ Server updates fetched and applied")
            
        } catch {
            print("❌ Failed to fetch server updates: \(error)")
        }
    }
    
    // MARK: - Statistics
    
    private func updateStatistics() async {
        guard let currentUserId = getCurrentUserId() else { return }
        
        do {
            taskStatistics = try await taskRepository.getTaskStatistics(for: currentUserId)
        } catch {
            print("❌ Failed to update statistics: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func getCurrentUserId() -> UUID? {
        // Get current user ID from AuthenticationManager or similar
        return UUID() // Placeholder - replace with actual implementation
    }
    
    private func setLoading(_ loading: Bool) {
        isLoading = loading
    }
    
    private func setError(_ message: String) {
        errorMessage = message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.errorMessage = nil
        }
    }
    
    // MARK: - Public Interface for Migration
    
    /// جلب جميع المهام
    func getAllTasks() async -> [Task] {
        return tasks
    }
    
    /// إعادة تحميل البيانات
    func refreshData() async {
        await loadInitialData()
    }
    
    /// تنظيف البيانات المحلية
    func clearLocalData() async {
        do {
            try await taskRepository.deleteAllTasks()
            tasks.removeAll()
            taskStatistics = nil
            print("🧹 Local task data cleared")
        } catch {
            print("❌ Failed to clear local data: \(error)")
        }
    }
}

// MARK: - Combine Import
import Combine
