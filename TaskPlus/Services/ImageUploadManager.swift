//
//  ImageUploadManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation
import UIKit
import Supabase

@MainActor
class ImageUploadManager: ObservableObject {
    static let shared = ImageUploadManager()

    private let supabaseManager = SupabaseManager.shared
    private let bucketName = "avatars"

    private var supabase: SupabaseClient {
        return supabaseManager.client
    }
    
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    @Published var compressionInfo: String = ""
    
    private init() {}
    
    // MARK: - Avatar Upload
    func uploadAvatar(_ image: UIImage, for userId: UUID) async throws -> String {
        isUploading = true
        uploadProgress = 0.0
        compressionInfo = "Preparing image..."

        defer {
            isUploading = false
            uploadProgress = 0.0
            compressionInfo = ""
        }

        // Check authentication
        guard let currentUser = supabase.auth.currentUser else {
            print("❌ No authenticated user found")
            throw ImageUploadError.uploadFailed
        }

        guard let session = supabase.auth.currentSession else {
            print("❌ No current session found")
            throw ImageUploadError.uploadFailed
        }

        print("✅ Authenticated user: \(currentUser.id)")
        print("✅ Session token exists: \(session.accessToken.prefix(20))...")
        print("✅ Target user ID: \(userId)")
        print("✅ Bucket: \(bucketName)")
        
        // Compress and prepare image
        guard let imageData = prepareImageForUpload(image) else {
            throw ImageUploadError.compressionFailed
        }
        
        uploadProgress = 0.3
        
        // Generate unique filename
        let fileName = generateAvatarFileName(for: userId)
        print("📁 File path: \(fileName)")

        uploadProgress = 0.5
        compressionInfo = "Uploading to cloud..."

        // Upload using SupabaseManager
        do {
            print("🔄 Starting upload via SupabaseManager")

            let publicURL = try await supabaseManager.uploadFile(
                to: bucketName,
                path: fileName,
                data: imageData,
                contentType: "image/jpeg"
            )

            uploadProgress = 1.0
            compressionInfo = "Upload complete!"

            print("✅ Avatar uploaded successfully: \(publicURL)")
            return publicURL

        } catch {
            print("❌ Upload error details: \(error)")
            if let storageError = error as? StorageError {
                print("❌ Storage error - Status: \(storageError.statusCode ?? "unknown")")
                print("❌ Storage error - Message: \(storageError.message)")
                print("❌ Storage error - Error: \(storageError.error ?? "unknown")")
            }
            throw ImageUploadError.uploadFailed
        }
    }
    
    // MARK: - Avatar Deletion
    func deleteAvatar(at url: String) async throws {
        guard let fileName = extractFileNameFromURL(url) else {
            throw ImageUploadError.invalidURL
        }
        
        try await supabase.storage
            .from(bucketName)
            .remove(paths: [fileName])
        
        print("✅ Avatar deleted successfully: \(fileName)")
    }
    
    // MARK: - Helper Methods
    private func prepareImageForUpload(_ image: UIImage) -> Data? {
        let originalSize = image.size
        let originalData = image.jpegData(compressionQuality: 1.0)
        let originalSizeKB = (originalData?.count ?? 0) / 1024

        compressionInfo = "Compressing \(originalSizeKB)KB image..."

        // Always resize to avatar size (256x256 max for avatars)
        let avatarSize: CGFloat = 256
        let resizedImage = resizeImageForAvatar(image, to: avatarSize)

        // Compress aggressively for avatars - start with moderate compression
        var compressionQuality: CGFloat = 0.7
        var imageData = resizedImage.jpegData(compressionQuality: compressionQuality)

        // Target file size: 150KB max for avatars (very small for fast loading)
        let targetSize = 150 * 1024 // 150KB

        // Reduce quality until we reach target size
        while let data = imageData, data.count > targetSize && compressionQuality > 0.2 {
            compressionQuality -= 0.1
            imageData = resizedImage.jpegData(compressionQuality: compressionQuality)
        }

        let finalSizeKB = (imageData?.count ?? 0) / 1024
        let compressionRatio = originalSizeKB > 0 ? Int((1.0 - Double(finalSizeKB) / Double(originalSizeKB)) * 100) : 0

        compressionInfo = "Compressed by \(compressionRatio)% (\(originalSizeKB)KB → \(finalSizeKB)KB)"

        print("📸 Avatar compressed: \(originalSize) → \(resizedImage.size)")
        print("📸 File size: \(originalSizeKB)KB → \(finalSizeKB)KB (\(compressionRatio)% reduction)")
        print("📸 Quality: \(Int(compressionQuality * 100))%")

        return imageData
    }

    private func resizeImageForAvatar(_ image: UIImage, to maxSize: CGFloat) -> UIImage {
        let size = image.size

        // Always resize - even if image is smaller, we want consistent sizes
        let aspectRatio = size.width / size.height

        var newSize: CGSize
        if size.width > size.height {
            newSize = CGSize(width: maxSize, height: maxSize / aspectRatio)
        } else {
            newSize = CGSize(width: maxSize * aspectRatio, height: maxSize)
        }

        // Use high quality rendering for better results
        let renderer = UIGraphicsImageRenderer(size: newSize)
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }

        print("📸 Avatar resized from \(size) to \(newSize)")
        return resizedImage
    }
    
    private func generateAvatarFileName(for userId: UUID) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        return "\(userId.uuidString)/avatar_\(timestamp).jpg"
    }
    
    private func extractFileNameFromURL(_ url: String) -> String? {
        guard let urlComponents = URLComponents(string: url) else {
            return nil
        }

        // Extract path after bucket name in URL
        let pathComponents = urlComponents.path.components(separatedBy: "/")

        // Look for pattern: .../storage/v1/object/public/avatars/[filename]
        if let avatarsIndex = pathComponents.firstIndex(of: bucketName),
           avatarsIndex + 1 < pathComponents.count {
            let filePathComponents = Array(pathComponents[(avatarsIndex + 1)...])
            return filePathComponents.joined(separator: "/")
        }

        return nil
    }
    
    // MARK: - Validation
    func validateImage(_ image: UIImage) -> ImageValidationResult {
        // Very relaxed validation - we'll handle compression automatically
        let minDimension: CGFloat = 50  // Much smaller minimum
        let maxOriginalSize: Int = 50 * 1024 * 1024 // 50MB original (we'll compress it)

        // Check minimum dimensions (very small minimum since we resize anyway)
        let size = image.size
        if size.width < minDimension || size.height < minDimension {
            return .failure(.tooSmall)
        }

        // Check if image is extremely large (probably corrupted)
        if size.width > 10000 || size.height > 10000 {
            return .failure(.tooLarge)
        }

        // Check original file size (very generous limit)
        if let data = image.jpegData(compressionQuality: 1.0),
           data.count > maxOriginalSize {
            return .failure(.fileTooLarge)
        }

        return .success
    }
}

// MARK: - Error Types
enum ImageUploadError: LocalizedError {
    case compressionFailed
    case uploadFailed
    case invalidURL
    case bucketNotFound
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .compressionFailed:
            return "Failed to compress image"
        case .uploadFailed:
            return "Failed to upload image"
        case .invalidURL:
            return "Invalid image URL"
        case .bucketNotFound:
            return "Storage bucket not found"
        case .networkError:
            return "Network error occurred"
        }
    }
}

enum ImageValidationResult {
    case success
    case failure(ImageValidationError)
}

enum ImageValidationError: LocalizedError {
    case tooSmall
    case tooLarge
    case fileTooLarge
    case invalidFormat

    var errorDescription: String? {
        switch self {
        case .tooSmall:
            return "Image is too small (minimum 50x50 pixels)"
        case .tooLarge:
            return "Image dimensions are too large (maximum 10000x10000 pixels)"
        case .fileTooLarge:
            return "Original file is too large (maximum 50MB). Please choose a smaller image."
        case .invalidFormat:
            return "Invalid image format. Please use JPEG, PNG, or WebP."
        }
    }
}

// MARK: - Preview Helper
#if DEBUG
extension ImageUploadManager {
    static let preview = ImageUploadManager()
}
#endif
