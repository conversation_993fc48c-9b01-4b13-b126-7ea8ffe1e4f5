import Foundation
import Supabase

@MainActor
class MotivationManager: ObservableObject {
    @Published var availableIcons: [MotivationIcon] = []
    @Published var sentMotivations: [SentMotivation] = []
    @Published var receivedMotivations: [SentMotivation] = []
    @Published var isLoading = false
    @Published var lastError: String?
    
    private let supabaseManager = SupabaseManager.shared
    
    enum MotivationError: Error {
        case notAuthenticated
        case insufficientPoints
        case iconNotFound
        case operationFailed
        case networkError
    }
    
    // MARK: - Load Available Icons
    func loadAvailableIcons() async {
        print("🎨 Loading available motivation icons...")
        isLoading = true
        defer { isLoading = false }
        
        do {
            let iconsResponse: [DatabaseMotivationIcon] = try await supabaseManager.client
                .from("motivation_icons")
                .select("*")
                .eq("is_active", value: true)
                .order("cost_points", ascending: true)
                .execute()
                .value
            
            availableIcons = iconsResponse.map { dbIcon in
                MotivationIcon(
                    id: dbIcon.id,
                    iconEmoji: dbIcon.iconEmoji,
                    iconName: dbIcon.iconName,
                    costPoints: dbIcon.costPoints,
                    isActive: dbIcon.isActive,
                    createdAt: dbIcon.createdAt,
                    updatedAt: dbIcon.updatedAt
                )
            }
            
            print("🎨 Loaded \(availableIcons.count) motivation icons")
        } catch {
            print("❌ Error loading motivation icons: \(error)")
            handleError(error)
        }
    }
    
    // MARK: - Send Motivation
    func sendMotivation(to userId: UUID, iconId: UUID) async -> Bool {
        guard let currentUserId = supabaseManager.authenticatedUserId else {
            lastError = "Not authenticated"
            return false
        }
        
        guard let selectedIcon = availableIcons.first(where: { $0.id == iconId }) else {
            lastError = "Icon not found"
            return false
        }
        
        print("🎉 Sending motivation \(selectedIcon.iconEmoji) to user \(userId)")
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Check if user has enough points
            let currentPoints = try await getCurrentUserPoints()
            if currentPoints < selectedIcon.costPoints {
                lastError = "Insufficient points"
                return false
            }
            
            // Send motivation and deduct points
            try await sendMotivationToDatabase(
                fromUserId: currentUserId,
                toUserId: userId,
                iconId: iconId,
                pointsSpent: selectedIcon.costPoints
            )
            
            print("🎉 Motivation sent successfully!")
            return true
        } catch {
            print("❌ Error sending motivation: \(error)")
            handleError(error)
            return false
        }
    }
    
    // MARK: - Get Current User Points
    func getCurrentUserPoints() async throws -> Int {
        guard let currentUserId = supabaseManager.authenticatedUserId else {
            throw MotivationError.notAuthenticated
        }
        
        struct UserPoints: Codable {
            let points: Int
        }

        let userResponse: [UserPoints] = try await supabaseManager.client
            .from("users")
            .select("points")
            .eq("id", value: currentUserId)
            .execute()
            .value
        
        return userResponse.first?.points ?? 0
    }
    
    // MARK: - Load Sent Motivations
    func loadSentMotivations() async {
        guard let currentUserId = supabaseManager.authenticatedUserId else { return }
        
        print("📤 Loading sent motivations...")
        isLoading = true
        defer { isLoading = false }
        
        do {
            let motivationsResponse: [DatabaseSentMotivation] = try await supabaseManager.client
                .from("sent_motivations")
                .select("*")
                .eq("from_user_id", value: currentUserId)
                .order("created_at", ascending: false)
                .limit(20)
                .execute()
                .value
            
            sentMotivations = motivationsResponse.map { dbMotivation in
                SentMotivation(
                    id: dbMotivation.id,
                    fromUserId: dbMotivation.fromUserId,
                    toUserId: dbMotivation.toUserId,
                    iconId: dbMotivation.iconId,
                    pointsSpent: dbMotivation.pointsSpent,
                    createdAt: dbMotivation.createdAt
                )
            }
            
            print("📤 Loaded \(sentMotivations.count) sent motivations")
        } catch {
            print("❌ Error loading sent motivations: \(error)")
            handleError(error)
        }
    }
    
    // MARK: - Get Sent Motivations to Specific User
    func getSentMotivationsToUser(_ userId: UUID) async -> [String: Int] {
        guard let currentUserId = supabaseManager.authenticatedUserId else { return [:] }

        print("📤 Loading sent motivations to user \(userId)...")

        do {
            // Get all sent motivations to this user
            let motivationsResponse: [DatabaseSentMotivation] = try await supabaseManager.client
                .from("sent_motivations")
                .select("*")
                .eq("from_user_id", value: currentUserId)
                .eq("to_user_id", value: userId)
                .execute()
                .value

            // Get all icons to map IDs to emojis
            let iconsResponse: [DatabaseMotivationIcon] = try await supabaseManager.client
                .from("motivation_icons")
                .select("*")
                .execute()
                .value

            // Create icon ID to emoji mapping
            var iconMap: [UUID: String] = [:]
            for icon in iconsResponse {
                iconMap[icon.id] = icon.iconEmoji
            }

            // Count motivations by emoji
            var result: [String: Int] = [:]
            for motivation in motivationsResponse {
                if let emoji = iconMap[motivation.iconId] {
                    result[emoji, default: 0] += 1
                }
            }

            print("📤 Found \(result.count) different motivations sent to user")
            return result
        } catch {
            print("❌ Error loading sent motivations: \(error)")
            return [:]
        }
    }

    // MARK: - Get Received Motivations for Specific User (Public)
    func getReceivedMotivationsForUser(_ userId: UUID) async -> [String: Int] {
        print("📥 Loading received motivations for user \(userId)...")

        do {
            // Get all received motivations for this user (from everyone)
            let motivationsResponse: [DatabaseSentMotivation] = try await supabaseManager.client
                .from("sent_motivations")
                .select("*")
                .eq("to_user_id", value: userId)
                .execute()
                .value

            // Get all icons to map IDs to emojis
            let iconsResponse: [DatabaseMotivationIcon] = try await supabaseManager.client
                .from("motivation_icons")
                .select("*")
                .execute()
                .value

            // Create icon ID to emoji mapping
            var iconMap: [UUID: String] = [:]
            for icon in iconsResponse {
                iconMap[icon.id] = icon.iconEmoji
            }

            // Count motivations by emoji
            var result: [String: Int] = [:]
            for motivation in motivationsResponse {
                if let emoji = iconMap[motivation.iconId] {
                    result[emoji, default: 0] += 1
                }
            }

            print("📥 Found \(result.count) different motivations received by user")
            return result
        } catch {
            print("❌ Error loading received motivations: \(error)")
            return [:]
        }
    }

    // MARK: - Load Received Motivations
    func loadReceivedMotivations() async {
        guard let currentUserId = supabaseManager.authenticatedUserId else { return }
        
        print("📥 Loading received motivations...")
        isLoading = true
        defer { isLoading = false }
        
        do {
            let motivationsResponse: [DatabaseSentMotivation] = try await supabaseManager.client
                .from("sent_motivations")
                .select("*")
                .eq("to_user_id", value: currentUserId)
                .order("created_at", ascending: false)
                .limit(20)
                .execute()
                .value
            
            receivedMotivations = motivationsResponse.map { dbMotivation in
                SentMotivation(
                    id: dbMotivation.id,
                    fromUserId: dbMotivation.fromUserId,
                    toUserId: dbMotivation.toUserId,
                    iconId: dbMotivation.iconId,
                    pointsSpent: dbMotivation.pointsSpent,
                    createdAt: dbMotivation.createdAt
                )
            }
            
            print("📥 Loaded \(receivedMotivations.count) received motivations")
        } catch {
            print("❌ Error loading received motivations: \(error)")
            handleError(error)
        }
    }
    
    // MARK: - Private Methods
    private func sendMotivationToDatabase(fromUserId: UUID, toUserId: UUID, iconId: UUID, pointsSpent: Int) async throws {
        print("💾 Sending motivation to database...")

        // Get current points first to ensure we have enough
        let currentPoints = try await getCurrentUserPoints()
        print("💰 Current points: \(currentPoints), Cost: \(pointsSpent)")

        if currentPoints < pointsSpent {
            print("❌ Insufficient points!")
            throw MotivationError.insufficientPoints
        }

        let newPoints = currentPoints - pointsSpent
        print("💰 New points will be: \(newPoints)")

        // Insert motivation record
        let motivationInsert = DatabaseSentMotivationInsert(
            fromUserId: fromUserId,
            toUserId: toUserId,
            iconId: iconId,
            pointsSpent: pointsSpent
        )

        print("💾 Inserting motivation record...")
        try await supabaseManager.client
            .from("sent_motivations")
            .insert(motivationInsert)
            .execute()
        print("💾 Motivation record inserted!")

        // Update points with the new value
        print("💰 Updating user points...")
        try await supabaseManager.client
            .from("users")
            .update(["points": newPoints])
            .eq("id", value: fromUserId)
            .execute()
        print("💰 Points updated successfully!")
    }
    
    private func handleError(_ error: Error) {
        if let postgrestError = error as? PostgrestError {
            lastError = postgrestError.message
        } else {
            lastError = error.localizedDescription
        }
    }
}
