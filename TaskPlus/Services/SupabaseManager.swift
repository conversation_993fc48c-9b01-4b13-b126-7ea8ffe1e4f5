//
//  SupabaseManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import Supabase

// MARK: - Supabase Manager
@MainActor
class SupabaseManager: ObservableObject {
    static let shared = SupabaseManager()
    
    // MARK: - Supabase Client
    private let supabase: SupabaseClient

    // Public access to client for other managers
    var client: SupabaseClient {
        return supabase
    }

    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        // Initialize Supabase client
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://bvqwlkudghfrrugjbvbh.supabase.co")!,
            supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2cXdsa3VkZ2hmcnJ1Z2pidmJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MTYwMjIsImV4cCI6MjA2NTA5MjAyMn0.nF85xHTVAqcNevob6O0LF5ErBOsiKGHN5e16u53ZApw"
        )

        // No need for connection check - we'll check when we actually use it
        isConnected = true
    }
    

    
    // MARK: - Authentication Status
    var isAuthenticated: Bool {
        return supabase.auth.currentUser != nil
    }

    /// الحصول على UUID المستخدم المصادق عليه من Supabase Auth
    var authenticatedUserId: UUID? {
        return supabase.auth.currentUser?.id
    }

    var currentUserId: UUID? {
        return supabase.auth.currentUser?.id
    }

    // MARK: - Authentication
    func signUp(email: String, password: String, userData: [String: Any]) async throws -> User {
        isLoading = true
        errorMessage = nil

        do {
            // Use simple signup without metadata for now
            let authResponse = try await supabase.auth.signUp(
                email: email,
                password: password
            )
            
            let _ = authResponse.user

            // Create local user object
            let localUser = User(
                username: userData["username"] as? String ?? "",
                displayName: userData["display_name"] as? String ?? "",
                email: email
            )

            // Note: Profile will be created automatically by database trigger
            // We could update it here with additional data if needed

            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signIn(email: String, password: String) async throws -> User {
        isLoading = true
        errorMessage = nil
        
        do {
            let authResponse = try await supabase.auth.signIn(
                email: email,
                password: password
            )
            
            let user = authResponse.user
            
            // Fetch user profile from users table
            let userProfile: UserProfile = try await supabase
                .from("users")
                .select()
                .eq("id", value: user.id)
                .single()
                .execute()
                .value

            var localUser = User(
                username: userProfile.username,
                displayName: userProfile.displayName,
                email: userProfile.email,
                bio: userProfile.bio
            )

            // Set profile change tracking
            localUser.lastDisplayNameChange = userProfile.lastDisplayNameChange
            localUser.usernameChanged = userProfile.usernameChanged
            
            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signOut() async throws {
        try await supabase.auth.signOut()
        currentUser = nil
    }

    // MARK: - Session Management
    func restoreSession() async {
        print("🔄 restoreSession() called")
        do {
            // Check if there's an existing session
            if let session = supabase.auth.currentSession {
                print("✅ Found existing Supabase session for user: \(session.user.id)")
                print("  - Session valid until: \(session.expiresAt)")
                print("  - User email: \(session.user.email ?? "unknown")")

                // Fetch user profile to restore currentUser
                let userProfile: UserProfile = try await supabase
                    .from("users")
                    .select()
                    .eq("id", value: session.user.id)
                    .single()
                    .execute()
                    .value

                var localUser = User(
                    username: userProfile.username,
                    displayName: userProfile.displayName,
                    email: userProfile.email,
                    bio: userProfile.bio
                )

                // Set profile change tracking
                localUser.lastDisplayNameChange = userProfile.lastDisplayNameChange
                localUser.usernameChanged = userProfile.usernameChanged

                currentUser = localUser
                print("✅ Session restored successfully for user: \(localUser.username)")
            } else {
                print("ℹ️ No existing Supabase session found")
            }
        } catch {
            print("❌ Failed to restore session: \(error)")
        }
    }

    // MARK: - User Management
    private func ensureUserExists(for userId: UUID) async {
        do {
            // Check if user exists
            let _: UserProfile = try await supabase
                .from("users")
                .select()
                .eq("id", value: userId)
                .single()
                .execute()
                .value

            print("✅ User exists: \(userId)")

        } catch {
            print("⚠️ User not found, creating one...")

            // Create user if it doesn't exist
            do {
                guard let userEmail = supabase.auth.currentUser?.email else {
                    print("❌ Cannot create user: no email")
                    return
                }

                // Create user using simple insert struct
                let userInsert = UserInsert(
                    id: userId,
                    username: "user_\(userId.uuidString.prefix(8))",
                    displayName: userEmail.components(separatedBy: "@").first ?? "User",
                    email: userEmail,
                    privacySetting: "friends"
                )

                let _: [UserProfile] = try await supabase
                    .from("users")
                    .insert(userInsert)
                    .select()
                    .execute()
                    .value

                print("✅ User created successfully: \(userId)")

            } catch {
                print("❌ Failed to create user: \(error)")
            }
        }
    }
    
    // MARK: - User Profile Management
    func updateProfile(_ user: User) async throws {
        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let userUpdate = UserUpdate(
            username: user.username,
            displayName: user.displayName,
            bio: user.bio,
            avatarUrl: user.avatarURL,
            lastDisplayNameChange: user.lastDisplayNameChange,
            usernameChanged: user.usernameChanged,
            updatedAt: Date()
        )

        try await supabase
            .from("users")
            .update(userUpdate)
            .eq("id", value: currentUserId)
            .execute()

        currentUser = user
    }

    // MARK: - Storage Management
    func uploadFile(to bucket: String, path: String, data: Data, contentType: String) async throws -> String {
        print("🔄 Uploading file to bucket: \(bucket), path: \(path)")

        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        print("✅ Authenticated user for upload: \(currentUserId)")

        // Upload file
        let uploadedFile = try await supabase.storage
            .from(bucket)
            .upload(
                path: path,
                file: data,
                options: FileOptions(
                    cacheControl: "3600",
                    contentType: contentType,
                    upsert: true
                )
            )

        print("✅ File uploaded successfully: \(uploadedFile)")

        // Get public URL
        let publicURL = try supabase.storage
            .from(bucket)
            .getPublicURL(path: path)

        print("✅ Public URL generated: \(publicURL)")
        return publicURL.absoluteString
    }
    
    // MARK: - Task Management
    func createTask(_ task: Task) async throws -> Task {
        print("🔍 createTask() called for: \(task.title)")
        print("🔍 Current session exists: \(supabase.auth.currentSession != nil)")
        print("🔍 Current user ID: \(supabase.auth.currentUser?.id.uuidString ?? "NIL")")

        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ createTask failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ createTask proceeding with Auth UUID: \(authUserId)")

        // Ensure user exists before creating task
        await ensureUserExists(for: authUserId)

        let taskInsert = TaskInsert(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            taskType: task.taskType.rawValue,
            createdBy: authUserId,
            groupId: task.groupId,
            subtasks: task.subtasks.map { SubtaskData(from: $0) },
            attachments: task.attachments.map { AttachmentData(from: $0) },
            location: task.location != nil ? LocationData(from: task.location!) : nil,
            reminderTime: task.reminderTime,
            isImportant: task.isImportant,
            difficulty: task.difficulty.rawValue,
            category: task.category != nil ? CategoryData(from: task.category!) : nil,
            tags: task.tags,
            estimatedDuration: task.estimatedDuration,
            recurrence: task.recurrence != nil ? RecurrenceData(from: task.recurrence!) : nil
        )
        
        let insertedTask: TaskResponse = try await supabase
            .from("tasks")
            .insert(taskInsert)
            .select()
            .single()
            .execute()
            .value

        print("✅ createTask completed successfully")
        print("  - Task ID: \(insertedTask.id)")
        print("  - Task Title: \(insertedTask.title)")
        print("  - Created By: \(insertedTask.createdBy)")

        return insertedTask.toTask()
    }
    
    func fetchTasks() async throws -> [Task] {
        print("🔍 fetchTasks() called")
        print("🔍 Current session exists: \(supabase.auth.currentSession != nil)")
        print("🔍 Current user ID: \(supabase.auth.currentUser?.id.uuidString ?? "NIL")")
        print("🔍 SupabaseManager currentUser: \(currentUser?.username ?? "NIL")")

        guard let currentUserId = supabase.auth.currentUser?.id else {
            print("❌ fetchTasks failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ fetchTasks proceeding with user ID: \(currentUserId)")
        print("🔍 Querying tasks for user: \(currentUserId.uuidString)")
        
        // Use Auth UUID directly (should match profile.id after trigger fix)
        let taskResponses: [TaskResponse] = try await supabase
            .from("tasks")
            .select()
            .eq("created_by", value: currentUserId)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ fetchTasks completed: found \(taskResponses.count) tasks")
        for task in taskResponses {
            print("  - Task: \(task.title)")
        }

        return taskResponses.map { $0.toTask() }
    }
    
    func updateTask(_ task: Task) async throws {
        print("🔍 updateTask() called for: \(task.title)")
        print("🔍 Task ID: \(task.id)")

        let taskUpdate = TaskUpdate(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            completedAt: task.completedAt,
            updatedAt: Date(),
            subtasks: task.subtasks.map { SubtaskData(from: $0) },
            attachments: task.attachments.map { AttachmentData(from: $0) },
            location: task.location != nil ? LocationData(from: task.location!) : nil,
            reminderTime: task.reminderTime,
            isImportant: task.isImportant,
            difficulty: task.difficulty.rawValue,
            category: task.category != nil ? CategoryData(from: task.category!) : nil,
            tags: task.tags,
            estimatedDuration: task.estimatedDuration,
            recurrence: task.recurrence != nil ? RecurrenceData(from: task.recurrence!) : nil
        )

        try await supabase
            .from("tasks")
            .update(taskUpdate)
            .eq("id", value: task.id)
            .execute()

        print("✅ updateTask completed successfully")
    }
    
    func deleteTask(_ taskId: UUID) async throws {
        print("🔍 deleteTask() called for ID: \(taskId)")

        try await supabase
            .from("tasks")
            .delete()
            .eq("id", value: taskId)
            .execute()

        print("✅ deleteTask completed successfully")
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Supabase Errors
enum SupabaseError: LocalizedError {
    case notAuthenticated
    case authenticationFailed
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .authenticationFailed:
            return "Authentication failed"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}

// MARK: - Supabase Data Models
struct UserProfile: Codable {
    let id: UUID
    let username: String
    let displayName: String
    let email: String
    let bio: String?
    let avatarUrl: String?
    let privacySetting: String
    let lastDisplayNameChange: Date?
    let usernameChanged: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, username, email, bio
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case privacySetting = "privacy_setting"
        case lastDisplayNameChange = "last_display_name_change"
        case usernameChanged = "username_changed"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct UserInsert: Codable {
    let id: UUID
    let username: String
    let displayName: String
    let email: String
    let privacySetting: String

    enum CodingKeys: String, CodingKey {
        case id, username, email
        case displayName = "display_name"
        case privacySetting = "privacy_setting"
    }
}

struct UserUpdate: Codable {
    let username: String
    let displayName: String
    let bio: String?
    let avatarUrl: String?
    let lastDisplayNameChange: Date?
    let usernameChanged: Bool?
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case username, bio
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case lastDisplayNameChange = "last_display_name_change"
        case usernameChanged = "username_changed"
        case updatedAt = "updated_at"
    }
}



struct TaskInsert: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let recurrence: RecurrenceData?

    enum CodingKeys: String, CodingKey {
        case title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case estimatedDuration = "estimated_duration"
    }
}

struct TaskResponse: Codable {
    let id: UUID
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?
    let completedAt: Date?
    let createdAt: Date
    let updatedAt: Date

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    private let _estimatedDuration: FlexibleTimeInterval?
    let recurrence: RecurrenceData?

    // Computed property to handle flexible estimated duration
    var estimatedDuration: TimeInterval? {
        return _estimatedDuration?.value
    }

    enum CodingKeys: String, CodingKey {
        case id, title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
        case completedAt = "completed_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case _estimatedDuration = "estimated_duration"
    }
    
    func toTask() -> Task {
        var task = Task(
            id: id,
            title: title,
            description: description,
            dueDate: dueDate,
            priority: Task.Priority(rawValue: priority) ?? .medium,
            status: Task.TaskStatus(rawValue: status) ?? .inProgress,
            createdByUserId: createdBy,
            taskType: Task.TaskType(rawValue: taskType) ?? .personal,
            groupId: groupId,
            completedAt: completedAt,
            createdAt: createdAt,
            updatedAt: updatedAt
        )

        // Set enhanced properties
        task.subtasks = subtasks?.map { $0.toSubtask() } ?? []
        task.location = location?.toTaskLocation()
        task.reminderTime = reminderTime
        task.isImportant = isImportant ?? false
        task.difficulty = Task.Difficulty(rawValue: difficulty ?? "medium") ?? .medium
        task.category = category?.toTaskCategory()
        task.tags = tags ?? []
        task.estimatedDuration = estimatedDuration
        task.recurrence = recurrence?.toRecurrencePattern()

        // Convert attachments
        if let attachmentData = attachments {
            task.attachments = attachmentData.map { data in
                TaskAttachment(
                    name: data.name,
                    type: TaskAttachment.AttachmentType(rawValue: data.type) ?? .document,
                    url: data.url
                )
            }
        }

        return task
    }
}

struct TaskUpdate: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let completedAt: Date?
    let updatedAt: Date

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let recurrence: RecurrenceData?

    enum CodingKeys: String, CodingKey {
        case title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case completedAt = "completed_at"
        case updatedAt = "updated_at"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case estimatedDuration = "estimated_duration"
    }
}

// MARK: - Enhanced Data Models for Supabase
struct SubtaskData: Codable {
    let id: UUID
    let title: String
    let isCompleted: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, title
        case isCompleted = "is_completed"
        case createdAt = "created_at"
    }

    init(from subtask: Subtask) {
        self.id = subtask.id
        self.title = subtask.title
        self.isCompleted = subtask.isCompleted
        self.createdAt = subtask.createdAt
    }

    func toSubtask() -> Subtask {
        var subtask = Subtask(title: title)
        subtask.isCompleted = isCompleted
        return subtask
    }
}

struct AttachmentData: Codable {
    let id: UUID
    let name: String
    let type: String
    let url: String?
    let localPath: String?
    let size: Int64?
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, type, url, size
        case localPath = "local_path"
        case createdAt = "created_at"
    }

    init(from attachment: TaskAttachment) {
        self.id = attachment.id
        self.name = attachment.name
        self.type = attachment.type.rawValue
        self.url = attachment.url
        self.localPath = attachment.localPath
        self.size = attachment.size
        self.createdAt = attachment.createdAt
    }
}

struct LocationData: Codable {
    let name: String
    let address: String?
    let latitude: Double?
    let longitude: Double?
    let radius: Double?

    init(from location: TaskLocation) {
        self.name = location.name
        self.address = location.address
        self.latitude = location.latitude
        self.longitude = location.longitude
        self.radius = location.radius
    }

    func toTaskLocation() -> TaskLocation {
        return TaskLocation(
            name: name,
            address: address,
            latitude: latitude,
            longitude: longitude,
            radius: radius
        )
    }
}

struct CategoryData: Codable {
    let id: UUID
    let name: String
    let color: String
    let icon: String
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, color, icon
        case createdAt = "created_at"
    }

    init(from category: TaskCategory) {
        self.id = category.id
        self.name = category.name
        self.color = category.color
        self.icon = category.icon
        self.createdAt = category.createdAt
    }

    func toTaskCategory() -> TaskCategory {
        return TaskCategory(name: name, color: color, icon: icon)
    }
}

struct RecurrenceData: Codable {
    let type: String
    let interval: Int
    let daysOfWeek: [Int]?
    let endDate: Date?

    enum CodingKeys: String, CodingKey {
        case type, interval
        case daysOfWeek = "days_of_week"
        case endDate = "end_date"
    }

    init(from recurrence: RecurrencePattern) {
        self.type = recurrence.type.rawValue
        self.interval = recurrence.interval
        self.daysOfWeek = recurrence.daysOfWeek
        self.endDate = recurrence.endDate
    }

    func toRecurrencePattern() -> RecurrencePattern {
        return RecurrencePattern(
            type: RecurrencePattern.RecurrenceType(rawValue: type) ?? .daily,
            interval: interval,
            daysOfWeek: daysOfWeek,
            endDate: endDate
        )
    }
}

// MARK: - Flexible TimeInterval for Database Compatibility
struct FlexibleTimeInterval: Codable {
    let value: TimeInterval?

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if try container.decodeNil() {
            value = nil
        } else if let doubleValue = try? container.decode(Double.self) {
            value = doubleValue
        } else if let intValue = try? container.decode(Int.self) {
            value = TimeInterval(intValue)
        } else if let stringValue = try? container.decode(String.self) {
            // Try to parse string as number
            if let doubleFromString = Double(stringValue) {
                value = doubleFromString
            } else {
                print("⚠️ Could not parse estimated_duration string: '\(stringValue)'")
                value = nil
            }
        } else {
            print("⚠️ Unknown type for estimated_duration")
            value = nil
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(value)
    }
}

// MARK: - Group Management Extension
extension SupabaseManager {
    func createGroup(_ group: Group) async throws -> Group {
        print("🔄 Creating group in Supabase: \(group.name)")

        // استخدام نفس النهج المستخدم في createTask()
        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ createGroup failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ createGroup proceeding with Auth UUID: \(authUserId)")
        print("🔍 Group owner ID: \(group.ownerId.uuidString)")

        // التأكد من تطابق المستخدم المصادق مع مالك المجموعة
        guard authUserId == group.ownerId else {
            print("❌ Auth user ID (\(authUserId)) doesn't match group owner ID (\(group.ownerId))")
            throw NSError(domain: "SupabaseManager", code: 1003, userInfo: [NSLocalizedDescriptionKey: "User ID mismatch"])
        }

        let groupData: [String: AnyJSON] = [
            "id": AnyJSON.string(group.id.uuidString),
            "name": AnyJSON.string(group.name),
            "description": group.description.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "avatar_url": group.avatarURL.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "is_private": AnyJSON.bool(group.isPrivate),
            "owner_id": AnyJSON.string(group.ownerId.uuidString),
            "member_ids": AnyJSON.array(group.memberIds.map { AnyJSON.string($0.uuidString) }),
            "group_code": AnyJSON.string(group.groupCode),  // إضافة رمز المجموعة
            "created_at": AnyJSON.string(ISO8601DateFormatter().string(from: group.createdAt)),
            "updated_at": AnyJSON.string(ISO8601DateFormatter().string(from: group.updatedAt))
        ]

        let response: [GroupResponse] = try await supabase
            .from("groups")
            .insert(groupData)
            .select()
            .execute()
            .value

        guard let createdGroup = response.first else {
            throw NSError(domain: "SupabaseManager", code: 1001, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])
        }

        print("✅ Group created in Supabase: \(createdGroup.name)")
        return createdGroup.toGroup()
    }

    func updateGroup(_ group: Group) async throws {
        print("🔄 Updating group in Supabase: \(group.name)")

        let groupData: [String: AnyJSON] = [
            "name": AnyJSON.string(group.name),
            "description": AnyJSON.string(group.description ?? ""),
            "avatar_url": AnyJSON.string(group.avatarURL ?? ""),
            "is_private": AnyJSON.bool(group.isPrivate),
            "member_ids": AnyJSON.array(group.memberIds.map { AnyJSON.string($0.uuidString) }),
            "updated_at": AnyJSON.string(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("groups")
            .update(groupData)
            .eq("id", value: group.id.uuidString)
            .execute()

        print("✅ Group updated in Supabase: \(group.name)")
    }

    func deleteGroup(_ groupId: UUID) async throws {
        print("🔄 Deleting group from Supabase: \(groupId)")

        try await supabase
            .from("groups")
            .delete()
            .eq("id", value: groupId.uuidString)
            .execute()

        print("✅ Group deleted from Supabase: \(groupId)")
    }

    func fetchUserGroups() async throws -> [Group] {
        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ fetchUserGroups failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("🔄 Fetching user groups from Supabase for user: \(authUserId)")

        // البحث في المجموعات التي المستخدم مالك لها أو عضو فيها
        let response: [GroupResponse] = try await supabase
            .from("groups")
            .select()
            .or("owner_id.eq.\(authUserId.uuidString),member_ids.cs.{\(authUserId.uuidString)}")
            .execute()
            .value

        print("✅ Fetched \(response.count) groups from Supabase")
        return response.map { $0.toGroup() }
    }

    /// البحث عن مجموعة برمزها
    func findGroupByCode(_ code: String) async throws -> Group? {
        print("🔍 Searching for group with code: \(code)")

        let response: [GroupResponse] = try await supabase
            .from("groups")
            .select()
            .eq("group_code", value: code.uppercased())
            .limit(1)
            .execute()
            .value

        if let groupResponse = response.first {
            print("✅ Found group: \(groupResponse.name)")
            return groupResponse.toGroup()
        } else {
            print("❌ No group found with code: \(code)")
            return nil
        }
    }

    // MARK: - Group Tasks Management
    func createGroupTask(_ task: GroupTask) async throws -> GroupTask {
        print("🔄 Creating group task in Supabase: \(task.title)")

        // استخدام نفس النهج المستخدم في createTask()
        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ createGroupTask failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ createGroupTask proceeding with Auth UUID: \(authUserId)")
        print("🔍 Task creator ID: \(task.createdById.uuidString)")
        print("🔍 Group ID: \(task.groupId.uuidString)")

        // التأكد من تطابق المستخدم المصادق مع منشئ المهمة
        guard authUserId == task.createdById else {
            print("❌ Auth user ID (\(authUserId)) doesn't match task creator ID (\(task.createdById))")
            throw NSError(domain: "SupabaseManager", code: 1003, userInfo: [NSLocalizedDescriptionKey: "User ID mismatch"])
        }

        let taskData: [String: AnyJSON] = [
            "id": AnyJSON.string(task.id.uuidString),
            "title": AnyJSON.string(task.title),
            "description": task.description.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "group_id": AnyJSON.string(task.groupId.uuidString),
            "group_code": AnyJSON.string(task.groupCode),  // استخدام رمز المجموعة
            "created_by_id": AnyJSON.string(task.createdById.uuidString),
            "task_type": AnyJSON.string(task.taskType.isGroupTask ? "group_task" : "individual_task"),
            "assigned_to_id": task.taskType.assigneeId.map { AnyJSON.string($0.uuidString) } ?? AnyJSON.null,
            "priority": AnyJSON.string(task.priority.rawValue),
            "status": AnyJSON.string(task.status.rawValue),
            "due_date": task.dueDate.map { AnyJSON.string(ISO8601DateFormatter().string(from: $0)) } ?? AnyJSON.null,
            "estimated_duration": task.estimatedDuration.map { AnyJSON.string("\($0) seconds") } ?? AnyJSON.null,
            "is_important": AnyJSON.bool(task.isImportant),
            "difficulty": AnyJSON.string(task.difficulty.rawValue),
            "category_name": task.categoryName.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "tags": AnyJSON.array(task.tags.map { AnyJSON.string($0) }),
            "completed_by_members": AnyJSON.object(Dictionary(uniqueKeysWithValues: task.completedByMembers.map { (key, value) in
                (key.uuidString, AnyJSON.string(ISO8601DateFormatter().string(from: value)))
            })),
            "total_members": AnyJSON.double(Double(task.totalMembers)),
            "created_at": AnyJSON.string(ISO8601DateFormatter().string(from: task.createdAt)),
            "updated_at": AnyJSON.string(ISO8601DateFormatter().string(from: task.updatedAt))
        ]

        let response: [GroupTaskResponse] = try await supabase
            .from("group_tasks")
            .insert(taskData)
            .select()
            .execute()
            .value

        guard let createdTask = response.first else {
            throw NSError(domain: "SupabaseManager", code: 1001, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])
        }

        print("✅ Group task created in Supabase: \(createdTask.title)")
        return createdTask.toGroupTask()
    }

    func updateGroupTask(_ task: GroupTask) async throws {
        print("🔄 Updating group task in Supabase: \(task.title)")

        let taskData: [String: AnyJSON] = [
            "title": AnyJSON.string(task.title),
            "description": task.description.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "task_type": AnyJSON.string(task.taskType.isGroupTask ? "group_task" : "individual_task"),
            "assigned_to_id": task.taskType.assigneeId.map { AnyJSON.string($0.uuidString) } ?? AnyJSON.null,
            "priority": AnyJSON.string(task.priority.rawValue),
            "status": AnyJSON.string(task.status.rawValue),
            "due_date": task.dueDate.map { AnyJSON.string(ISO8601DateFormatter().string(from: $0)) } ?? AnyJSON.null,
            "estimated_duration": task.estimatedDuration.map { AnyJSON.string("\($0) seconds") } ?? AnyJSON.null,
            "is_important": AnyJSON.bool(task.isImportant),
            "difficulty": AnyJSON.string(task.difficulty.rawValue),
            "category_name": task.categoryName.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "tags": AnyJSON.array(task.tags.map { AnyJSON.string($0) }),
            "completed_by_members": AnyJSON.object(Dictionary(uniqueKeysWithValues: task.completedByMembers.map { (key, value) in
                (key.uuidString, AnyJSON.string(ISO8601DateFormatter().string(from: value)))
            })),
            "total_members": AnyJSON.double(Double(task.totalMembers)),
            "updated_at": AnyJSON.string(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("group_tasks")
            .update(taskData)
            .eq("id", value: task.id.uuidString)
            .execute()

        print("✅ Group task updated in Supabase: \(task.title)")
    }

    func deleteGroupTask(_ taskId: UUID) async throws {
        print("🔄 Deleting group task from Supabase: \(taskId)")

        try await supabase
            .from("group_tasks")
            .delete()
            .eq("id", value: taskId.uuidString)
            .execute()

        print("✅ Group task deleted from Supabase: \(taskId)")
    }

    func fetchGroupTasks(for groupId: UUID) async throws -> [GroupTask] {
        print("🔄 Fetching group tasks from Supabase for group: \(groupId)")

        let response: [GroupTaskResponse] = try await supabase
            .from("group_tasks")
            .select()
            .eq("group_id", value: groupId.uuidString)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ Fetched \(response.count) group tasks from Supabase")
        return response.map { $0.toGroupTask() }
    }

    // MARK: - Group Invitations

    /// إنشاء دعوة مجموعة جديدة
    func createGroupInvitation(_ invitation: GroupInvitation) async throws -> GroupInvitation {
        print("🔄 Creating group invitation in Supabase")

        let invitationData: [String: AnyJSON] = [
            "id": AnyJSON.string(invitation.id.uuidString),
            "group_id": AnyJSON.string(invitation.groupId.uuidString),
            "invited_user_id": AnyJSON.string(invitation.invitedUserId.uuidString),
            "invited_by_user_id": AnyJSON.string(invitation.invitedByUserId.uuidString),
            "message": invitation.message.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "status": AnyJSON.string(invitation.status.rawValue),
            "created_at": AnyJSON.string(ISO8601DateFormatter().string(from: invitation.createdAt))
        ]

        let response: [GroupInvitationResponse] = try await supabase
            .from("group_invitations")
            .insert(invitationData)
            .select()
            .execute()
            .value

        guard let createdInvitation = response.first else {
            throw NSError(domain: "SupabaseManager", code: 1001, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])
        }

        print("✅ Group invitation created in Supabase: \(createdInvitation.id)")
        return createdInvitation.toGroupInvitation()
    }

    /// جلب دعوات المجموعة
    func fetchGroupInvitations(for groupId: UUID) async throws -> [GroupInvitation] {
        print("🔄 Fetching group invitations from Supabase for group: \(groupId)")

        let response: [GroupInvitationResponse] = try await supabase
            .from("group_invitations")
            .select()
            .eq("group_id", value: groupId.uuidString)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ Fetched \(response.count) group invitations from Supabase")
        return response.map { $0.toGroupInvitation() }
    }

    /// جلب دعوات المستخدم
    func fetchUserInvitations() async throws -> [GroupInvitation] {
        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ fetchUserInvitations failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("🔄 Fetching user invitations from Supabase for user: \(authUserId)")

        let response: [GroupInvitationResponse] = try await supabase
            .from("group_invitations")
            .select()
            .eq("invited_user_id", value: authUserId.uuidString)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ Fetched \(response.count) user invitations from Supabase")
        return response.map { $0.toGroupInvitation() }
    }

    /// تحديث حالة الدعوة
    func updateInvitationStatus(_ invitationId: UUID, status: GroupInvitation.InvitationStatus) async throws {
        print("🔄 Updating invitation status in Supabase: \(invitationId) -> \(status.rawValue)")

        let updateData: [String: AnyJSON] = [
            "status": AnyJSON.string(status.rawValue),
            "responded_at": AnyJSON.string(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("group_invitations")
            .update(updateData)
            .eq("id", value: invitationId.uuidString)
            .execute()

        print("✅ Invitation status updated in Supabase")
    }

    /// تحديث المجموعة مع قائمة الأعضاء الجديدة
    func updateGroup(groupId: UUID, name: String, description: String?, isPrivate: Bool, memberIds: [UUID]) async throws {
        print("🔄 Updating group with new members in Supabase: \(groupId)")

        let updateData: [String: AnyJSON] = [
            "name": AnyJSON.string(name),
            "description": description.map { AnyJSON.string($0) } ?? AnyJSON.null,
            "is_private": AnyJSON.bool(isPrivate),
            "member_ids": AnyJSON.array(memberIds.map { AnyJSON.string($0.uuidString) }),
            "updated_at": AnyJSON.string(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("groups")
            .update(updateData)
            .eq("id", value: groupId.uuidString)
            .execute()

        print("✅ Group updated with new members in Supabase")
    }

    // MARK: - User Profile Management

    /// جلب معلومات المستخدم بما في ذلك الصورة الشخصية
    func fetchUserProfile(userId: UUID) async throws -> UserProfile? {
        print("🔄 Fetching user profile from Supabase for user: \(userId)")

        let response: [UserProfileResponse] = try await supabase
            .from("users")
            .select("id, username, email, full_name, avatar_url, created_at, updated_at")
            .eq("id", value: userId.uuidString)
            .limit(1)
            .execute()
            .value

        if let userProfile = response.first {
            print("✅ Fetched user profile: \(userProfile.username)")
            return userProfile.toUserProfile()
        } else {
            print("❌ User profile not found: \(userId)")
            return nil
        }
    }

    /// جلب معلومات عدة مستخدمين
    func fetchUserProfiles(userIds: [UUID]) async throws -> [UserProfile] {
        guard !userIds.isEmpty else { return [] }

        print("🔄 Fetching \(userIds.count) user profiles from Supabase")

        let userIdStrings = userIds.map { $0.uuidString }

        let response: [UserProfileResponse] = try await supabase
            .from("users")
            .select("id, username, email, full_name, avatar_url, created_at, updated_at")
            .in("id", values: userIdStrings)
            .execute()
            .value

        print("✅ Fetched \(response.count) user profiles from Supabase")
        return response.map { $0.toUserProfile() }
    }

    /// البحث عن مستخدم بالاسم أو البريد الإلكتروني
    func searchUsers(query: String) async throws -> [UserProfile] {
        print("🔍 Searching users with query: \(query)")

        let response: [UserProfileResponse] = try await supabase
            .from("users")
            .select("id, username, email, full_name, avatar_url, created_at, updated_at")
            .or("username.ilike.%\(query)%,email.ilike.%\(query)%,full_name.ilike.%\(query)%")
            .limit(20)
            .execute()
            .value

        print("✅ Found \(response.count) users matching query")
        return response.map { $0.toUserProfile() }
    }
}

// MARK: - Group Data Models
struct GroupResponse: Codable {
    let id: UUID
    let name: String
    let description: String?
    let avatarUrl: String?
    let isPrivate: Bool
    let ownerId: UUID
    let memberIds: [UUID]?
    let groupCode: String
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case avatarUrl = "avatar_url"
        case isPrivate = "is_private"
        case ownerId = "owner_id"
        case memberIds = "member_ids"
        case groupCode = "group_code"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    func toGroup() -> Group {
        var group = Group(name: name, description: description, ownerId: ownerId, isPrivate: isPrivate)

        // ✅ تحديث ID بالـ ID الصحيح من قاعدة البيانات
        group.id = id

        group.avatarURL = avatarUrl
        group.memberIds = memberIds ?? [ownerId]
        group.groupCode = groupCode
        group.createdAt = createdAt
        group.updatedAt = updatedAt
        return group
    }
}

// MARK: - Group Invitation Response Model
struct GroupInvitationResponse: Codable {
    let id: UUID
    let groupId: UUID
    let invitedUserId: UUID
    let invitedByUserId: UUID
    let message: String?
    let status: String
    let createdAt: Date
    let respondedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case groupId = "group_id"
        case invitedUserId = "invited_user_id"
        case invitedByUserId = "invited_by_user_id"
        case message, status
        case createdAt = "created_at"
        case respondedAt = "responded_at"
    }

    func toGroupInvitation() -> GroupInvitation {
        var invitation = GroupInvitation(
            groupId: groupId,
            invitedUserId: invitedUserId,
            invitedByUserId: invitedByUserId,
            message: message
        )

        // تحديث البيانات من قاعدة البيانات
        invitation.id = id
        invitation.status = GroupInvitation.InvitationStatus(rawValue: status) ?? .pending
        invitation.createdAt = createdAt
        invitation.respondedAt = respondedAt

        return invitation
    }
}

// MARK: - User Profile Response Model
struct UserProfileResponse: Codable {
    let id: UUID
    let username: String
    let email: String
    let fullName: String?
    let avatarUrl: String?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, username, email
        case fullName = "full_name"
        case avatarUrl = "avatar_url"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    func toUserProfile() -> UserProfile {
        return UserProfile(
            id: id,
            username: username,
            displayName: fullName ?? username,
            email: email,
            bio: nil,
            avatarUrl: avatarUrl,
            privacySetting: .public,
            lastDisplayNameChange: nil,
            usernameChanged: false,
            createdAt: createdAt,
            updatedAt: updatedAt
        )
    }
}



// MARK: - Group Task Data Models
struct GroupTaskResponse: Codable {
    let id: UUID
    let title: String
    let description: String?
    let groupId: UUID
    let groupCode: String
    let createdById: UUID
    let taskType: String
    let assignedToId: UUID?
    let priority: String
    let status: String
    let dueDate: Date?
    let estimatedDuration: String?
    let isImportant: Bool
    let difficulty: String
    let categoryName: String?
    let tags: [String]
    let completedByMembers: [String: String]
    let totalMembers: Int
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, title, description
        case groupId = "group_id"
        case groupCode = "group_code"
        case createdById = "created_by_id"
        case taskType = "task_type"
        case assignedToId = "assigned_to_id"
        case priority, status
        case dueDate = "due_date"
        case estimatedDuration = "estimated_duration"
        case isImportant = "is_important"
        case difficulty
        case categoryName = "category_name"
        case tags
        case completedByMembers = "completed_by_members"
        case totalMembers = "total_members"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    func toGroupTask() -> GroupTask {
        let taskType: GroupTask.GroupTaskType
        if self.taskType == "group_task" {
            taskType = .groupTask
        } else if let assigneeId = assignedToId {
            taskType = .individualTask(assigneeId)
        } else {
            taskType = .groupTask // fallback
        }

        // Convert completed_by_members from [String: String] to [UUID: Date]
        let completedByMembersConverted: [UUID: Date] = completedByMembers.compactMapValues { dateString in
            ISO8601DateFormatter().date(from: dateString)
        }.compactMapKeys { uuidString in
            UUID(uuidString: uuidString)
        }

        var task = GroupTask(
            title: title,
            description: description,
            dueDate: dueDate,
            priority: GroupTask.Priority(rawValue: priority) ?? .medium,
            groupId: groupId,
            groupCode: groupCode,
            createdById: createdById,
            taskType: taskType,
            totalMembers: totalMembers
        )

        task.id = id
        task.status = GroupTask.GroupTaskStatus(rawValue: status) ?? .active
        task.isImportant = isImportant
        task.difficulty = GroupTask.Difficulty(rawValue: difficulty) ?? .medium
        task.categoryName = categoryName
        task.tags = tags
        task.completedByMembers = completedByMembersConverted
        task.createdAt = createdAt
        task.updatedAt = updatedAt

        if let durationString = estimatedDuration,
           let seconds = Double(durationString.replacingOccurrences(of: " seconds", with: "")) {
            task.estimatedDuration = TimeInterval(seconds)
        }

        return task
    }
}

// Helper extension for Dictionary key mapping
extension Dictionary {
    func compactMapKeys<T>(_ transform: (Key) throws -> T?) rethrows -> [T: Value] {
        var result: [T: Value] = [:]
        for (key, value) in self {
            if let transformedKey = try transform(key) {
                result[transformedKey] = value
            }
        }
        return result
    }
}
