//
//  NotificationManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import UserNotifications
import SwiftUI

// MARK: - Notification Manager
@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var notificationSettings: NotificationSettings = NotificationSettings()
    
    private init() {
        checkAuthorizationStatus()
        setupNotificationCategories()
    }
    
    // MARK: - Interactive Notifications Setup
    private func setupNotificationCategories() {
        // Task Reminder Category with Actions
        let completeAction = UNNotificationAction(
            identifier: "COMPLETE_TASK",
            title: "✅ Mark Complete",
            options: [.foreground]
        )

        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE_TASK",
            title: "⏰ Snooze 15min",
            options: []
        )

        let viewAction = UNNotificationAction(
            identifier: "VIEW_TASK",
            title: "👁️ View Details",
            options: [.foreground]
        )

        let taskReminderCategory = UNNotificationCategory(
            identifier: "TASK_REMINDER",
            actions: [completeAction, snoozeAction, viewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Overdue Task Category
        let urgentCompleteAction = UNNotificationAction(
            identifier: "COMPLETE_TASK",
            title: "✅ Complete Now",
            options: [.foreground]
        )

        let rescheduleAction = UNNotificationAction(
            identifier: "RESCHEDULE_TASK",
            title: "📅 Reschedule",
            options: [.foreground]
        )

        let overdueCategory = UNNotificationCategory(
            identifier: "OVERDUE_TASK",
            actions: [urgentCompleteAction, rescheduleAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Daily Summary Category
        let openAppAction = UNNotificationAction(
            identifier: "OPEN_APP",
            title: "📱 Open TaskMate",
            options: [.foreground]
        )

        let dailySummaryCategory = UNNotificationCategory(
            identifier: "DAILY_SUMMARY",
            actions: [openAppAction],
            intentIdentifiers: [],
            options: []
        )

        // Register categories
        UNUserNotificationCenter.current().setNotificationCategories([
            taskReminderCategory,
            overdueCategory,
            dailySummaryCategory
        ])

        print("✅ Notification categories configured")
    }

    // MARK: - Authorization
    func requestAuthorization() async {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            isAuthorized = granted
        } catch {
            print("Failed to request notification authorization: \(error)")
        }
    }
    
    private func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Enhanced Task Notifications
    func scheduleTaskReminder(for task: Task, minutesBefore: Int = 30) {
        guard isAuthorized, let dueDate = task.dueDate else {
            print("❌ Cannot schedule notification: not authorized or no due date")
            return
        }

        let reminderDate = Calendar.current.date(
            byAdding: .minute,
            value: -minutesBefore,
            to: dueDate
        ) ?? dueDate

        // Don't schedule notifications for past dates
        guard reminderDate > Date() else {
            print("⚠️ Skipping notification for past date: \(task.title)")
            return
        }

        let content = UNMutableNotificationContent()
        content.title = getNotificationTitle(for: task, minutesBefore: minutesBefore)
        content.body = getNotificationBody(for: task, minutesBefore: minutesBefore)
        content.sound = getNotificationSound(for: task)
        content.badge = 1

        // Add rich content
        content.categoryIdentifier = "TASK_REMINDER"
        content.userInfo = [
            "taskId": task.id.uuidString,
            "taskTitle": task.title,
            "priority": task.priority.rawValue,
            "minutesBefore": minutesBefore
        ]

        // Add task priority and emoji to notification
        content.subtitle = getPrioritySubtitle(for: task)

        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents(
            [.year, .month, .day, .hour, .minute],
            from: reminderDate
        )

        let trigger = UNCalendarNotificationTrigger(
            dateMatching: dateComponents,
            repeats: false
        )

        let request = UNNotificationRequest(
            identifier: "task-\(task.id.uuidString)-\(minutesBefore)",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ Failed to schedule notification: \(error)")
            } else {
                print("✅ Scheduled notification for '\(task.title)' at \(reminderDate)")
            }
        }
    }

    // MARK: - Multiple Reminders for Important Tasks
    func scheduleMultipleReminders(for task: Task) {
        guard task.isImportant || task.priority == .high else {
            // Regular reminder for normal tasks
            scheduleTaskReminder(for: task, minutesBefore: notificationSettings.reminderMinutesBefore)
            return
        }

        // Multiple reminders for important/high priority tasks
        let reminderIntervals = [1440, 60, 15] // 1 day, 1 hour, 15 minutes before

        for interval in reminderIntervals {
            scheduleTaskReminder(for: task, minutesBefore: interval)
        }

        print("✅ Scheduled \(reminderIntervals.count) reminders for important task: \(task.title)")
    }

    // MARK: - Overdue Task Notifications
    func scheduleOverdueNotification(for task: Task) {
        guard isAuthorized, let dueDate = task.dueDate, dueDate < Date() else { return }

        let content = UNMutableNotificationContent()
        content.title = "🚨 Overdue Task"
        content.body = "⚠️ \(task.title) was due \(formatOverdueTime(dueDate))"
        content.subtitle = "Needs immediate attention"
        content.sound = UNNotificationSound(named: UNNotificationSoundName("urgent.wav"))
        content.badge = 1
        content.categoryIdentifier = "OVERDUE_TASK"

        content.userInfo = [
            "taskId": task.id.uuidString,
            "taskTitle": task.title,
            "isOverdue": true
        ]

        // Schedule for immediate delivery
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)

        let request = UNNotificationRequest(
            identifier: "overdue-\(task.id.uuidString)",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ Failed to schedule overdue notification: \(error)")
            } else {
                print("✅ Scheduled overdue notification for: \(task.title)")
            }
        }
    }

    // MARK: - Smart Notifications
    func scheduleSmartReminder(for task: Task) {
        guard let dueDate = task.dueDate else { return }

        let timeUntilDue = dueDate.timeIntervalSinceNow
        let hoursUntilDue = timeUntilDue / 3600

        // Smart scheduling based on time remaining and task properties
        var reminderTimes: [Int] = []

        if hoursUntilDue > 48 { // More than 2 days
            reminderTimes = [1440, 120, 30] // 1 day, 2 hours, 30 minutes
        } else if hoursUntilDue > 24 { // 1-2 days
            reminderTimes = [120, 30] // 2 hours, 30 minutes
        } else if hoursUntilDue > 4 { // 4-24 hours
            reminderTimes = [60, 15] // 1 hour, 15 minutes
        } else if hoursUntilDue > 1 { // 1-4 hours
            reminderTimes = [30, 5] // 30 minutes, 5 minutes
        } else { // Less than 1 hour
            reminderTimes = [15] // 15 minutes only
        }

        // Adjust based on priority and importance
        if task.priority == .high || task.isImportant {
            reminderTimes.append(5) // Extra 5-minute reminder
        }

        for minutesBefore in reminderTimes {
            scheduleTaskReminder(for: task, minutesBefore: minutesBefore)
        }

        print("✅ Scheduled \(reminderTimes.count) smart reminders for: \(task.title)")
    }

    // MARK: - Snooze Functionality
    func snoozeTask(_ taskId: String, minutes: Int = 15) {
        // Cancel existing notifications for this task
        let identifiers = [
            "task-\(taskId)-30",
            "task-\(taskId)-60",
            "task-\(taskId)-15",
            "task-\(taskId)"
        ]

        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)

        // Find the task and schedule new reminder
        if let task = DataManager.shared.tasks.first(where: { $0.id.uuidString == taskId }) {
            let snoozeDate = Date().addingTimeInterval(TimeInterval(minutes * 60))

            let content = UNMutableNotificationContent()
            content.title = "⏰ Snoozed Task Reminder"
            content.body = "🔔 \(task.title) - Snooze time is up!"
            content.sound = .default
            content.badge = 1
            content.categoryIdentifier = "TASK_REMINDER"

            content.userInfo = [
                "taskId": task.id.uuidString,
                "taskTitle": task.title,
                "isSnoozed": true
            ]

            let trigger = UNTimeIntervalNotificationTrigger(
                timeInterval: TimeInterval(minutes * 60),
                repeats: false
            )

            let request = UNNotificationRequest(
                identifier: "snooze-\(taskId)",
                content: content,
                trigger: trigger
            )

            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("❌ Failed to schedule snooze notification: \(error)")
                } else {
                    print("✅ Task snoozed for \(minutes) minutes: \(task.title)")
                }
            }
        }
    }

    // MARK: - Helper Functions
    private func formatOverdueTime(_ dueDate: Date) -> String {
        let timeInterval = Date().timeIntervalSince(dueDate)
        let hours = Int(timeInterval / 3600)
        let days = hours / 24

        if days > 0 {
            return "\(days) day\(days == 1 ? "" : "s") ago"
        } else if hours > 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s") ago"
        } else {
            let minutes = Int(timeInterval / 60)
            return "\(minutes) minute\(minutes == 1 ? "" : "s") ago"
        }
    }

    // MARK: - Notification Action Handlers
    func handleNotificationAction(_ actionIdentifier: String, taskId: String) {
        print("🔔 Handling notification action: \(actionIdentifier) for task: \(taskId)")

        switch actionIdentifier {
        case "COMPLETE_TASK":
            completeTaskFromNotification(taskId)
        case "SNOOZE_TASK":
            snoozeTask(taskId, minutes: 15)
        case "RESCHEDULE_TASK":
            // This will open the app to reschedule
            print("📅 Opening app to reschedule task: \(taskId)")
        case "VIEW_TASK":
            // This will open the app to view task details
            print("👁️ Opening app to view task: \(taskId)")
        case "OPEN_APP":
            print("📱 Opening app from daily summary")
        default:
            print("⚠️ Unknown notification action: \(actionIdentifier)")
        }
    }

    private func completeTaskFromNotification(_ taskId: String) {
        guard let task = DataManager.shared.tasks.first(where: { $0.id.uuidString == taskId }) else {
            print("❌ Task not found: \(taskId)")
            return
        }

        _Concurrency.Task {
            var updatedTask = task
            updatedTask.status = .completed
            updatedTask.completedAt = Date()

            await DataManager.shared.updateTask(updatedTask)

            // Cancel any remaining notifications for this task
            await MainActor.run {
                self.cancelTaskReminder(for: task)
                print("✅ Task completed from notification: \(task.title)")

                // Send success notification
                self.sendTaskCompletionNotification(task.title)
            }
        }
    }

    func sendTaskCompletionNotification(_ taskTitle: String) {
        let content = UNMutableNotificationContent()
        content.title = "🎉 Task Completed!"
        content.body = "Great job! You completed '\(taskTitle)'"
        content.sound = UNNotificationSound(named: UNNotificationSoundName("success.wav"))

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "completion-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request)
    }
    
    func cancelTaskReminder(for task: Task) {
        // Cancel all reminders for this task (multiple intervals)
        let identifiers = [
            "task-\(task.id.uuidString)-30",    // Default reminder
            "task-\(task.id.uuidString)-1440",  // 1 day before
            "task-\(task.id.uuidString)-60",    // 1 hour before
            "task-\(task.id.uuidString)-15",    // 15 minutes before
            "task-\(task.id.uuidString)"        // Legacy format
        ]

        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: identifiers
        )

        print("✅ Cancelled all reminders for task: \(task.title)")
    }

    // MARK: - Notification Content Helpers
    private func getNotificationTitle(for task: Task, minutesBefore: Int) -> String {
        switch minutesBefore {
        case 1440: // 1 day
            return "📅 Task Due Tomorrow"
        case 60: // 1 hour
            return "⏰ Task Due in 1 Hour"
        case 15: // 15 minutes
            return "🚨 Task Due Soon!"
        default:
            return "📋 Task Reminder"
        }
    }

    private func getNotificationBody(for task: Task, minutesBefore: Int) -> String {
        let timeText = getTimeText(minutesBefore: minutesBefore)
        let priorityEmoji = getPriorityEmoji(for: task.priority)

        return "\(priorityEmoji) \(task.title) \(timeText)"
    }

    private func getPrioritySubtitle(for task: Task) -> String {
        let priorityText = task.priority.displayName
        let importantText = task.isImportant ? " ⭐" : ""
        return "\(priorityText) Priority\(importantText)"
    }

    private func getPriorityEmoji(for priority: Task.Priority) -> String {
        switch priority {
        case .high: return "🔴"
        case .medium: return "🟡"
        case .low: return "🟢"
        }
    }

    private func getTimeText(minutesBefore: Int) -> String {
        switch minutesBefore {
        case 1440: return "is due tomorrow"
        case 60: return "is due in 1 hour"
        case 15: return "is due in 15 minutes"
        case 0: return "is due now!"
        default: return "is due in \(minutesBefore) minutes"
        }
    }

    private func getNotificationSound(for task: Task) -> UNNotificationSound {
        switch task.priority {
        case .high:
            return UNNotificationSound(named: UNNotificationSoundName("high_priority.wav"))
        case .medium:
            return .default
        case .low:
            return UNNotificationSound(named: UNNotificationSoundName("gentle.wav"))
        }
    }
    
    // MARK: - Group Notifications
    func scheduleGroupActivityNotification(groupName: String, activity: String) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Group Activity"
        content.body = "\(activity) in \(groupName)"
        content.sound = .default
        content.badge = 1
        
        // Schedule for immediate delivery
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "group-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Enhanced Daily Summary
    func scheduleDailySummary() {
        guard isAuthorized, notificationSettings.dailySummaryEnabled else { return }

        let tasks = DataManager.shared.tasks
        let todayTasks = tasks.filter { Calendar.current.isDateInToday($0.dueDate ?? Date.distantPast) }
        let completedToday = todayTasks.filter { $0.status == .completed }.count
        let pendingToday = todayTasks.filter { $0.status != .completed }.count
        let overdueCount = tasks.filter { $0.isOverdue && $0.status != .completed }.count

        let content = UNMutableNotificationContent()
        content.title = "📊 Daily Summary"
        content.body = generateDailySummaryText(completed: completedToday, pending: pendingToday, overdue: overdueCount)
        content.sound = .default
        content.categoryIdentifier = "DAILY_SUMMARY"

        content.userInfo = [
            "type": "daily_summary",
            "completed": completedToday,
            "pending": pendingToday,
            "overdue": overdueCount
        ]

        // Schedule for the time set in settings
        var dateComponents = DateComponents()
        dateComponents.hour = notificationSettings.dailySummaryTime.hour
        dateComponents.minute = notificationSettings.dailySummaryTime.minute

        let trigger = UNCalendarNotificationTrigger(
            dateMatching: dateComponents,
            repeats: true
        )

        let request = UNNotificationRequest(
            identifier: "daily-summary",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ Failed to schedule daily summary: \(error)")
            } else {
                print("✅ Daily summary scheduled for \(dateComponents.hour ?? 9):\(String(format: "%02d", dateComponents.minute ?? 0))")
            }
        }
    }

    private func generateDailySummaryText(completed: Int, pending: Int, overdue: Int) -> String {
        var summaryParts: [String] = []

        if completed > 0 {
            summaryParts.append("✅ \(completed) completed")
        }

        if pending > 0 {
            summaryParts.append("📋 \(pending) pending")
        }

        if overdue > 0 {
            summaryParts.append("🚨 \(overdue) overdue")
        }

        if summaryParts.isEmpty {
            return "🎉 All caught up! No tasks for today."
        } else {
            return summaryParts.joined(separator: " • ")
        }
    }
    
    func cancelDailySummary() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: ["daily-summary"]
        )
    }
    
    // MARK: - Motivational Notifications
    func scheduleMotivationalMessage() {
        guard isAuthorized, notificationSettings.motivationalMessagesEnabled else { return }
        
        let messages = [
            "You're doing great! Keep up the momentum! 🌟",
            "Every small step counts towards your goals! 🚀",
            "Believe in yourself - you've got this! 💪",
            "Progress, not perfection. You're on the right track! ✨",
            "Your future self will thank you for today's efforts! 🌅"
        ]
        
        let content = UNMutableNotificationContent()
        content.title = "Stay Motivated!"
        content.body = messages.randomElement() ?? "Keep pushing forward!"
        content.sound = .default
        
        // Schedule randomly between 1-4 hours from now
        let randomInterval = Double.random(in: 3600...14400) // 1-4 hours
        let trigger = UNTimeIntervalNotificationTrigger(
            timeInterval: randomInterval,
            repeats: false
        )
        
        let request = UNNotificationRequest(
            identifier: "motivation-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Settings Management
    func updateNotificationSettings(_ settings: NotificationSettings) {
        notificationSettings = settings
        
        // Update scheduled notifications based on new settings
        if settings.dailySummaryEnabled {
            scheduleDailySummary()
        } else {
            cancelDailySummary()
        }
    }
    
    // MARK: - Badge Management
    func updateBadgeCount(_ count: Int) {
        UNUserNotificationCenter.current().setBadgeCount(count)
    }
    
    func clearBadge() {
        updateBadgeCount(0)
    }

    // MARK: - Quiet Hours
    func isInQuietHours() -> Bool {
        guard notificationSettings.quietHoursEnabled else { return false }

        let now = Date()
        let calendar = Calendar.current
        let currentHour = calendar.component(.hour, from: now)
        let currentMinute = calendar.component(.minute, from: now)

        let startHour = notificationSettings.quietHoursStart.hour
        let startMinute = notificationSettings.quietHoursStart.minute
        let endHour = notificationSettings.quietHoursEnd.hour
        let endMinute = notificationSettings.quietHoursEnd.minute

        let currentMinutes = currentHour * 60 + currentMinute
        let startMinutes = startHour * 60 + startMinute
        let endMinutes = endHour * 60 + endMinute

        if startMinutes <= endMinutes {
            // Same day quiet hours (e.g., 22:00 to 23:00)
            return currentMinutes >= startMinutes && currentMinutes <= endMinutes
        } else {
            // Overnight quiet hours (e.g., 22:00 to 08:00)
            return currentMinutes >= startMinutes || currentMinutes <= endMinutes
        }
    }

    // MARK: - Notification Analytics
    func getNotificationStats() -> NotificationStats {
        let tasks = DataManager.shared.tasks
        let totalTasks = tasks.count
        let tasksWithReminders = tasks.filter { $0.dueDate != nil }.count
        let overdueTasks = tasks.filter { $0.isOverdue && $0.status != .completed }.count
        let completedToday = tasks.filter {
            $0.status == .completed &&
            Calendar.current.isDateInToday($0.completedAt ?? Date.distantPast)
        }.count

        return NotificationStats(
            totalTasks: totalTasks,
            tasksWithReminders: tasksWithReminders,
            overdueTasks: overdueTasks,
            completedToday: completedToday,
            notificationsEnabled: isAuthorized
        )
    }
}

// MARK: - Enhanced Notification Settings
struct NotificationSettings: Codable {
    var taskRemindersEnabled = true
    var groupNotificationsEnabled = true
    var dailySummaryEnabled = true
    var motivationalMessagesEnabled = true
    var overdueNotificationsEnabled = true
    var smartRemindersEnabled = true
    var completionCelebrationEnabled = true

    var dailySummaryTime = NotificationTime(hour: 9, minute: 0) // 9:00 AM
    var reminderMinutesBefore = 30
    var snoozeMinutes = 15
    var maxRemindersPerTask = 3

    // Sound preferences
    var useCustomSounds = true
    var quietHoursEnabled = false
    var quietHoursStart = NotificationTime(hour: 22, minute: 0) // 10:00 PM
    var quietHoursEnd = NotificationTime(hour: 8, minute: 0)   // 8:00 AM
    
    struct NotificationTime: Codable {
        var hour: Int
        var minute: Int
        
        var displayString: String {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            
            var components = DateComponents()
            components.hour = hour
            components.minute = minute
            
            if let date = Calendar.current.date(from: components) {
                return formatter.string(from: date)
            }
            return "\(hour):\(String(format: "%02d", minute))"
        }
    }
}

// MARK: - Notification Extensions
extension NotificationManager {
    func scheduleAllTaskReminders() {
        let tasks = DataManager.shared.tasks.filter { task in
            task.status != .completed && task.dueDate != nil
        }

        for task in tasks {
            if notificationSettings.smartRemindersEnabled {
                scheduleSmartReminder(for: task)
            } else {
                scheduleTaskReminder(for: task, minutesBefore: notificationSettings.reminderMinutesBefore)
            }
        }

        print("✅ Scheduled reminders for \(tasks.count) tasks")
    }

    func cancelAllTaskReminders() {
        let tasks = DataManager.shared.tasks
        for task in tasks {
            cancelTaskReminder(for: task)
        }

        print("✅ Cancelled all task reminders")
    }

    func scheduleOverdueTasksCheck() {
        let overdueTasks = DataManager.shared.tasks.filter {
            $0.isOverdue && $0.status != .completed
        }

        for task in overdueTasks {
            if notificationSettings.overdueNotificationsEnabled {
                scheduleOverdueNotification(for: task)
            }
        }

        print("✅ Scheduled overdue notifications for \(overdueTasks.count) tasks")
    }

    // MARK: - Group Notifications
    func scheduleGroupTaskReminder(for task: GroupTask) {
        guard isAuthorized, let dueDate = task.dueDate else {
            print("❌ Cannot schedule group task notification: not authorized or no due date")
            return
        }

        let reminderDate = Calendar.current.date(
            byAdding: .minute,
            value: -notificationSettings.reminderMinutesBefore,
            to: dueDate
        ) ?? dueDate

        // Don't schedule notifications for past dates
        guard reminderDate > Date() else {
            print("⚠️ Skipping group task notification for past date: \(task.title)")
            return
        }

        let content = UNMutableNotificationContent()
        content.title = "📋 Group Task Reminder"
        content.body = getGroupTaskNotificationBody(for: task)
        content.subtitle = task.assignmentInfo
        content.sound = getNotificationSound(for: Task(title: task.title, priority: Task.Priority(rawValue: task.priority.rawValue) ?? .medium, createdByUserId: task.createdById))
        content.badge = 1
        content.categoryIdentifier = "GROUP_TASK_REMINDER"

        content.userInfo = [
            "taskId": task.id.uuidString,
            "groupId": task.groupId.uuidString,
            "taskTitle": task.title,
            "taskType": task.taskType.isGroupTask ? "group" : "individual"
        ]

        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents(
            [.year, .month, .day, .hour, .minute],
            from: reminderDate
        )

        let trigger = UNCalendarNotificationTrigger(
            dateMatching: dateComponents,
            repeats: false
        )

        let request = UNNotificationRequest(
            identifier: "group-task-\(task.id.uuidString)",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ Failed to schedule group task notification: \(error)")
            } else {
                print("✅ Scheduled group task notification for '\(task.title)' at \(reminderDate)")
            }
        }
    }

    func cancelGroupTaskReminder(for task: GroupTask) {
        let identifier = "group-task-\(task.id.uuidString)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: [identifier]
        )
        print("✅ Cancelled group task reminder for: \(task.title)")
    }





    private func getGroupTaskNotificationBody(for task: GroupTask) -> String {
        let priorityEmoji = getPriorityEmoji(for: Task.Priority(rawValue: task.priority.rawValue) ?? .medium)

        switch task.taskType {
        case .groupTask:
            let progress = task.groupProgress
            return "\(priorityEmoji) \(task.title) - Group Task (\(progress) completed)"
        case .individualTask:
            return "\(priorityEmoji) \(task.title) - Assigned to you"
        }
    }
}

// MARK: - Notification Stats
struct NotificationStats {
    let totalTasks: Int
    let tasksWithReminders: Int
    let overdueTasks: Int
    let completedToday: Int
    let notificationsEnabled: Bool

    var reminderCoverage: Double {
        guard totalTasks > 0 else { return 0 }
        return Double(tasksWithReminders) / Double(totalTasks)
    }

    var completionRate: Double {
        guard totalTasks > 0 else { return 0 }
        let completedTasks = totalTasks - overdueTasks
        return Double(completedTasks) / Double(totalTasks)
    }
}
