//
//  NetworkMonitor.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Network connectivity monitoring for offline mode
//

import Foundation
import Network
import SwiftUI

// MARK: - Network Monitor
@MainActor
class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var connectionType: ConnectionType = .unknown
    @Published var isExpensive = false
    
    // MARK: - Private Properties
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    // MARK: - Connection Types
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown
        
        var description: String {
            switch self {
            case .wifi: return "Wi-Fi"
            case .cellular: return "Cellular"
            case .ethernet: return "Ethernet"
            case .unknown: return "Unknown"
            }
        }
        
        var icon: String {
            switch self {
            case .wifi: return "wifi"
            case .cellular: return "antenna.radiowaves.left.and.right"
            case .ethernet: return "cable.connector"
            case .unknown: return "questionmark.circle"
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        print("🌐 NetworkMonitor initialized")
        startMonitoring()
    }
    
    // MARK: - Monitoring
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateConnectionStatus(path)
            }
        }
        
        monitor.start(queue: queue)
        print("🔄 Network monitoring started")
    }
    
    private func updateConnectionStatus(_ path: NWPath) {
        let wasConnected = isConnected
        isConnected = path.status == .satisfied
        isExpensive = path.isExpensive
        
        // Determine connection type
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unknown
        }
        
        // Log connection changes
        if wasConnected != isConnected {
            if isConnected {
                print("✅ Network connected: \(connectionType.description)")
                NotificationCenter.default.post(name: .networkConnected, object: nil)
            } else {
                print("❌ Network disconnected")
                NotificationCenter.default.post(name: .networkDisconnected, object: nil)
            }
        }
    }
    
    // MARK: - Public Methods
    
    /// فحص الاتصال يدوياً
    func checkConnection() {
        print("🔍 Manual connection check requested")
        // The monitor will automatically update the status
    }
    
    /// إيقاف المراقبة
    func stopMonitoring() {
        monitor.cancel()
        print("⏹️ Network monitoring stopped")
    }
    
    /// الحصول على معلومات الاتصال
    func getConnectionInfo() -> String {
        if isConnected {
            var info = "Connected via \(connectionType.description)"
            if isExpensive {
                info += " (Expensive)"
            }
            return info
        } else {
            return "No connection"
        }
    }
    
    /// التحقق من إمكانية استخدام البيانات
    var canUseData: Bool {
        return isConnected && (!isExpensive || connectionType == .wifi)
    }
    
    /// التحقق من إمكانية المزامنة
    var canSync: Bool {
        return isConnected
    }
    
    deinit {
        monitor.cancel()
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let networkConnected = Notification.Name("networkConnected")
    static let networkDisconnected = Notification.Name("networkDisconnected")
}

// MARK: - Network Status View
struct NetworkStatusView: View {
    @StateObject private var networkMonitor = NetworkMonitor.shared
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: networkMonitor.isConnected ? 
                  networkMonitor.connectionType.icon : "wifi.slash")
                .foregroundColor(networkMonitor.isConnected ? .green : .red)
            
            Text(networkMonitor.getConnectionInfo())
                .font(.caption)
                .foregroundColor(networkMonitor.isConnected ? 
                               DesignSystem.Colors.textSecondary : .red)
            
            if networkMonitor.isExpensive {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Offline Message View
struct OfflineMessageView: View {
    let icon: String
    let title: String
    let message: String
    let retryAction: () -> Void
    
    @StateObject private var networkMonitor = NetworkMonitor.shared
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Icon
            Image(systemName: icon)
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            // Title and Message
            VStack(spacing: 12) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            // Network Status
            NetworkStatusView()
            
            // Retry Button
            Button(action: retryAction) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("Try Again")
                }
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
            }
            .disabled(!networkMonitor.isConnected)
            .opacity(networkMonitor.isConnected ? 1.0 : 0.6)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Network-Aware View Modifier
struct NetworkAware: ViewModifier {
    let offlineContent: () -> AnyView
    
    @StateObject private var networkMonitor = NetworkMonitor.shared
    
    func body(content: Content) -> some View {
        if networkMonitor.isConnected {
            content
        } else {
            offlineContent()
        }
    }
}

extension View {
    func networkAware<OfflineContent: View>(
        @ViewBuilder offlineContent: @escaping () -> OfflineContent
    ) -> some View {
        self.modifier(NetworkAware(offlineContent: { AnyView(offlineContent()) }))
    }
}

// MARK: - Preview
struct NetworkStatusView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            NetworkStatusView()
            
            OfflineMessageView(
                icon: "wifi.slash",
                title: "No Internet Connection",
                message: "This feature requires internet connection. Please check your connection and try again.",
                retryAction: {
                    NetworkMonitor.shared.checkConnection()
                }
            )
        }
        .padding()
    }
}
