//
//  UserInfoManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation
import SwiftUI

// MARK: - Unified User Info Model
struct UnifiedUserInfo: Identifiable, Codable, Hashable {
    let id: UUID
    let username: String
    let email: String
    var fullName: String?
    var avatarURL: String?
    var bio: String?
    var points: Int
    var level: String
    var isOnline: Bool
    var lastSeen: Date?
    let createdAt: Date
    var updatedAt: Date
    
    // MARK: - Computed Properties
    
    /// الاسم المعروض (الاسم الكامل أو اسم المستخدم)
    var displayName: String {
        return fullName?.isEmpty == false ? fullName! : username
    }
    
    /// الأحرف الأولى للاسم
    var initials: String {
        if let fullName = fullName, !fullName.isEmpty {
            let components = fullName.split(separator: " ")
            if components.count >= 2 {
                return String(components[0].prefix(1) + components[1].prefix(1)).uppercased()
            } else {
                return String(fullName.prefix(2)).uppercased()
            }
        } else {
            return String(username.prefix(2)).uppercased()
        }
    }
    
    /// لون الخلفية للأفاتار
    var avatarBackgroundColor: LinearGradient {
        let colors = [
            [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
            [Color.blue, Color.purple],
            [Color.green, Color.teal],
            [Color.orange, Color.red],
            [Color.pink, Color.purple]
        ]
        
        let index = abs(username.hashValue) % colors.count
        return LinearGradient(
            colors: colors[index],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Initializers
    
    init(id: UUID = UUID(), username: String, email: String, fullName: String? = nil, avatarURL: String? = nil) {
        self.id = id
        self.username = username
        self.email = email
        self.fullName = fullName
        self.avatarURL = avatarURL
        self.bio = nil
        self.points = 0
        self.level = "Beginner"
        self.isOnline = false
        self.lastSeen = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // MARK: - Sample Data
    static func sampleUsers() -> [UnifiedUserInfo] {
        [
            UnifiedUserInfo(
                username: "ahmed_dev",
                email: "<EMAIL>",
                fullName: "Ahmed Mohammed",
                avatarURL: nil
            ),
            UnifiedUserInfo(
                username: "sara_designer",
                email: "<EMAIL>",
                fullName: "Sara Ali",
                avatarURL: nil
            ),
            UnifiedUserInfo(
                username: "omar_pm",
                email: "<EMAIL>",
                fullName: "Omar Hassan",
                avatarURL: nil
            )
        ]
    }
}

// MARK: - User Info Manager (Unified System)
@MainActor
class UserInfoManager: ObservableObject {
    static let shared = UserInfoManager()
    
    // MARK: - Published Properties
    @Published var users: [UUID: UnifiedUserInfo] = [:]
    @Published var avatarImages: [String: UIImage] = [:]
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var ongoingUserRequests: Set<UUID> = []
    private var ongoingImageRequests: Set<String> = []
    
    private init() {
        print("🚀 UserInfoManager initialized - Unified user system")
        loadSampleData() // Temporary for development
    }
    
    // MARK: - Public Methods
    
    /// جلب معلومات مستخدم واحد
    func getUserInfo(_ userId: UUID) async -> UnifiedUserInfo? {
        // التحقق من الذاكرة أولاً
        if let cachedUser = users[userId] {
            print("📱 Using cached user info for: \(cachedUser.username)")
            return cachedUser
        }
        
        // تجنب الطلبات المتكررة
        if ongoingUserRequests.contains(userId) {
            while ongoingUserRequests.contains(userId) {
                try? await _Concurrency.Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            }
            return users[userId]
        }
        
        ongoingUserRequests.insert(userId)
        
        do {
            // جلب من قاعدة البيانات
            if let userProfile = try await SupabaseManager.shared.fetchUserProfile(userId: userId) {
                let userInfo = UnifiedUserInfo(
                    id: userProfile.id,
                    username: userProfile.username,
                    email: userProfile.email,
                    fullName: userProfile.displayName,
                    avatarURL: userProfile.avatarUrl
                )
                
                await MainActor.run {
                    self.users[userId] = userInfo
                    print("✅ Cached user info for: \(userInfo.username)")
                }
                
                ongoingUserRequests.remove(userId)
                return userInfo
            }
        } catch {
            print("❌ Failed to fetch user info: \(error)")
            await MainActor.run {
                self.setError("Failed to load user info: \(error.localizedDescription)")
            }
        }
        
        ongoingUserRequests.remove(userId)
        return nil
    }
    
    /// جلب معلومات عدة مستخدمين
    func getUsersInfo(_ userIds: [UUID]) async -> [UnifiedUserInfo] {
        var result: [UnifiedUserInfo] = []
        var missingIds: [UUID] = []
        
        // التحقق من الذاكرة أولاً
        for userId in userIds {
            if let cachedUser = users[userId] {
                result.append(cachedUser)
            } else {
                missingIds.append(userId)
            }
        }
        
        // جلب المستخدمين المفقودين
        if !missingIds.isEmpty {
            do {
                let userProfiles = try await SupabaseManager.shared.fetchUserProfiles(userIds: missingIds)
                
                await MainActor.run {
                    for profile in userProfiles {
                        let userInfo = UnifiedUserInfo(
                            id: profile.id,
                            username: profile.username,
                            email: profile.email,
                            fullName: profile.displayName,
                            avatarURL: profile.avatarUrl
                        )
                        self.users[profile.id] = userInfo
                        result.append(userInfo)
                    }
                    print("✅ Cached \(userProfiles.count) new users")
                }
            } catch {
                print("❌ Failed to fetch users info: \(error)")
                await MainActor.run {
                    self.setError("Failed to load users info: \(error.localizedDescription)")
                }
            }
        }
        
        return result
    }
    
    /// جلب صورة المستخدم
    func getUserAvatar(_ avatarURL: String?) async -> UIImage? {
        guard let avatarURL = avatarURL, !avatarURL.isEmpty else { return nil }
        
        // التحقق من الذاكرة أولاً
        if let cachedImage = avatarImages[avatarURL] {
            print("📱 Using cached avatar for URL: \(avatarURL)")
            return cachedImage
        }
        
        // تجنب الطلبات المتكررة
        if ongoingImageRequests.contains(avatarURL) {
            while ongoingImageRequests.contains(avatarURL) {
                try? await _Concurrency.Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            }
            return avatarImages[avatarURL]
        }
        
        ongoingImageRequests.insert(avatarURL)
        
        do {
            guard let url = URL(string: avatarURL) else {
                ongoingImageRequests.remove(avatarURL)
                return nil
            }
            
            let (data, _) = try await URLSession.shared.data(from: url)
            
            if let image = UIImage(data: data) {
                await MainActor.run {
                    self.avatarImages[avatarURL] = image
                    print("✅ Cached avatar for URL: \(avatarURL)")
                }
                ongoingImageRequests.remove(avatarURL)
                return image
            }
        } catch {
            print("❌ Failed to load avatar: \(error)")
        }
        
        ongoingImageRequests.remove(avatarURL)
        return nil
    }
    
    /// تحديث معلومات مستخدم
    func updateUserInfo(_ userInfo: UnifiedUserInfo) {
        users[userInfo.id] = userInfo
        print("🔄 Updated user info for: \(userInfo.username)")
    }
    
    /// الحصول على اسم المستخدم المعروض
    func getDisplayName(_ userId: UUID) -> String {
        return users[userId]?.displayName ?? "Unknown User"
    }
    
    /// الحصول على اسم المستخدم
    func getUsername(_ userId: UUID) -> String {
        return users[userId]?.username ?? "unknown"
    }
    
    /// الحصول على الأحرف الأولى
    func getInitials(_ userId: UUID) -> String {
        return users[userId]?.initials ?? "??"
    }
    
    /// الحصول على لون الخلفية
    func getAvatarBackgroundColor(_ userId: UUID) -> LinearGradient {
        return users[userId]?.avatarBackgroundColor ?? LinearGradient(
            colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Private Methods
    
    private func setError(_ message: String) {
        errorMessage = message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.errorMessage = nil
        }
    }
    
    private func loadSampleData() {
        // بيانات نموذجية للتطوير
        let sampleUsers = UnifiedUserInfo.sampleUsers()
        for user in sampleUsers {
            users[user.id] = user
        }
        print("📝 Loaded \(sampleUsers.count) sample users")
    }
}
