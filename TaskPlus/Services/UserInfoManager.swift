//
//  UserInfoManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation
import SwiftUI

// MARK: - User Info Manager (Unified System)
@MainActor
class UserInfoManager: ObservableObject {
    static let shared = UserInfoManager()
    
    // MARK: - Published Properties
    @Published var users: [UUID: UnifiedUserInfo] = [:]
    @Published var avatarImages: [String: UIImage] = [:]
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var ongoingUserRequests: Set<UUID> = []
    private var ongoingImageRequests: Set<String> = []
    private var lastCacheUpdate: Date = Date()
    private let cacheExpirationTime: TimeInterval = 300 // 5 minutes
    
    private init() {
        print("🚀 UserInfoManager initialized - Unified user system")
        loadSampleData() // Temporary for development
    }
    
    // MARK: - Public Methods
    
    /// جلب معلومات مستخدم واحد
    func getUserInfo(_ userId: UUID) async -> UnifiedUserInfo? {
        // التحقق من الذاكرة أولاً
        if let cachedUser = users[userId] {
            print("📱 Using cached user info for: \(cachedUser.username)")
            return cachedUser
        }
        
        // تجنب الطلبات المتكررة
        if ongoingUserRequests.contains(userId) {
            while ongoingUserRequests.contains(userId) {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            }
            return users[userId]
        }
        
        ongoingUserRequests.insert(userId)
        
        do {
            // جلب من قاعدة البيانات
            if let userProfile = try await SupabaseManager.shared.fetchUserProfile(userId: userId) {
                let userInfo = UserInfo(
                    id: userProfile.id,
                    username: userProfile.username,
                    email: userProfile.email,
                    fullName: userProfile.fullName,
                    avatarURL: userProfile.avatarUrl
                )
                
                await MainActor.run {
                    self.users[userId] = userInfo
                    print("✅ Cached user info for: \(userInfo.username)")
                }
                
                ongoingUserRequests.remove(userId)
                return userInfo
            }
        } catch {
            print("❌ Failed to fetch user info: \(error)")
            await MainActor.run {
                self.setError("Failed to load user info: \(error.localizedDescription)")
            }
        }
        
        ongoingUserRequests.remove(userId)
        return nil
    }
    
    /// جلب معلومات عدة مستخدمين
    func getUsersInfo(_ userIds: [UUID]) async -> [UserInfo] {
        var result: [UserInfo] = []
        var missingIds: [UUID] = []
        
        // التحقق من الذاكرة أولاً
        for userId in userIds {
            if let cachedUser = users[userId] {
                result.append(cachedUser)
            } else {
                missingIds.append(userId)
            }
        }
        
        // جلب المستخدمين المفقودين
        if !missingIds.isEmpty {
            do {
                let userProfiles = try await SupabaseManager.shared.fetchUserProfiles(userIds: missingIds)
                
                await MainActor.run {
                    for profile in userProfiles {
                        let userInfo = UserInfo(
                            id: profile.id,
                            username: profile.username,
                            email: profile.email,
                            fullName: profile.fullName,
                            avatarURL: profile.avatarUrl
                        )
                        self.users[profile.id] = userInfo
                        result.append(userInfo)
                    }
                    print("✅ Cached \(userProfiles.count) new users")
                }
            } catch {
                print("❌ Failed to fetch users info: \(error)")
                await MainActor.run {
                    self.setError("Failed to load users info: \(error.localizedDescription)")
                }
            }
        }
        
        return result
    }
    
    /// جلب صورة المستخدم
    func getUserAvatar(_ avatarURL: String?) async -> UIImage? {
        guard let avatarURL = avatarURL, !avatarURL.isEmpty else { return nil }
        
        // التحقق من الذاكرة أولاً
        if let cachedImage = avatarImages[avatarURL] {
            print("📱 Using cached avatar for URL: \(avatarURL)")
            return cachedImage
        }
        
        // تجنب الطلبات المتكررة
        if ongoingImageRequests.contains(avatarURL) {
            while ongoingImageRequests.contains(avatarURL) {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            }
            return avatarImages[avatarURL]
        }
        
        ongoingImageRequests.insert(avatarURL)
        
        do {
            guard let url = URL(string: avatarURL) else {
                ongoingImageRequests.remove(avatarURL)
                return nil
            }
            
            let (data, _) = try await URLSession.shared.data(from: url)
            
            if let image = UIImage(data: data) {
                await MainActor.run {
                    self.avatarImages[avatarURL] = image
                    print("✅ Cached avatar for URL: \(avatarURL)")
                }
                ongoingImageRequests.remove(avatarURL)
                return image
            }
        } catch {
            print("❌ Failed to load avatar: \(error)")
        }
        
        ongoingImageRequests.remove(avatarURL)
        return nil
    }
    
    /// البحث عن المستخدمين
    func searchUsers(_ query: String) async -> [UserInfo] {
        guard !query.isEmpty else { return [] }
        
        do {
            let userProfiles = try await SupabaseManager.shared.searchUsers(query: query)
            
            var result: [UserInfo] = []
            
            await MainActor.run {
                for profile in userProfiles {
                    let userInfo = UserInfo(
                        id: profile.id,
                        username: profile.username,
                        email: profile.email,
                        fullName: profile.fullName,
                        avatarURL: profile.avatarUrl
                    )
                    self.users[profile.id] = userInfo
                    result.append(userInfo)
                }
                print("🔍 Found \(result.count) users for query: \(query)")
            }
            
            return result
        } catch {
            print("❌ Failed to search users: \(error)")
            await MainActor.run {
                self.setError("Failed to search users: \(error.localizedDescription)")
            }
            return []
        }
    }
    
    /// تحديث معلومات مستخدم
    func updateUserInfo(_ userInfo: UserInfo) {
        users[userInfo.id] = userInfo
        print("🔄 Updated user info for: \(userInfo.username)")
    }
    
    /// إزالة مستخدم من الذاكرة
    func removeUserInfo(_ userId: UUID) {
        users.removeValue(forKey: userId)
        print("🗑️ Removed user info for: \(userId)")
    }
    
    /// مسح جميع البيانات
    func clearCache() {
        users.removeAll()
        avatarImages.removeAll()
        ongoingUserRequests.removeAll()
        ongoingImageRequests.removeAll()
        print("🧹 Cleared all user cache")
    }
    
    /// تحديث الذاكرة إذا انتهت صلاحيتها
    func refreshCacheIfNeeded() async {
        let timeSinceLastUpdate = Date().timeIntervalSince(lastCacheUpdate)
        
        if timeSinceLastUpdate > cacheExpirationTime {
            print("🔄 Cache expired, refreshing...")
            
            let userIds = Array(users.keys)
            if !userIds.isEmpty {
                _ = await getUsersInfo(userIds)
            }
            
            lastCacheUpdate = Date()
        }
    }
    
    // MARK: - Helper Methods
    
    /// الحصول على اسم المستخدم المعروض
    func getDisplayName(_ userId: UUID) -> String {
        return users[userId]?.displayName ?? "Unknown User"
    }
    
    /// الحصول على اسم المستخدم
    func getUsername(_ userId: UUID) -> String {
        return users[userId]?.username ?? "unknown"
    }
    
    /// الحصول على الأحرف الأولى
    func getInitials(_ userId: UUID) -> String {
        return users[userId]?.initials ?? "??"
    }
    
    /// الحصول على لون الخلفية
    func getAvatarBackgroundColor(_ userId: UUID) -> LinearGradient {
        return users[userId]?.avatarBackgroundColor ?? LinearGradient(
            colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Private Methods
    
    private func setError(_ message: String) {
        errorMessage = message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.errorMessage = nil
        }
    }
    
    private func loadSampleData() {
        // بيانات نموذجية للتطوير
        let sampleUsers = UserInfo.sampleUsers()
        for user in sampleUsers {
            users[user.id] = user
        }
        print("📝 Loaded \(sampleUsers.count) sample users")
    }
    
    // MARK: - Statistics
    
    var cacheStats: (users: Int, images: Int, ongoingRequests: Int) {
        return (
            users: users.count,
            images: avatarImages.count,
            ongoingRequests: ongoingUserRequests.count + ongoingImageRequests.count
        )
    }
    
    func printCacheStats() {
        let stats = cacheStats
        print("📊 UserInfoManager Stats:")
        print("  - Cached Users: \(stats.users)")
        print("  - Cached Images: \(stats.images)")
        print("  - Ongoing Requests: \(stats.ongoingRequests)")
    }
}

// MARK: - User Avatar Component (Unified)
struct UserAvatarView: View {
    let userId: UUID
    let size: CGFloat
    let showOnlineStatus: Bool

    @StateObject private var userInfoManager = UserInfoManager.shared
    @State private var userInfo: UserInfo?
    @State private var avatarImage: UIImage?
    @State private var isLoading = true

    init(userId: UUID, size: CGFloat = 40, showOnlineStatus: Bool = false) {
        self.userId = userId
        self.size = size
        self.showOnlineStatus = showOnlineStatus
    }

    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(userInfo?.avatarBackgroundColor ?? LinearGradient(
                    colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: size, height: size)

            // Avatar content
            Group {
                if let avatarImage = avatarImage {
                    // User's actual image
                    Image(uiImage: avatarImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: size, height: size)
                        .clipShape(Circle())
                } else if let userInfo = userInfo {
                    // User initials
                    Text(userInfo.initials)
                        .font(.system(size: size * 0.4, weight: .semibold))
                        .foregroundColor(.white)
                } else if isLoading {
                    // Loading state
                    ProgressView()
                        .scaleEffect(0.7)
                        .foregroundColor(.white)
                } else {
                    // Fallback
                    Image(systemName: "person.fill")
                        .font(.system(size: size * 0.5))
                        .foregroundColor(.white)
                }
            }

            // Online status indicator
            if showOnlineStatus, let userInfo = userInfo {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(userInfo.connectionStatus.color)
                            .frame(width: size * 0.25, height: size * 0.25)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 2)
                            )
                            .offset(x: -2, y: -2)
                    }
                }
            }
        }
        .task {
            await loadUserData()
        }
        .onChange(of: userId) { _ in
            Task {
                await loadUserData()
            }
        }
    }

    private func loadUserData() async {
        isLoading = true

        // Load user info
        if let info = await userInfoManager.getUserInfo(userId) {
            await MainActor.run {
                self.userInfo = info
            }

            // Load avatar image if available
            if let image = await userInfoManager.getUserAvatar(info.avatarURL) {
                await MainActor.run {
                    self.avatarImage = image
                }
            }
        }

        await MainActor.run {
            self.isLoading = false
        }
    }
}
