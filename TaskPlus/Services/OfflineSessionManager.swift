//
//  OfflineSessionManager.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//  Offline Session Management for Authentication and App State
//

import Foundation
import SwiftUI
import LocalAuthentication
import Combine

// MARK: - Session State
enum SessionState {
    case unauthenticated
    case authenticating
    case authenticated(User)
    case offline(User)
    case expired
    case locked
    
    var isAuthenticated: Bool {
        switch self {
        case .authenticated, .offline:
            return true
        default:
            return false
        }
    }
    
    var user: User? {
        switch self {
        case .authenticated(let user), .offline(let user):
            return user
        default:
            return nil
        }
    }
    
    var description: String {
        switch self {
        case .unauthenticated: return "Not signed in"
        case .authenticating: return "Signing in..."
        case .authenticated: return "Online"
        case .offline: return "Offline"
        case .expired: return "Session expired"
        case .locked: return "Locked"
        }
    }
    
    var icon: String {
        switch self {
        case .unauthenticated: return "person.slash"
        case .authenticating: return "person.badge.clock"
        case .authenticated: return "person.check"
        case .offline: return "person.slash.fill"
        case .expired: return "clock.badge.exclamationmark"
        case .locked: return "lock.fill"
        }
    }
}

// MARK: - Offline Session Manager
@MainActor
class OfflineSessionManager: ObservableObject {
    static let shared = OfflineSessionManager()
    
    // MARK: - Published Properties
    @Published var sessionState: SessionState = .unauthenticated
    @Published var isOfflineMode = false
    @Published var lastOnlineTime: Date?
    @Published var sessionExpiryTime: Date?
    @Published var biometricAuthEnabled = false
    @Published var autoLockEnabled = true
    @Published var autoLockTimeout: TimeInterval = 300 // 5 minutes
    
    // MARK: - Private Properties
    private let networkMonitor = NetworkMonitor.shared
    private let supabaseManager = SupabaseManager.shared
    private var cancellables = Set<AnyCancellable>()
    private var sessionTimer: Timer?
    private var autoLockTimer: Timer?
    private var lastActivityTime = Date()
    
    // MARK: - Session Configuration
    private let offlineSessionDuration: TimeInterval = 86400 * 7 // 7 days
    private let sessionRefreshInterval: TimeInterval = 3600 // 1 hour
    
    // MARK: - Keychain Keys
    private let keychainService = "com.taskplus.session"
    private let userDataKey = "cached_user_data"
    private let sessionTokenKey = "session_token"
    private let biometricKey = "biometric_enabled"
    
    // MARK: - Initialization
    private init() {
        print("🔐 OfflineSessionManager initialized")
        setupNetworkMonitoring()
        setupActivityMonitoring()
        loadPersistedSession()
        loadBiometricSettings()
    }
    
    // MARK: - Network Monitoring
    private func setupNetworkMonitoring() {
        networkMonitor.$isConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.handleNetworkChange(isConnected)
            }
            .store(in: &cancellables)
    }
    
    private func handleNetworkChange(_ isConnected: Bool) {
        isOfflineMode = !isConnected
        
        if isConnected {
            lastOnlineTime = Date()
            
            // Try to refresh session when back online
            if case .offline(let user) = sessionState {
                _Concurrency.Task {
                    await refreshOnlineSession(user)
                }
            }
        } else {
            // Switch to offline mode if authenticated
            if case .authenticated(let user) = sessionState {
                sessionState = .offline(user)
                print("📱 Switched to offline mode")
            }
        }
    }
    
    // MARK: - Authentication
    
    /// تسجيل دخول مع إمكانية العمل بدون إنترنت
    func signIn(email: String, password: String) async -> Bool {
        sessionState = .authenticating
        
        do {
            if networkMonitor.isConnected {
                // Online sign in
                let user = try await supabaseManager.signIn(email: email, password: password)
                
                // Cache user data for offline use
                await cacheUserData(user)
                await cacheSessionToken()
                
                sessionState = .authenticated(user)
                sessionExpiryTime = Date().addingTimeInterval(offlineSessionDuration)
                
                setupSessionRefresh()
                resetAutoLockTimer()
                
                print("✅ Online sign in successful")
                return true
                
            } else {
                // Offline sign in - check cached credentials
                if let cachedUser = await getCachedUserData(),
                   await validateOfflineCredentials(email: email, password: password) {
                    
                    sessionState = .offline(cachedUser)
                    sessionExpiryTime = Date().addingTimeInterval(offlineSessionDuration)
                    
                    resetAutoLockTimer()
                    
                    print("✅ Offline sign in successful")
                    return true
                } else {
                    sessionState = .unauthenticated
                    print("❌ Offline sign in failed - no cached credentials")
                    return false
                }
            }
            
        } catch {
            sessionState = .unauthenticated
            print("❌ Sign in failed: \(error)")
            return false
        }
    }
    
    /// تسجيل خروج
    func signOut() async {
        // Clear session timer
        sessionTimer?.invalidate()
        autoLockTimer?.invalidate()
        
        // Sign out from Supabase if online
        if networkMonitor.isConnected {
            try? await supabaseManager.signOut()
        }
        
        // Clear cached data
        await clearCachedData()
        
        sessionState = .unauthenticated
        sessionExpiryTime = nil
        
        print("✅ Signed out successfully")
    }
    
    /// تحديث الجلسة عند العودة للإنترنت
    private func refreshOnlineSession(_ user: User) async {
        do {
            // Try to refresh the session
            do {
                await supabaseManager.restoreSession()
                sessionState = .authenticated(user)
                setupSessionRefresh()
                print("✅ Session refreshed successfully")
            } catch {
                // Session expired - need to re-authenticate
                sessionState = .expired
                print("⚠️ Session expired - re-authentication required")
            }
        }
    }
    
    // MARK: - Biometric Authentication
    
    /// تمكين المصادقة البيومترية
    func enableBiometricAuth() async -> Bool {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            print("❌ Biometric authentication not available: \(error?.localizedDescription ?? "Unknown error")")
            return false
        }
        
        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: "Enable biometric authentication for TaskPlus"
            )
            
            if success {
                biometricAuthEnabled = true
                await saveBiometricSettings()
                print("✅ Biometric authentication enabled")
                return true
            }
        } catch {
            print("❌ Biometric authentication failed: \(error)")
        }
        
        return false
    }
    
    /// المصادقة البيومترية
    func authenticateWithBiometrics() async -> Bool {
        guard biometricAuthEnabled else { return false }
        
        let context = LAContext()
        
        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: "Authenticate to access TaskPlus"
            )
            
            if success {
                // Unlock the app
                if case .locked = sessionState,
                   let cachedUser = await getCachedUserData() {
                    sessionState = isOfflineMode ? .offline(cachedUser) : .authenticated(cachedUser)
                    resetAutoLockTimer()
                    print("✅ Biometric authentication successful")
                    return true
                }
            }
        } catch {
            print("❌ Biometric authentication failed: \(error)")
        }
        
        return false
    }
    
    // MARK: - Auto Lock
    
    /// إعداد مراقبة النشاط للقفل التلقائي
    private func setupActivityMonitoring() {
        // Monitor app state changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
    }
    
    @objc private func appDidBecomeActive() {
        updateLastActivity()
        
        // Check if session expired while app was inactive
        if let expiryTime = sessionExpiryTime, Date() > expiryTime {
            sessionState = .expired
        }
    }
    
    @objc private func appWillResignActive() {
        // Lock immediately when app goes to background if biometric is enabled
        if biometricAuthEnabled && sessionState.isAuthenticated {
            sessionState = .locked
        }
    }
    
    /// تحديث وقت آخر نشاط
    func updateLastActivity() {
        lastActivityTime = Date()
        resetAutoLockTimer()
    }
    
    /// إعادة تعيين مؤقت القفل التلقائي
    private func resetAutoLockTimer() {
        autoLockTimer?.invalidate()
        
        guard autoLockEnabled && sessionState.isAuthenticated else { return }
        
        autoLockTimer = Timer.scheduledTimer(withTimeInterval: autoLockTimeout, repeats: false) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                self?.lockSession()
            }
        }
    }
    
    /// قفل الجلسة
    private func lockSession() {
        guard sessionState.isAuthenticated else { return }
        
        sessionState = .locked
        print("🔒 Session locked due to inactivity")
    }
    
    // MARK: - Session Refresh
    
    /// إعداد تحديث الجلسة الدوري
    private func setupSessionRefresh() {
        sessionTimer?.invalidate()
        
        sessionTimer = Timer.scheduledTimer(withTimeInterval: sessionRefreshInterval, repeats: true) { [weak self] _ in
            _Concurrency.Task { @MainActor in
                await self?.refreshSessionIfNeeded()
            }
        }
    }
    
    /// تحديث الجلسة عند الحاجة
    private func refreshSessionIfNeeded() async {
        guard networkMonitor.isConnected,
              case .authenticated(let user) = sessionState else { return }
        
        do {
            do {
                await supabaseManager.restoreSession()
                // Session is still valid
                sessionExpiryTime = Date().addingTimeInterval(offlineSessionDuration)
                print("✅ Session refreshed")
            } catch {
                // Session expired
                sessionState = .expired
                print("⚠️ Session expired during refresh")
            }
        }
    }
    
    // MARK: - Data Persistence
    
    /// حفظ بيانات المستخدم في الكاش
    private func cacheUserData(_ user: User) async {
        do {
            let userData = try JSONEncoder().encode(user)
            let success = KeychainHelper.save(userData, service: keychainService, account: userDataKey)
            
            if success {
                print("✅ User data cached successfully")
            } else {
                print("❌ Failed to cache user data")
            }
        } catch {
            print("❌ Failed to encode user data: \(error)")
        }
    }
    
    /// جلب بيانات المستخدم من الكاش
    private func getCachedUserData() async -> User? {
        guard let userData = KeychainHelper.load(service: keychainService, account: userDataKey) else {
            return nil
        }
        
        do {
            let user = try JSONDecoder().decode(User.self, from: userData)
            return user
        } catch {
            print("❌ Failed to decode cached user data: \(error)")
            return nil
        }
    }
    
    /// حفظ رمز الجلسة
    private func cacheSessionToken() async {
        // This would cache the session token for offline validation
        // Implementation depends on Supabase session structure
        print("💾 Session token cached")
    }
    
    /// التحقق من بيانات الاعتماد بدون إنترنت
    private func validateOfflineCredentials(email: String, password: String) async -> Bool {
        // This would validate against cached credentials
        // For security, this should use proper hashing and encryption
        // For now, we'll assume validation is successful if user data exists
        return await getCachedUserData() != nil
    }
    
    /// مسح البيانات المخزنة
    private func clearCachedData() async {
        KeychainHelper.delete(service: keychainService, account: userDataKey)
        KeychainHelper.delete(service: keychainService, account: sessionTokenKey)
        print("🧹 Cached data cleared")
    }
    
    /// تحميل الجلسة المحفوظة
    private func loadPersistedSession() {
        _Concurrency.Task {
            if let cachedUser = await getCachedUserData() {
                if networkMonitor.isConnected {
                    // Try to restore online session
                    do {
                        await supabaseManager.restoreSession()
                        sessionState = .authenticated(cachedUser)
                        setupSessionRefresh()
                    } catch {
                        sessionState = .offline(cachedUser)
                    }
                } else {
                    sessionState = .offline(cachedUser)
                }
                
                resetAutoLockTimer()
                print("✅ Session restored from cache")
            }
        }
    }
    
    /// حفظ إعدادات البيومترية
    private func saveBiometricSettings() async {
        UserDefaults.standard.set(biometricAuthEnabled, forKey: biometricKey)
    }
    
    /// تحميل إعدادات البيومترية
    private func loadBiometricSettings() {
        biometricAuthEnabled = UserDefaults.standard.bool(forKey: biometricKey)
    }
    
    deinit {
        sessionTimer?.invalidate()
        autoLockTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Keychain Helper
class KeychainHelper {
    static func save(_ data: Data, service: String, account: String) -> Bool {
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecValueData as String: data
        ] as [String: Any]
        
        SecItemDelete(query as CFDictionary)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    static func load(service: String, account: String) -> Data? {
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ] as [String: Any]
        
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        if status == errSecSuccess {
            return dataTypeRef as? Data
        } else {
            return nil
        }
    }
    
    static func delete(service: String, account: String) -> Bool {
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account
        ] as [String: Any]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess
    }
}
