import Foundation

// MARK: - Motivation Icon Model
struct MotivationIcon: Identifiable, Codable {
    let id: UUID
    let iconEmoji: String
    let iconName: String
    let costPoints: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case iconEmoji = "icon_emoji"
        case iconName = "icon_name"
        case costPoints = "cost_points"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Database Motivation Icon (for Supabase)
struct DatabaseMotivationIcon: Codable {
    let id: UUID
    let iconEmoji: String
    let iconName: String
    let costPoints: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case iconEmoji = "icon_emoji"
        case iconName = "icon_name"
        case costPoints = "cost_points"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Sent Motivation Model
struct SentMotivation: Identifiable, Codable {
    let id: UUID
    let fromUserId: UUID
    let toUserId: UUID
    let iconId: UUID
    let pointsSpent: Int
    let createdAt: Date
    
    // Additional properties for UI
    var senderInfo: User?
    var receiverInfo: User?
    var iconInfo: MotivationIcon?
    
    enum CodingKeys: String, CodingKey {
        case id
        case fromUserId = "from_user_id"
        case toUserId = "to_user_id"
        case iconId = "icon_id"
        case pointsSpent = "points_spent"
        case createdAt = "created_at"
    }
    
    init(fromUserId: UUID, toUserId: UUID, iconId: UUID, pointsSpent: Int) {
        self.id = UUID()
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.iconId = iconId
        self.pointsSpent = pointsSpent
        self.createdAt = Date()
        self.senderInfo = nil
        self.receiverInfo = nil
        self.iconInfo = nil
    }
    
    // Init from database
    init(id: UUID, fromUserId: UUID, toUserId: UUID, iconId: UUID, pointsSpent: Int, createdAt: Date) {
        self.id = id
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.iconId = iconId
        self.pointsSpent = pointsSpent
        self.createdAt = createdAt
        self.senderInfo = nil
        self.receiverInfo = nil
        self.iconInfo = nil
    }
}

// MARK: - Database Sent Motivation (for Supabase)
struct DatabaseSentMotivation: Codable {
    let id: UUID
    let fromUserId: UUID
    let toUserId: UUID
    let iconId: UUID
    let pointsSpent: Int
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case fromUserId = "from_user_id"
        case toUserId = "to_user_id"
        case iconId = "icon_id"
        case pointsSpent = "points_spent"
        case createdAt = "created_at"
    }
}

// MARK: - Database Sent Motivation Insert (for Supabase)
struct DatabaseSentMotivationInsert: Codable {
    let fromUserId: UUID
    let toUserId: UUID
    let iconId: UUID
    let pointsSpent: Int
    
    enum CodingKeys: String, CodingKey {
        case fromUserId = "from_user_id"
        case toUserId = "to_user_id"
        case iconId = "icon_id"
        case pointsSpent = "points_spent"
    }
}
