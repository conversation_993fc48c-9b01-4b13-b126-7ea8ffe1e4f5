//
//  Group.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation

// MARK: - Group Model
struct Group: Identifiable, Codable, Hashable {
    var id: UUID  // تغيير إلى var لتمكين التحديث من قاعدة البيانات
    var name: String
    var description: String?
    var avatarURL: String?
    var isPrivate: Bool
    var ownerId: UUID
    var memberIds: [UUID]
    var groupCode: String  // رمز المجموعة الفريد
    var createdAt: Date
    var updatedAt: Date
    
    // Group Statistics
    var stats: GroupStats
    
    init(name: String, description: String? = nil, ownerId: UUID, isPrivate: Bool = false) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.avatarURL = nil
        self.isPrivate = isPrivate
        self.ownerId = ownerId
        self.memberIds = [ownerId] // Owner is automatically a member
        self.groupCode = Group.generateGroupCode() // توليد رمز فريد
        self.createdAt = Date()
        self.updatedAt = Date()
        self.stats = GroupStats()
    }
}

// MARK: - Group Statistics
struct GroupStats: Codable, Hashable {
    var totalTasksCreated: Int = 0
    var totalTasksCompleted: Int = 0
    var totalMembers: Int = 1 // Owner counts as 1
    var totalInvitationsSent: Int = 0
    var averageTaskCompletionTime: TimeInterval = 0
    
    var completionRate: Double {
        guard totalTasksCreated > 0 else { return 0.0 }
        return Double(totalTasksCompleted) / Double(totalTasksCreated)
    }
}

// MARK: - Group Member
struct GroupMember: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let groupId: UUID
    var role: MemberRole
    var joinedAt: Date
    var lastActiveAt: Date
    
    // Member Statistics within the group
    var stats: MemberStats
    
    init(userId: UUID, groupId: UUID, role: MemberRole = .member) {
        self.id = UUID()
        self.userId = userId
        self.groupId = groupId
        self.role = role
        self.joinedAt = Date()
        self.lastActiveAt = Date()
        self.stats = MemberStats()
    }
    
    enum MemberRole: String, Codable, CaseIterable {
        case owner = "owner"
        case member = "member"
        
        var displayName: String {
            switch self {
            case .owner: return "Owner"
            case .member: return "Member"
            }
        }
        
        var canCreateTasks: Bool {
            return self == .owner
        }
        
        var canAssignTasks: Bool {
            return self == .owner
        }
        
        var canManageMembers: Bool {
            return self == .owner
        }
        
        var canEditGroup: Bool {
            return self == .owner
        }
    }
}

// MARK: - Member Statistics
struct MemberStats: Codable {
    var tasksAssigned: Int = 0
    var tasksCompleted: Int = 0
    var averageCompletionTime: TimeInterval = 0
    var contributionScore: Double = 0.0
    
    var completionRate: Double {
        guard tasksAssigned > 0 else { return 0.0 }
        return Double(tasksCompleted) / Double(tasksAssigned)
    }
}

// MARK: - Group Invitation
struct GroupInvitation: Identifiable, Codable {
    var id: UUID  // تغيير إلى var لتمكين التحديث من قاعدة البيانات
    let groupId: UUID
    let invitedUserId: UUID
    let invitedByUserId: UUID
    var message: String?
    var status: InvitationStatus
    var createdAt: Date  // تغيير إلى var لتمكين التحديث من قاعدة البيانات
    var respondedAt: Date?

    init(groupId: UUID, invitedUserId: UUID, invitedByUserId: UUID, message: String? = nil) {
        self.id = UUID()
        self.groupId = groupId
        self.invitedUserId = invitedUserId
        self.invitedByUserId = invitedByUserId
        self.message = message
        self.status = .pending
        self.createdAt = Date()
        self.respondedAt = nil
    }
    
    enum InvitationStatus: String, Codable, CaseIterable {
        case pending = "pending"
        case accepted = "accepted"
        case declined = "declined"
        case expired = "expired"
        
        var displayName: String {
            switch self {
            case .pending: return "Pending"
            case .accepted: return "Accepted"
            case .declined: return "Declined"
            case .expired: return "Expired"
            }
        }
    }
}

// MARK: - Group Extensions
extension Group {
    /// توليد رمز مجموعة فريد
    static func generateGroupCode() -> String {
        let characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        let length = 8

        return String((0..<length).compactMap { _ in
            characters.randomElement()
        })
    }

    /// البحث عن مجموعة برمزها
    static func findByCode(_ code: String, in groups: [Group]) -> Group? {
        return groups.first { $0.groupCode.uppercased() == code.uppercased() }
    }

    // Check if user is the owner
    func isOwner(_ userId: UUID) -> Bool {
        return ownerId == userId
    }
    
    // Check if user is a member
    func isMember(_ userId: UUID) -> Bool {
        return memberIds.contains(userId)
    }
    
    // Get member count
    var memberCount: Int {
        return memberIds.count
    }
    
    // Add member
    mutating func addMember(_ userId: UUID) {
        if !memberIds.contains(userId) {
            memberIds.append(userId)
            stats.totalMembers = memberIds.count
            updatedAt = Date()
        }
    }
    
    // Remove member
    mutating func removeMember(_ userId: UUID) {
        if let index = memberIds.firstIndex(of: userId), userId != ownerId {
            memberIds.remove(at: index)
            stats.totalMembers = memberIds.count
            updatedAt = Date()
        }
    }
    
    // Update group statistics
    mutating func updateStats(taskCreated: Bool = false, taskCompleted: Bool = false, invitationSent: Bool = false) {
        if taskCreated {
            stats.totalTasksCreated += 1
        }
        
        if taskCompleted {
            stats.totalTasksCompleted += 1
        }
        
        if invitationSent {
            stats.totalInvitationsSent += 1
        }
        
        updatedAt = Date()
    }
    
    // Check if group matches search criteria
    func matchesSearch(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return name.lowercased().contains(lowercasedSearch) ||
               description?.lowercased().contains(lowercasedSearch) == true
    }
}

// MARK: - Group Invitation Extensions
extension GroupInvitation {
    // Check if invitation is expired (30 days)
    var isExpired: Bool {
        let expirationDate = Calendar.current.date(byAdding: .day, value: 30, to: createdAt) ?? createdAt
        return Date() > expirationDate && status == .pending
    }
    
    // Accept invitation
    mutating func accept() {
        status = .accepted
        respondedAt = Date()
    }
    
    // Decline invitation
    mutating func decline() {
        status = .declined
        respondedAt = Date()
    }
    
    // Mark as expired
    mutating func markExpired() {
        status = .expired
        respondedAt = Date()
    }
}

// MARK: - Sample Data
extension Group {
    static func sampleGroups(for userId: UUID) -> [Group] {
        [
            Group(name: "Marketing Team", description: "Collaborative space for marketing projects and campaigns", ownerId: userId),
            Group(name: "Study Group - CS101", description: "Computer Science study group for collaborative learning", ownerId: userId, isPrivate: true),
            Group(name: "Fitness Challenge", description: "30-day fitness challenge with friends", ownerId: userId),
            Group(name: "Book Club", description: "Monthly book reading and discussion group", ownerId: userId, isPrivate: true)
        ]
    }
}

extension GroupMember {
    static func sampleMembers(for groupId: UUID, userIds: [UUID]) -> [GroupMember] {
        userIds.enumerated().map { index, userId in
            GroupMember(userId: userId, groupId: groupId, role: index == 0 ? .owner : .member)
        }
    }
}

extension GroupInvitation {
    static func sampleInvitations(groupId: UUID, invitedUserId: UUID, invitedByUserId: UUID) -> [GroupInvitation] {
        [
            GroupInvitation(groupId: groupId, invitedUserId: invitedUserId, invitedByUserId: invitedByUserId, message: "Join our marketing team! We'd love to have you collaborate with us on upcoming projects."),
            GroupInvitation(groupId: groupId, invitedUserId: invitedUserId, invitedByUserId: invitedByUserId, message: "Hey! Want to join our study group? We meet twice a week to work on assignments together.")
        ]
    }
}
