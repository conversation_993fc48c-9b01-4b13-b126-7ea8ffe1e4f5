//
//  UserInfo.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation
import SwiftUI

// MARK: - User Info Model (Unified)
struct UnifiedUserInfo: Identifiable, Codable, Hashable {
    let id: UUID
    let username: String
    let email: String
    var fullName: String?
    var avatarURL: String?
    var bio: String?
    var points: Int
    var level: String
    var isOnline: Bool
    var lastSeen: Date?
    let createdAt: Date
    var updatedAt: Date
    
    // MARK: - Computed Properties
    
    /// الاسم المعروض (الاسم الكامل أو اسم المستخدم)
    var displayName: String {
        return fullName?.isEmpty == false ? fullName! : username
    }
    
    /// الأحرف الأولى للاسم
    var initials: String {
        if let fullName = fullName, !fullName.isEmpty {
            let components = fullName.split(separator: " ")
            if components.count >= 2 {
                return String(components[0].prefix(1) + components[1].prefix(1)).uppercased()
            } else {
                return String(fullName.prefix(2)).uppercased()
            }
        } else {
            return String(username.prefix(2)).uppercased()
        }
    }
    
    /// لون الخلفية للأفاتار
    var avatarBackgroundColor: LinearGradient {
        let colors = [
            [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
            [Color.blue, Color.purple],
            [Color.green, Color.teal],
            [Color.orange, Color.red],
            [Color.pink, Color.purple]
        ]
        
        let index = abs(username.hashValue) % colors.count
        return LinearGradient(
            colors: colors[index],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /// حالة الاتصال
    var connectionStatus: ConnectionStatus {
        if isOnline {
            return .online
        } else if let lastSeen = lastSeen {
            let timeSinceLastSeen = Date().timeIntervalSince(lastSeen)
            if timeSinceLastSeen < 300 { // 5 دقائق
                return .away
            } else if timeSinceLastSeen < 3600 { // ساعة
                return .recently
            } else {
                return .offline
            }
        } else {
            return .offline
        }
    }
    
    /// نص حالة الاتصال
    var connectionStatusText: String {
        switch connectionStatus {
        case .online:
            return "Online"
        case .away:
            return "Away"
        case .recently:
            return "Recently active"
        case .offline:
            if let lastSeen = lastSeen {
                return "Last seen \(lastSeen.timeAgoDisplay)"
            } else {
                return "Offline"
            }
        }
    }
    
    /// مستوى المستخدم بناءً على النقاط
    var calculatedLevel: String {
        switch points {
        case 0..<100:
            return "Beginner"
        case 100..<500:
            return "Intermediate"
        case 500..<1000:
            return "Advanced"
        case 1000..<2500:
            return "Expert"
        case 2500..<5000:
            return "Master"
        default:
            return "Legend"
        }
    }
    
    /// نسبة التقدم للمستوى التالي
    var levelProgress: Double {
        let levelRanges = [
            (0, 100),
            (100, 500),
            (500, 1000),
            (1000, 2500),
            (2500, 5000),
            (5000, Int.max)
        ]
        
        for (index, range) in levelRanges.enumerated() {
            if points >= range.0 && points < range.1 {
                let progress = Double(points - range.0) / Double(range.1 - range.0)
                return min(progress, 1.0)
            }
        }
        
        return 1.0 // Max level
    }
    
    // MARK: - Initializers
    
    init(id: UUID = UUID(), username: String, email: String, fullName: String? = nil, avatarURL: String? = nil) {
        self.id = id
        self.username = username
        self.email = email
        self.fullName = fullName
        self.avatarURL = avatarURL
        self.bio = nil
        self.points = 0
        self.level = "Beginner"
        self.isOnline = false
        self.lastSeen = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // MARK: - Methods
    
    /// تحديث النقاط
    mutating func addPoints(_ pointsToAdd: Int) {
        points += pointsToAdd
        level = calculatedLevel
        updatedAt = Date()
    }
    
    /// تحديث حالة الاتصال
    mutating func updateOnlineStatus(_ online: Bool) {
        isOnline = online
        if !online {
            lastSeen = Date()
        }
        updatedAt = Date()
    }
    
    /// تحديث الملف الشخصي
    mutating func updateProfile(fullName: String?, bio: String?, avatarURL: String?) {
        self.fullName = fullName
        self.bio = bio
        self.avatarURL = avatarURL
        self.updatedAt = Date()
    }
}

// MARK: - Connection Status
enum ConnectionStatus: String, CaseIterable {
    case online = "online"
    case away = "away"
    case recently = "recently"
    case offline = "offline"
    
    var color: Color {
        switch self {
        case .online:
            return .green
        case .away:
            return .yellow
        case .recently:
            return .orange
        case .offline:
            return .gray
        }
    }
    
    var icon: String {
        switch self {
        case .online:
            return "circle.fill"
        case .away:
            return "moon.fill"
        case .recently:
            return "clock.fill"
        case .offline:
            return "circle"
        }
    }
}

// MARK: - Sample Data
extension UnifiedUserInfo {
    static func sampleUsers() -> [UnifiedUserInfo] {
        [
            UnifiedUserInfo(
                username: "ahmed_dev",
                email: "<EMAIL>",
                fullName: "Ahmed Mohammed",
                avatarURL: nil
            ),
            UnifiedUserInfo(
                username: "sara_designer",
                email: "<EMAIL>",
                fullName: "Sara Ali",
                avatarURL: nil
            ),
            UnifiedUserInfo(
                username: "omar_pm",
                email: "<EMAIL>",
                fullName: "Omar Hassan",
                avatarURL: nil
            )
        ]
    }
}

// MARK: - Date Extension for Time Display
extension Date {
    var timeAgoDisplay: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: self, relativeTo: Date())
    }
}
