//
//  Friend.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation

// MARK: - Friend Model
struct Friend: Identifiable, Codable, Hashable {
    let id: UUID
    let userId: UUID
    let friendId: UUID
    let status: FriendshipStatus
    let createdAt: Date
    let updatedAt: Date
    
    // Friend's user info (populated from users table)
    var friendInfo: UserInfo?
    
    init(userId: UUID, friendId: UUID, status: FriendshipStatus = .pending) {
        self.id = UUID()
        self.userId = userId
        self.friendId = friendId
        self.status = status
        self.createdAt = Date()
        self.updatedAt = Date()
        self.friendInfo = nil
    }
    
    // MARK: - Friendship Status
    enum FriendshipStatus: String, Codable, CaseIterable {
        case pending = "pending"
        case accepted = "accepted"
        case blocked = "blocked"
        case declined = "declined"
        
        var displayName: String {
            switch self {
            case .pending:
                return "Pending"
            case .accepted:
                return "Friends"
            case .blocked:
                return "Blocked"
            case .declined:
                return "Declined"
            }
        }
        
        var icon: String {
            switch self {
            case .pending:
                return "clock"
            case .accepted:
                return "checkmark.circle.fill"
            case .blocked:
                return "xmark.circle.fill"
            case .declined:
                return "minus.circle.fill"
            }
        }
    }
}

// MARK: - User Info for Friends
struct UserInfo: Identifiable, Codable, Hashable {
    let id: UUID
    let username: String
    let email: String
    let displayName: String?
    let avatarUrl: String?
    let isOnline: Bool
    let lastSeen: Date?
    let createdAt: Date
    
    var name: String {
        return displayName ?? email.components(separatedBy: "@").first ?? "User"
    }
    
    var initials: String {
        let components = name.components(separatedBy: " ")
        if components.count >= 2 {
            return String(components[0].prefix(1) + components[1].prefix(1)).uppercased()
        } else {
            return String(name.prefix(2)).uppercased()
        }
    }
    
    var onlineStatus: String {
        if isOnline {
            return "Online"
        } else if let lastSeen = lastSeen {
            let formatter = RelativeDateTimeFormatter()
            formatter.unitsStyle = .abbreviated
            return formatter.localizedString(for: lastSeen, relativeTo: Date())
        } else {
            return "Offline"
        }
    }
}

// MARK: - Friend Request
struct FriendRequest: Identifiable, Codable, Hashable {
    let id: UUID
    let fromUserId: UUID
    let toUserId: UUID
    let message: String?
    let status: Friend.FriendshipStatus
    let createdAt: Date
    let updatedAt: Date
    
    // Sender's user info
    var senderInfo: UserInfo?
    
    init(fromUserId: UUID, toUserId: UUID, message: String? = nil) {
        self.id = UUID()
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.message = message
        self.status = .pending
        self.createdAt = Date()
        self.updatedAt = Date()
        self.senderInfo = nil
    }

    // Init from database
    init(id: UUID, fromUserId: UUID, toUserId: UUID, message: String?, status: Friend.FriendshipStatus, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.message = message
        self.status = status
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.senderInfo = nil
    }
    
    var isPending: Bool {
        return status == .pending
    }
    
    var isAccepted: Bool {
        return status == .accepted
    }
}

// MARK: - Sample Data
extension Friend {
    static func sampleFriends(for userId: UUID) -> [Friend] {
        let sampleUsers = UserInfo.sampleUsers()
        
        return [
            Friend(userId: userId, friendId: sampleUsers[0].id, status: .accepted),
            Friend(userId: userId, friendId: sampleUsers[1].id, status: .accepted),
            Friend(userId: userId, friendId: sampleUsers[2].id, status: .pending),
            Friend(userId: userId, friendId: sampleUsers[3].id, status: .accepted)
        ].map { friend in
            var updatedFriend = friend
            updatedFriend.friendInfo = sampleUsers.first { $0.id == friend.friendId }
            return updatedFriend
        }
    }
}

extension UserInfo {
    static func sampleUsers() -> [UserInfo] {
        return [
            UserInfo(
                id: UUID(),
                username: "ahmed_ali",
                email: "<EMAIL>",
                displayName: "Ahmed Ali",
                avatarUrl: nil,
                isOnline: true,
                lastSeen: Date(),
                createdAt: Date().addingTimeInterval(-86400 * 30)
            ),
            UserInfo(
                id: UUID(),
                username: "sara_mohammed",
                email: "<EMAIL>",
                displayName: "Sara Mohammed",
                avatarUrl: nil,
                isOnline: false,
                lastSeen: Date().addingTimeInterval(-3600 * 2),
                createdAt: Date().addingTimeInterval(-86400 * 15)
            ),
            UserInfo(
                id: UUID(),
                username: "omar_hassan",
                email: "<EMAIL>",
                displayName: "Omar Hassan",
                avatarUrl: nil,
                isOnline: false,
                lastSeen: Date().addingTimeInterval(-86400),
                createdAt: Date().addingTimeInterval(-86400 * 7)
            ),
            UserInfo(
                id: UUID(),
                username: "fatima_khalil",
                email: "<EMAIL>",
                displayName: "Fatima Khalil",
                avatarUrl: nil,
                isOnline: true,
                lastSeen: Date(),
                createdAt: Date().addingTimeInterval(-86400 * 5)
            ),
            UserInfo(
                id: UUID(),
                username: "mohammed_saleh",
                email: "<EMAIL>",
                displayName: "Mohammed Saleh",
                avatarUrl: nil,
                isOnline: false,
                lastSeen: Date().addingTimeInterval(-3600 * 6),
                createdAt: Date().addingTimeInterval(-86400 * 3)
            )
        ]
    }
}

extension FriendRequest {
    static func sampleRequests(for userId: UUID) -> [FriendRequest] {
        let sampleUsers = UserInfo.sampleUsers()
        
        return [
            FriendRequest(fromUserId: sampleUsers[0].id, toUserId: userId, message: "Hi! Let's be friends!"),
            FriendRequest(fromUserId: sampleUsers[1].id, toUserId: userId, message: nil),
            FriendRequest(fromUserId: sampleUsers[2].id, toUserId: userId, message: "Found you through mutual friends!")
        ].map { request in
            var updatedRequest = request
            updatedRequest.senderInfo = sampleUsers.first { $0.id == request.fromUserId }
            return updatedRequest
        }
    }
}
