//
//  GroupTask.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation

// MARK: - Group Task Model
struct GroupTask: Identifiable, Codable {
    var id: UUID
    var title: String
    var description: String?
    var dueDate: Date?
    var priority: Priority
    var status: GroupTaskStatus
    var tags: [String]
    var estimatedDuration: TimeInterval?
    
    // Group-specific properties
    let groupId: UUID        // للتوافق مع النظام الحالي
    let groupCode: String    // رمز المجموعة الجديد
    let createdById: UUID
    var taskType: GroupTaskType
    
    // Completion tracking
    var completedByMembers: [UUID: Date] = [:]  // [memberID: completionDate]
    var totalMembers: Int
    
    // Timestamps
    var createdAt: Date
    var updatedAt: Date
    
    // Enhanced features
    var isImportant: Bool
    var difficulty: Difficulty
    var categoryName: String?
    var reminderTime: Date?
    
    // MARK: - Initializers
    init(title: String,
         description: String? = nil,
         dueDate: Date? = nil,
         priority: Priority = .medium,
         groupId: UUID,
         groupCode: String,
         createdById: UUID,
         taskType: GroupTaskType,
         totalMembers: Int) {

        self.id = UUID()
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.priority = priority
        self.status = .active
        self.tags = []
        self.estimatedDuration = nil

        self.groupId = groupId
        self.groupCode = groupCode
        self.createdById = createdById
        self.taskType = taskType
        self.totalMembers = totalMembers
        
        self.createdAt = Date()
        self.updatedAt = Date()
        
        self.isImportant = false
        self.difficulty = .medium
        self.categoryName = nil
        self.reminderTime = nil
    }
    
    // MARK: - Task Types
    enum GroupTaskType: Codable {
        case groupTask              // مهمة جماعية (للجميع)
        case individualTask(UUID)   // مهمة فردية (لعضو محدد)
        
        var isGroupTask: Bool {
            switch self {
            case .groupTask:
                return true
            case .individualTask:
                return false
            }
        }
        
        var assigneeId: UUID? {
            switch self {
            case .groupTask:
                return nil
            case .individualTask(let userId):
                return userId
            }
        }
    }
    
    // MARK: - Task Status
    enum GroupTaskStatus: String, Codable, CaseIterable {
        case active = "active"
        case completed = "completed"
        case cancelled = "cancelled"
        case onHold = "on_hold"
        
        var displayName: String {
            switch self {
            case .active: return "Active"
            case .completed: return "Completed"
            case .cancelled: return "Cancelled"
            case .onHold: return "On Hold"
            }
        }
    }
    
    // MARK: - Priority
    enum Priority: String, Codable, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        
        var displayName: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            }
        }
        
        var sortOrder: Int {
            switch self {
            case .low: return 1
            case .medium: return 2
            case .high: return 3
            }
        }
    }
    
    // MARK: - Difficulty
    enum Difficulty: String, Codable, CaseIterable {
        case easy = "easy"
        case medium = "medium"
        case hard = "hard"
        case expert = "expert"
        
        var displayName: String {
            switch self {
            case .easy: return "Easy"
            case .medium: return "Medium"
            case .hard: return "Hard"
            case .expert: return "Expert"
            }
        }
        
        var color: String {
            switch self {
            case .easy: return "systemGreen"
            case .medium: return "systemBlue"
            case .hard: return "systemOrange"
            case .expert: return "systemPurple"
            }
        }
    }
}

// MARK: - Computed Properties
extension GroupTask {
    // للمهام الجماعية - التقدم
    var groupProgress: String {
        guard case .groupTask = taskType else { return "N/A" }
        return "\(completedByMembers.count)/\(totalMembers)"
    }
    
    // للمهام الفردية - حالة الإكمال
    var isIndividualCompleted: Bool {
        guard case .individualTask(let assigneeId) = taskType else { return false }
        return completedByMembers.keys.contains(assigneeId)
    }
    
    // هل المهمة مكتملة بالكامل؟
    var isFullyCompleted: Bool {
        switch taskType {
        case .groupTask:
            return completedByMembers.count == totalMembers
        case .individualTask(let assigneeId):
            return completedByMembers.keys.contains(assigneeId)
        }
    }
    
    // نسبة الإكمال (0.0 - 1.0)
    var completionPercentage: Double {
        switch taskType {
        case .groupTask:
            guard totalMembers > 0 else { return 0.0 }
            return Double(completedByMembers.count) / Double(totalMembers)
        case .individualTask(let assigneeId):
            return completedByMembers.keys.contains(assigneeId) ? 1.0 : 0.0
        }
    }
    
    // معلومات التعيين للعرض
    var assignmentInfo: String {
        switch taskType {
        case .groupTask:
            return "مهمة جماعية - للجميع"
        case .individualTask:
            return "مهمة فردية - مُعيَّن لعضو محدد"
        }
    }
    
    // هل المهمة متأخرة؟
    var isOverdue: Bool {
        guard let dueDate = dueDate, !isFullyCompleted else { return false }
        return dueDate < Date()
    }
    
    // الوقت المتبقي
    var timeRemaining: TimeInterval? {
        guard let dueDate = dueDate, !isFullyCompleted else { return nil }
        let remaining = dueDate.timeIntervalSinceNow
        return remaining > 0 ? remaining : nil
    }
}

// MARK: - Member Management
extension GroupTask {
    // إكمال المهمة لعضو محدد
    mutating func markCompleted(by memberId: UUID) -> Bool {
        // التحقق من صحة العضو
        switch taskType {
        case .groupTask:
            // في المهام الجماعية، أي عضو يمكنه الإكمال
            break
        case .individualTask(let assigneeId):
            // في المهام الفردية، فقط المُعيَّن له يمكنه الإكمال
            guard memberId == assigneeId else { return false }
        }
        
        // إضافة العضو لقائمة المكملين
        if !completedByMembers.keys.contains(memberId) {
            completedByMembers[memberId] = Date()
            updatedAt = Date()
            
            // تحديث الحالة إذا اكتملت بالكامل
            if isFullyCompleted {
                status = .completed
            }
            
            return true
        }
        
        return false
    }
    
    // إلغاء إكمال المهمة لعضو محدد
    mutating func markIncomplete(by memberId: UUID) -> Bool {
        if completedByMembers.keys.contains(memberId) {
            completedByMembers.removeValue(forKey: memberId)
            updatedAt = Date()
            
            // إعادة تفعيل المهمة إذا لم تعد مكتملة
            if status == .completed && !isFullyCompleted {
                status = .active
            }
            
            return true
        }
        
        return false
    }
    
    // الحصول على قائمة الأعضاء المكملين
    var completedMemberIds: [UUID] {
        return Array(completedByMembers.keys)
    }
    
    // الحصول على قائمة الأعضاء غير المكملين
    func incompleteMemberIds(allMemberIds: [UUID]) -> [UUID] {
        switch taskType {
        case .groupTask:
            return allMemberIds.filter { !completedByMembers.keys.contains($0) }
        case .individualTask(let assigneeId):
            return completedByMembers.keys.contains(assigneeId) ? [] : [assigneeId]
        }
    }
    
    // التحقق من إكمال عضو محدد
    func isCompleted(by memberId: UUID) -> Bool {
        return completedByMembers.keys.contains(memberId)
    }
    
    // تاريخ إكمال عضو محدد
    func completionDate(for memberId: UUID) -> Date? {
        return completedByMembers[memberId]
    }
}



// MARK: - Sample Data
extension GroupTask {
    static func sampleGroupTasks(groupId: UUID, groupCode: String, createdById: UUID, totalMembers: Int) -> [GroupTask] {
        [
            GroupTask(
                title: "مراجعة التقرير الشهري",
                description: "مراجعة وتحليل التقرير المالي للشهر الماضي",
                dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
                priority: .high,
                groupId: groupId,
                groupCode: groupCode,
                createdById: createdById,
                taskType: .groupTask,
                totalMembers: totalMembers
            ),
            GroupTask(
                title: "تحضير العرض التقديمي",
                description: "إعداد عرض تقديمي للاجتماع القادم",
                dueDate: Calendar.current.date(byAdding: .day, value: 5, to: Date()),
                priority: .medium,
                groupId: groupId,
                groupCode: groupCode,
                createdById: createdById,
                taskType: .individualTask(UUID()), // سيتم تعيينه لعضو محدد
                totalMembers: totalMembers
            ),
            GroupTask(
                title: "تحديث قاعدة البيانات",
                description: "تحديث وتنظيف قاعدة بيانات العملاء",
                dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()),
                priority: .medium,
                groupId: groupId,
                groupCode: groupCode,
                createdById: createdById,
                taskType: .groupTask,
                totalMembers: totalMembers
            )
        ]
    }
}
