//
//  User.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation

// MARK: - User Model
struct User: Identifiable, Codable {
    let id: UUID
    var username: String
    var displayName: String
    var email: String
    var bio: String?
    var avatarURL: String?
    var createdAt: Date
    var updatedAt: Date

    // Profile Change Tracking
    var lastDisplayNameChange: Date?
    var usernameChanged: Bool = false

    // Privacy Settings
    var privacySettings: PrivacySettings

    // Productivity Statistics
    var stats: UserStats
    
    init(username: String, displayName: String, email: String, bio: String? = nil) {
        self.id = UUID()
        self.username = username
        self.displayName = displayName
        self.email = email
        self.bio = bio
        self.avatarURL = nil
        self.createdAt = Date()
        self.updatedAt = Date()
        self.lastDisplayNameChange = nil
        self.usernameChanged = false
        self.privacySettings = PrivacySettings()
        self.stats = UserStats()
    }
}

// MARK: - Privacy Settings
struct PrivacySettings: Codable {
    var profileVisibility: ProfileVisibility = .friends
    var activitySharing: Bool = true
    var allowFriendRequests: Bool = true
    var showOnlineStatus: Bool = true
    var allowMotivationalMessages: Bool = true
    
    enum ProfileVisibility: String, Codable, CaseIterable {
        case everyone = "everyone"
        case friends = "friends"
        case privateProfile = "private"

        var displayName: String {
            switch self {
            case .everyone: return "Everyone"
            case .friends: return "Friends Only"
            case .privateProfile: return "Private"
            }
        }
    }
}

// MARK: - User Statistics
struct UserStats: Codable {
    var totalTasksCreated: Int = 0
    var totalTasksCompleted: Int = 0
    var currentStreak: Int = 0
    var longestStreak: Int = 0
    var totalGroupsJoined: Int = 0
    var totalGroupsOwned: Int = 0
    var totalFriends: Int = 0
    var totalMotivationalMessagesSent: Int = 0
    var totalMotivationalMessagesReceived: Int = 0
    
    var completionRate: Double {
        guard totalTasksCreated > 0 else { return 0.0 }
        return Double(totalTasksCompleted) / Double(totalTasksCreated)
    }
}

// MARK: - User Extensions
extension User {
    // Update user statistics
    mutating func updateStats(taskCompleted: Bool = false, groupJoined: Bool = false, groupCreated: Bool = false, friendAdded: Bool = false, messageSent: Bool = false, messageReceived: Bool = false) {
        if taskCompleted {
            stats.totalTasksCompleted += 1
            // Update streak logic would go here
        }
        
        if groupJoined {
            stats.totalGroupsJoined += 1
        }
        
        if groupCreated {
            stats.totalGroupsOwned += 1
        }
        
        if friendAdded {
            stats.totalFriends += 1
        }
        
        if messageSent {
            stats.totalMotivationalMessagesSent += 1
        }
        
        if messageReceived {
            stats.totalMotivationalMessagesReceived += 1
        }
        
        updatedAt = Date()
    }
    
    // Check if user can be found by search
    func matchesSearch(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return username.lowercased().contains(lowercasedSearch) ||
               displayName.lowercased().contains(lowercasedSearch) ||
               email.lowercased().contains(lowercasedSearch)
    }

    // MARK: - Profile Change Validation
    func canChangeDisplayName() -> Bool {
        guard let lastChange = lastDisplayNameChange else {
            return true // Never changed before
        }

        let fourteenDaysAgo = Calendar.current.date(byAdding: .day, value: -14, to: Date()) ?? Date()
        return lastChange < fourteenDaysAgo
    }

    func canChangeUsername() -> Bool {
        return !usernameChanged
    }

    func daysUntilDisplayNameChange() -> Int? {
        guard let lastChange = lastDisplayNameChange else {
            return nil // Can change now
        }

        let fourteenDaysFromLastChange = Calendar.current.date(byAdding: .day, value: 14, to: lastChange) ?? Date()
        let daysRemaining = Calendar.current.dateComponents([.day], from: Date(), to: fourteenDaysFromLastChange).day ?? 0

        return max(0, daysRemaining)
    }
}

// MARK: - Sample Data
extension User {
    static let sampleUser = User(
        username: "sarah_achiever",
        displayName: "Sarah Johnson",
        email: "<EMAIL>",
        bio: "Marketing professional passionate about productivity and helping others achieve their goals! 🌅"
    )
    
    static let sampleUsers: [User] = [
        User(username: "ahmed_leader", displayName: "Ahmed Hassan", email: "<EMAIL>", bio: "Team lead who loves coordinating projects"),
        User(username: "lisa_student", displayName: "Lisa Chen", email: "<EMAIL>", bio: "University student balancing studies and group projects"),
        User(username: "maria_motivator", displayName: "Maria Rodriguez", email: "<EMAIL>", bio: "Fitness enthusiast spreading positivity! 💪")
    ]
}
