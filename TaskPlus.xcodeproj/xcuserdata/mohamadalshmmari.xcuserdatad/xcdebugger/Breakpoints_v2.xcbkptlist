<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "E0015406-3604-4414-83CF-F11E2E88CDC1"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8FE08710-D552-4327-B26F-1408A70BF9FF"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "TaskPlus/Services/SettingsManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "118"
            endingLineNumber = "118"
            landmarkName = "setTaskReminders(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
